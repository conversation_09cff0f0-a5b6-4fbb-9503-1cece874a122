"use client";

import {Box, Card, CardContent, Chip, Divider, Grid, Paper, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';

// 模拟价值客户数据
const mockValueCustomers = [
    {
        id: 1,
        name: '王小明',
        username: 'xiaoming123',
        label: 'KOL',
        labelColor: 'primary',
        value: '平台活跃度高，粉丝10万+，互动率8%，常分享类似产品评测'
    },
    {
        id: 2,
        name: '李梅',
        username: 'meili_shop',
        label: '回购客户',
        labelColor: 'success',
        value: '已购买5次相关产品，评价详细专业，内容被引用率高'
    },
    {
        id: 3,
        name: '张华',
        username: 'zhangh_fashion',
        label: '意见领袖',
        labelColor: 'info',
        value: '行业达人，内容专业度高，评论引发大量讨论，转化率8.5%'
    },
    {
        id: 4,
        name: '刘强',
        username: 'liuqiang_tech',
        label: 'KOL',
        labelColor: 'primary',
        value: '科技博主，专业测评能力强，视频平均播放量5万+，评论转化率7.2%'
    },
    {
        id: 5,
        name: '陈静',
        username: 'jingchen88',
        label: '回购客户',
        labelColor: 'success',
        value: '连续订阅会员2年，产品使用心得详尽，社区影响力高'
    },
    {
        id: 6,
        name: '赵阳',
        username: 'zhaoyang_life',
        label: '意见领袖',
        labelColor: 'info',
        value: '生活方式博主，内容高质量，粉丝黏性强，带货能力突出'
    },
    {
        id: 7,
        name: '杨丽',
        username: 'yangli_beauty',
        label: 'KOL',
        labelColor: 'primary',
        value: '美妆达人，专业度高，评测客观公正，用户信任度高'
    },
    {
        id: 8,
        name: '周鑫',
        username: 'xinxin_zhou',
        label: '回购客户',
        labelColor: 'success',
        value: '产品早期使用者，持续购买3年，反馈详细有建设性'
    },
    {
        id: 9,
        name: '吴佳',
        username: 'wujia_digital',
        label: '意见领袖',
        labelColor: 'info',
        value: '数码测评专家，专业知识深厚，评论说服力强，受众精准'
    },
    {
        id: 10,
        name: '郑雯',
        username: 'zhengwen_mom',
        label: '种子用户',
        labelColor: 'warning',
        value: '亲子博主，社区活跃度高，分享内容质量高，互动率9.1%'
    },
    {
        id: 11,
        name: '黄磊',
        username: 'huanglei_pro',
        label: 'KOL',
        labelColor: 'primary',
        value: '专业摄影师，内容专业性强，作品展示效果突出，引流能力强'
    },
    {
        id: 12,
        name: '孙芳',
        username: 'fangfang_style',
        label: '回购客户',
        labelColor: 'success',
        value: '时尚行业从业者，专业角度点评，引导多次团购，影响力大'
    },
    {
        id: 13,
        name: '朱天',
        username: 'zhutian_fit',
        label: '种子用户',
        labelColor: 'warning',
        value: '健身教练，产品使用场景展示专业，转化率高，粉丝互动积极'
    },
    {
        id: 14,
        name: '马佳丽',
        username: 'jiali_overseas',
        label: '意见领袖',
        labelColor: 'info',
        value: '海外留学生，跨文化视角独特，对比评测详尽，受众群体精准'
    },
    {
        id: 15,
        name: '何文',
        username: 'hewen_business',
        label: '行业专家',
        labelColor: 'secondary',
        value: '商业顾问，分析专业深入，内容被多家平台转载，影响力广泛'
    }
];

const ValueCustomerAnalysis = ({ valueCustomers = mockValueCustomers }) => {
    const theme = useTheme();
    
    // 使用传入的数据或默认的mock数据
    const customers = valueCustomers;

    return (
        <>
            <Typography variant="h5" fontWeight="medium" sx={{mb: 3}}>
                价值客户分析
            </Typography>

            <Paper
                elevation={0}
                sx={{
                    p: 3,
                    borderRadius: 2,
                    mb: 4,
                    border: `1px solid ${theme.palette.divider}`
                }}
            >
                <Grid container spacing={3} sx={{width: '100%'}}>
                    {customers.map((customer) => (
                        <Grid key={customer.id} size={{xs: 12, md: 4}}>
                            <Card sx={{
                                borderRadius: 2, 
                                boxShadow: 'none', 
                                border: `1px solid ${theme.palette.divider}`,
                                height: '100%'
                            }}>
                                <CardContent>
                                    <Box sx={{display: 'flex', justifyContent: 'space-between', mb: 1}}>
                                        <Typography variant="h6" fontWeight="medium">{customer.name}</Typography>
                                        <Chip 
                                            label={customer.label} 
                                            size="small" 
                                            color={customer.labelColor}
                                        />
                                    </Box>
                                    <Typography variant="body2" color="text.secondary" sx={{mb: 2}}>
                                        @{customer.username}
                                    </Typography>
                                    <Divider sx={{my: 1.5}} />
                                    <Typography variant="body2" sx={{mt: 2}}>
                                        <b>价值点：</b> {customer.value}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Paper>
        </>
    );
};

export default ValueCustomerAnalysis; 