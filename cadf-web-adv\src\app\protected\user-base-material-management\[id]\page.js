"use client";

import {useCallback, useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {Avatar, Box, Button, Card, CardMedia, Chip, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Grid, IconButton, Paper, Typography} from '@mui/material';
import {ArrowLeft, ChevronLeft, ChevronRight, Clock, Maximize2, X, FileText} from 'lucide-react';
import {useTheme} from '@mui/material/styles';
import dayjs from 'dayjs';
import {useSnackbar} from 'notistack';

import {advUserBasicMaterialApi} from '@/api/adv-user-basic-material-api';

const getFetchStatusColor = (status) => {
    switch (status) {
        case '待爬取':
            return 'warning';
        case '爬取中':
            return 'info';
        case '已完成':
            return 'success';
        case '失败':
            return 'error';
        default:
            return 'default';
    }
};

export default function AiBasicMaterialDetail() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams();
    const {enqueueSnackbar} = useSnackbar();
    const {id: materialId} = params;

    const [material, setMaterial] = useState(null);
    const [loading, setLoading] = useState(true);
    const [imagePreviewDialogOpen, setImagePreviewDialogOpen] = useState(false);
    const [previewImages, setPreviewImages] = useState([]);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const [currentMainImageIndex, setCurrentMainImageIndex] = useState(0);

    const fetchMaterialDetail = useCallback(async () => {
        if (!materialId) {
            enqueueSnackbar('无效的素材ID', {variant: 'error'});
            setLoading(false);
            return;
        }

        setLoading(true);
        try {
            const response = await advUserBasicMaterialApi.getById(materialId);
            setMaterial(response);
        } catch (err) {
            console.error("获取素材详情失败:", err);
            enqueueSnackbar(err.message || '获取素材详情失败', {variant: 'error'});
            setMaterial(null);
        } finally {
            setLoading(false);
        }
    }, [materialId, enqueueSnackbar]);

    useEffect(() => {
        fetchMaterialDetail();
    }, [fetchMaterialDetail]);

    // 对图片进行排序
    const sortedImages = material?.images ? [...material.images].sort((a, b) => (a.order || 0) - (b.order || 0)) : [];

    const handleImagePreview = (event, images, startIndex = 0) => {
        event.stopPropagation();
        const previewUrls = images.map(img => img.signed_url || 'https://placehold.co/600x400?text=ImageNotFound');
        setPreviewImages(previewUrls);
        setSelectedImageIndex(startIndex);
        setImagePreviewDialogOpen(true);
    };

    const handleCloseImagePreview = () => {
        setImagePreviewDialogOpen(false);
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';
        return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss');
    };

    const handlePreviousImage = () => {
        setSelectedImageIndex((prevIndex) =>
            prevIndex === 0 ? previewImages.length - 1 : prevIndex - 1
        );
    };

    const handleNextImage = () => {
        setSelectedImageIndex((prevIndex) =>
            prevIndex === previewImages.length - 1 ? 0 : prevIndex + 1
        );
    };

    const handlePreviousMainImage = (event) => {
        event.stopPropagation(); // 防止触发图片点击预览
        setCurrentMainImageIndex((prevIndex) =>
            prevIndex === 0 ? sortedImages.length - 1 : prevIndex - 1
        );
    };

    const handleNextMainImage = (event) => {
        event.stopPropagation(); // 防止触发图片点击预览
        setCurrentMainImageIndex((prevIndex) =>
            prevIndex === sortedImages.length - 1 ? 0 : prevIndex + 1
        );
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 200px)'}}>
                <CircularProgress/>
            </Box>
        );
    }

    if (!material) {
        return (
            <Box sx={{textAlign: 'center', mt: 5, p: 3}}>
                <Typography variant="h6" color="error">无法加载素材详情</Typography>
                <Button
                    startIcon={<ArrowLeft size={18}/>}
                    onClick={() => router.back()}
                    sx={{mt: 2}}
                >
                    返回列表
                </Button>
            </Box>
        );
    }

    return (
        <Container maxWidth="lg" sx={{py: 3}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton onClick={() => router.back()} sx={{mr: 1}}>
                    <ArrowLeft/>
                </IconButton>
                <Typography variant="h5" component="h1" sx={{fontWeight: 600}}>
                    素材详情
                </Typography>
            </Box>

            <Grid container spacing={3} sx={{width: '100%', alignItems: 'stretch'}}>
                {/* 左侧图片展示区域 - 修改为大图带切换按钮 */}
                <Grid size={{ xs: 12, md: 7 }}>
                    <Paper elevation={0} sx={{position: 'relative', overflow: 'hidden', borderRadius: 2, bgcolor: 'background.paper', height: '100%'}}>
                        {sortedImages.length > 0 ? (
                            <Box sx={{position: 'relative', aspectRatio: '3/4', width: '100%'}}>
                                <CardMedia
                                    component="img"
                                    image={sortedImages[currentMainImageIndex]?.signed_url || 'https://placehold.co/800x800/EEEEEE/999999?text=Loading...'}
                                    alt={`素材图片 ${currentMainImageIndex + 1}`}
                                    sx={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'contain',
                                        bgcolor: 'background.paper',
                                        cursor: 'pointer',
                                        transition: theme.transitions.create(['opacity', 'transform'], {
                                            duration: theme.transitions.duration.standard,
                                            easing: theme.transitions.easing.easeInOut,
                                        }),
                                    }}
                                    onClick={(e) => handleImagePreview(e, sortedImages, currentMainImageIndex)}
                                />
                                {/* 放大预览按钮 */}
                                <IconButton
                                    onClick={(e) => handleImagePreview(e, sortedImages, currentMainImageIndex)}
                                    sx={{
                                        position: 'absolute', top: 8, right: 8,
                                        bgcolor: 'background.paper',
                                        color: 'text.primary',
                                        opacity: 0.7,
                                        transition: theme.transitions.create(['opacity', 'transform'], {
                                            duration: theme.transitions.duration.shorter,
                                            easing: theme.transitions.easing.easeInOut,
                                        }),
                                        '&:hover': {
                                            opacity: 1,
                                            transform: 'scale(1.1)',
                                            bgcolor: 'background.paper'
                                        }
                                    }}
                                    aria-label="maximize image"
                                >
                                    <Maximize2 size={20}/>
                                </IconButton>

                                {/* 图片计数器 */}
                                <Typography
                                    sx={{
                                        position: 'absolute',
                                        bottom: 8,
                                        left: '50%',
                                        transform: 'translateX(-50%)',
                                        color: 'common.white',
                                        bgcolor: 'grey.800',
                                        padding: '2px 8px',
                                        borderRadius: 1,
                                        fontSize: '0.75rem',
                                        transition: theme.transitions.create(['opacity'], {
                                            duration: theme.transitions.duration.shorter,
                                        }),
                                    }}
                                >
                                    {currentMainImageIndex + 1} / {sortedImages.length}
                                </Typography>

                                {/* 图片切换按钮 */}
                                {sortedImages.length > 1 && (
                                    <>
                                        <IconButton
                                            onClick={handlePreviousMainImage}
                                            sx={{
                                                position: 'absolute', top: '50%', left: 8, transform: 'translateY(-50%)',
                                                bgcolor: 'grey.600', color: 'common.white',
                                                opacity: 0.8,
                                                transition: theme.transitions.create(['opacity', 'transform', 'bgcolor'], {
                                                    duration: theme.transitions.duration.shorter,
                                                    easing: theme.transitions.easing.easeInOut,
                                                }),
                                                '&:hover': {
                                                    bgcolor: 'grey.800',
                                                    opacity: 1,
                                                    transform: 'translateY(-50%) scale(1.1)'
                                                }
                                            }}
                                            aria-label="previous image"
                                        >
                                            <ChevronLeft/>
                                        </IconButton>
                                        <IconButton
                                            onClick={handleNextMainImage}
                                            sx={{
                                                position: 'absolute', top: '50%', right: 8, transform: 'translateY(-50%)',
                                                bgcolor: 'grey.600', color: 'common.white',
                                                opacity: 0.8,
                                                transition: theme.transitions.create(['opacity', 'transform', 'bgcolor'], {
                                                    duration: theme.transitions.duration.shorter,
                                                    easing: theme.transitions.easing.easeInOut,
                                                }),
                                                '&:hover': {
                                                    bgcolor: 'grey.800',
                                                    opacity: 1,
                                                    transform: 'translateY(-50%) scale(1.1)'
                                                }
                                            }}
                                            aria-label="next image"
                                        >
                                            <ChevronRight/>
                                        </IconButton>
                                    </>
                                )}
                            </Box>
                        ) : (
                            <Box sx={{
                                aspectRatio: '3/4',
                                width: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: 'grey.50',
                                borderRadius: 1,
                                border: 1,
                                borderColor: 'grey.200'
                            }}>
                                <Typography color="text.secondary">暂无图片</Typography>
                            </Box>
                        )}
                    </Paper>
                </Grid>

                {/* 右侧内容展示区域 */}
                <Grid size={{ xs: 12, md: 5 }}>
                    <Paper elevation={0} sx={{p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column'}}>
                        {/* 标题和状态信息 */}
                        <Typography variant="h5" gutterBottom sx={{fontWeight: 600, mb: 2}}>
                            {material.title || '无标题'}
                        </Typography>

                        <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
                            <Avatar sx={{width: 36, height: 36, mr: 1, bgcolor: theme.palette.primary.main}}>
                                {material.platform ? material.platform.charAt(0) : 'M'}
                            </Avatar>
                            <Box>
                                <Typography variant="subtitle2">{material.platform || '未知平台'}</Typography>
                                <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                    <Clock size={14}/>
                                    <Typography variant="caption" color="text.secondary">
                                        {formatTimestamp(material.create_at)}
                                    </Typography>
                                </Box>
                            </Box>
                            <Box sx={{ml: 'auto'}}>
                                <Chip
                                    label={material.fetch_status || '未知'}
                                    size="small"
                                    color={getFetchStatusColor(material.fetch_status)}
                                />
                            </Box>
                        </Box>

                        <Divider sx={{my: 2}}/>

                        {/* 领域标签 */}
                        {material.domain && material.domain.length > 0 && (
                            <Box sx={{mb: 2}}>
                                <Typography variant="subtitle2" gutterBottom>领域:</Typography>
                                <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1}}>
                                    {material.domain.map((dom, index) => (
                                        <Chip key={`domain-${index}`} label={dom} size="small" variant="outlined"/>
                                    ))}
                                </Box>
                            </Box>
                        )}

                        {/* ID信息 */}
                        {/* 
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                            ID: {material.id_}
                        </Typography> 
                        */}

                        {/* 内容展示 */}
                        <Box sx={{display: 'flex', alignItems: 'center', gap: 1, mt: 3, mb: 1}}>
                            <FileText style={{color: theme.palette.text.secondary}}/>
                            <Typography variant="h6" component="div" sx={{fontWeight: 'medium'}}>
                                内容
                            </Typography>
                        </Box>
                        <Card
                            variant="outlined"
                            sx={{
                                p: 2,
                                bgcolor: 'background.default',
                                borderRadius: 1,
                                overflow: 'auto',
                                flex: 1
                            }}
                        >
                            <Typography variant="body1" sx={{whiteSpace: 'pre-wrap'}}>
                                {material.content || '无内容'}
                            </Typography>
                        </Card>

                        {/* 来源链接 */}
                        {/* 
                        {material.share_url && (
                            <Button
                                variant="outlined"
                                startIcon={<Share2 size={16} />}
                                component="a"
                                href={material.share_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{ mt: 3, width: '100%' }}
                                size="small"
                            >
                                查看原始链接
                            </Button>
                        )} 
                        */}
                    </Paper>
                </Grid>
            </Grid>

            {/* 图片预览对话框 */}
            <Dialog
                open={imagePreviewDialogOpen}
                onClose={handleCloseImagePreview}
                maxWidth="lg"
                fullWidth
            >
                <DialogTitle sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                    图片预览 ({selectedImageIndex + 1} / {previewImages.length})
                    <IconButton edge="end" color="inherit" onClick={handleCloseImagePreview} aria-label="close">
                        <X/>
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{position: 'relative', p: 0}}>
                    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '75vh'}}>
                        {previewImages.length > 0 ? (
                            <img
                                src={previewImages[selectedImageIndex]}
                                alt={`预览图片 ${selectedImageIndex + 1}`}
                                style={{
                                    maxHeight: '100%',
                                    maxWidth: '100%',
                                    objectFit: 'contain',
                                    display: 'block',
                                }}
                            />
                        ) : (
                            <Typography>没有可预览的图片。</Typography>
                        )}
                    </Box>

                    {/* Navigation Arrows */}
                    {previewImages.length > 1 && (
                        <>
                            <IconButton
                                onClick={handlePreviousImage}
                                sx={{
                                    position: 'absolute',
                                    top: '50%',
                                    left: theme.spacing(1),
                                    transform: 'translateY(-50%)',
                                    bgcolor: 'grey.600',
                                    color: 'common.white',
                                    opacity: 0.8,
                                    transition: theme.transitions.create(['opacity', 'transform', 'bgcolor'], {
                                        duration: theme.transitions.duration.shorter,
                                        easing: theme.transitions.easing.easeInOut,
                                    }),
                                    '&:hover': {
                                        bgcolor: 'grey.800',
                                        opacity: 1,
                                        transform: 'translateY(-50%) scale(1.1)'
                                    }
                                }}
                                aria-label="previous image"
                            >
                                <ChevronLeft/>
                            </IconButton>
                            <IconButton
                                onClick={handleNextImage}
                                sx={{
                                    position: 'absolute',
                                    top: '50%',
                                    right: theme.spacing(1),
                                    transform: 'translateY(-50%)',
                                    bgcolor: 'grey.600',
                                    color: 'common.white',
                                    opacity: 0.8,
                                    transition: theme.transitions.create(['opacity', 'transform', 'bgcolor'], {
                                        duration: theme.transitions.duration.shorter,
                                        easing: theme.transitions.easing.easeInOut,
                                    }),
                                    '&:hover': {
                                        bgcolor: 'grey.800',
                                        opacity: 1,
                                        transform: 'translateY(-50%) scale(1.1)'
                                    }
                                }}
                                aria-label="next image"
                            >
                                <ChevronRight/>
                            </IconButton>
                        </>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseImagePreview}>关闭</Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
} 