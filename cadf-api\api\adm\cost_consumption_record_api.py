from typing import Any, Dict

from models.models import CostConsumptionRecord
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from utils.cost_helper import _get_or_create_user_account


@register_handler('cost_consumption_record')
class CostConsumptionRecordApi:

    @auth_required(['admin'])
    async def get_user_balance(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """查询指定用户账户的余额"""
        user_id = data.get('user_id')

        # 根据用户ID获取或创建用户账户 (假设 _get_or_create_user_account 是异步的)
        user_account = await _get_or_create_user_account(user_id)

        return {
            'user_id': user_account.user_id,
            'balance': float(user_account.balance),  # 将余额转换为浮点数
        }

    @auth_required(['admin'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """查询消费记录列表 (可按用户账户ID筛选)"""
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        cost_user_account_id = data.get('cost_user_account_id')  # 筛选条件

        query_conditions = []
        if cost_user_account_id:
            query_conditions.append(CostConsumptionRecord.cost_user_account_id == cost_user_account_id)

        query = CostConsumptionRecord.find(*query_conditions)

        records = await query.sort(-CostConsumptionRecord.created_at).skip((page - 1) * page_size).limit(
            page_size).to_list()

        # 计算总条数
        total = await CostConsumptionRecord.find(*query_conditions).count()

        record_dicts = [record.to_dict() for record in records]
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=record_dicts
        )
