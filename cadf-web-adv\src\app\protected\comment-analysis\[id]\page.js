"use client";

import {useEffect, useState, useRef} from 'react';
import {Box, Breadcrumbs, Container, Typography, Tabs, Tab, Paper, Card, Chip} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {ArrowLef<PERSON>, Brain} from 'lucide-react';
import Link from 'next/link';
import {useParams, useRouter} from 'next/navigation';
import {useSnackbar} from 'notistack';

// 导入自定义组件
import ProductBasicInfo from '@/app/protected/comment-analysis/[id]/components/ProductBasicInfo';
import DataTrendAnalysis from '@/app/protected/comment-analysis/[id]/components/DataTrendAnalysis';
import WordCloudQuadrantAnalysis from '@/app/protected/comment-analysis/[id]/components/WordCloudQuadrantAnalysis';
import ValueCustomerAnalysis from '@/app/protected/comment-analysis/[id]/components/ValueCustomerAnalysis';

// 模拟产品列表数据 - 简化版，详细数据已移到各组件内
const mockProducts = [
    {
        id: 1,
        name: '简约风格双肩包',
        platform: '小红书'
    },
    {
        id: 2,
        name: '男士休闲运动鞋',
        platform: '抖音'
    }
];

export default function ProductCommentAnalysis() {
    const theme = useTheme();
    const params = useParams();
    const router = useRouter();
    const {enqueueSnackbar} = useSnackbar();
    const [activeTab, setActiveTab] = useState(0);
    
    // 直接获取产品数据
    const productId = parseInt(params.id);
    const product = mockProducts.find(p => p.id === productId) || mockProducts[0];
    
    // 创建分析部分的引用
    const dataTrendRef = useRef(null);
    const wordCloudRef = useRef(null);
    const valueCustomerRef = useRef(null);

    // 处理AI控评点击
    const handleAIControlClick = () => {
        router.push(`/protected/comment-analysis/${params.id}/public-opinion-report`);
    };

    // 处理导航切换
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
        
        // 滚动到相应部分
        const refs = [dataTrendRef, wordCloudRef, valueCustomerRef];
        if (refs[newValue]?.current) {
            // 计算导航栏高度并添加额外偏移量
            const navHeight = 64; // 估计的导航栏高度，可根据实际调整
            const targetPosition = refs[newValue].current.getBoundingClientRect().top + window.pageYOffset - navHeight;
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    };

    // 监听滚动位置更新导航状态
    useEffect(() => {
        const handleScroll = () => {
            if (!dataTrendRef.current || !wordCloudRef.current || !valueCustomerRef.current) return;
            
            const navHeight = 64; // 估计的导航栏高度，与上面保持一致
            const dataTrendPos = dataTrendRef.current.getBoundingClientRect().top - navHeight;
            const wordCloudPos = wordCloudRef.current.getBoundingClientRect().top - navHeight;
            const valueCustomerPos = valueCustomerRef.current.getBoundingClientRect().top - navHeight;
            
            const threshold = 100; // 设置一个阈值，元素必须足够接近视口顶部才被视为当前部分
            
            // 从上到下依次检查，哪个部分最靠近视口顶部
            if (dataTrendPos > -threshold && dataTrendPos < window.innerHeight / 2) {
                setActiveTab(0);
            } else if (wordCloudPos > -threshold && wordCloudPos < window.innerHeight / 2) {
                setActiveTab(1);
            } else if (valueCustomerPos > -threshold && valueCustomerPos < window.innerHeight / 2) {
                setActiveTab(2);
            }
        };
        
        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    return (
        <Container maxWidth="lg" sx={{py: 3}}>
            {/* 面包屑导航 */}
            <Breadcrumbs sx={{mb: 3}}>
                <Link href="/protected/comment-analysis" passHref style={{textDecoration: 'none', color: theme.palette.text.secondary}}>
                    <Typography variant="body2" display="flex" alignItems="center">
                        <ArrowLeft size={16} style={{marginRight: '4px'}}/>
                        产品列表
                    </Typography>
                </Link>
                <Typography variant="body2" color="text.primary">
                    {product.name}
                </Typography>
            </Breadcrumbs>

            {/* 基础信息部分 */}
            <ProductBasicInfo />
            
            {/* AI控评卡片 */}
            <Card 
                onClick={handleAIControlClick}
                sx={{ 
                    cursor: 'pointer',
                    mb: 3,
                    mt: 3,
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.primary.light}`,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease-in-out',
                    position: 'relative',
                    '&:hover': {
                        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
                        transform: 'translateY(-3px)',
                        '& .hover-effect': {
                            opacity: 1,
                            right: '16px',
                        }
                    },
                    '&:active': {
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.10)',
                    }
                }}
            >
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    alignItems: { xs: 'flex-start', md: 'center' },
                    p: { xs: 2, md: 3 },
                    background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${theme.palette.primary.light}15)`,
                    position: 'relative',
                    overflow: 'hidden',
                }}>
                    <Box sx={{
                        bgcolor: theme.palette.primary.main,
                        color: theme.palette.common.white,
                        borderRadius: 2,
                        p: 2,
                        mr: { xs: 0, md: 3 },
                        mb: { xs: 2, md: 0 },
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    }}>
                        <Brain size={32} />
                    </Box>

                    <Box sx={{ flex: 1 }}>
                        <Typography variant="h5" fontWeight="bold" color="primary.main" gutterBottom>
                            AI控评分析系统
                        </Typography>
                        <Typography variant="body1" color="text.primary" sx={{ mb: 1 }}>
                            智能管理产品评论舆情，提升品牌形象与转化率
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            • 三大方向：获客控评、舆情管理、成交促进
                            • 智能与手动控评模式双重保障
                            • 自动删除负面评论，提升产品好评率
                            • 数据可视化分析，优化营销策略
                        </Typography>
                        
                        <Chip 
                            label="立即进入AI控评系统" 
                            color="primary" 
                            size="medium"
                            variant="filled"
                            sx={{ 
                                fontWeight: 'bold',
                                '& .MuiChip-label': { px: 2, py: 0.5 },
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                            }} 
                        />
                    </Box>

                    <Box 
                        className="hover-effect"
                        sx={{
                            position: 'absolute',
                            right: { xs: '16px', md: '-40px' },
                            top: '50%',
                            transform: 'translateY(-50%)',
                            opacity: { xs: 1, md: 0 },
                            transition: 'all 0.3s ease-in-out',
                            display: 'flex',
                            alignItems: 'center',
                            bgcolor: 'primary.main',
                            color: 'common.white',
                            p: 1,
                            borderRadius: '50%',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                        }}
                    >
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </Box>
                </Box>
            </Card>
            
            {/* 分析导航栏 */}
            <Paper 
                elevation={0}
                sx={{ 
                    position: 'sticky', 
                    top: 0, 
                    zIndex: 10,
                    backgroundColor: theme.palette.background.paper,
                    mb: 3,
                    borderRadius: 1,
                    borderBottom: `1px solid ${theme.palette.divider}`
                }}
            >
                <Tabs 
                    value={activeTab} 
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    allowScrollButtonsMobile
                    sx={{
                        '& .MuiTab-root': {
                            minWidth: { xs: 'auto', md: 160 },
                            px: { xs: 1, md: 3 },
                            fontSize: { xs: '0.8rem', md: '0.875rem' }
                        },
                        '& .MuiTabs-scrollButtons': {
                            opacity: 1,
                            '&.Mui-disabled': {
                                opacity: 0.3
                            }
                        }
                    }}
                >
                    <Tab label="数据趋势分析" />
                    <Tab label="意图词云四象限分析" />
                    <Tab label="价值客户分析" />
                </Tabs>
            </Paper>

            {/* 数据趋势分析 */}
            <Box ref={dataTrendRef} sx={{ pt: 2, scrollMarginTop: '70px' }}>
                <DataTrendAnalysis />
            </Box>

            {/* 意图词云四象限分析 */}
            <Box ref={wordCloudRef} sx={{ pt: 2, scrollMarginTop: '70px' }}>
                <WordCloudQuadrantAnalysis />
            </Box>

            {/* 价值客户分析 */}
            <Box ref={valueCustomerRef} sx={{ pt: 2, scrollMarginTop: '70px' }}>
                <ValueCustomerAnalysis />
            </Box>
        </Container>
    );
} 