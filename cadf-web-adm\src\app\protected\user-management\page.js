"use client";

import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Stack,
} from "@mui/material";
import { Edit, Trash2, Plus } from "lucide-react";
import { userApi } from "@/api/user-api";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";

const roleOptions = [
  { value: "user", label: "普通用户" },
  { value: "creator", label: "流量主" },
  { value: "captain", label: "舰长" },
  { value: "advertiser", label: "广告主" },
  { value: "admin", label: "超级管理员" },
];

export default function UserManagement() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  // 获取所有用户
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const data = await userApi.queryAll();
      setUsers(data);
    } catch (error) {
      console.error("获取用户列表失败", error);
      dispatch(addAlert({ type: AlertType.ERROR, message: "获取用户列表失败" }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // 打开添加用户页面
  const handleAddUser = () => {
    router.push("/protected/user-management/create");
  };

  // 打开编辑用户页面
  const handleEditUser = (user) => {
    router.push(`/protected/user-management/edit/${user.id_}`);
  };

  // 打开删除确认对话框
  const handleOpenDeleteDialog = (user) => {
    setCurrentUser(user);
    setOpenDeleteDialog(true);
  };

  // 关闭删除确认对话框
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setCurrentUser(null);
  };

  // 删除用户
  const handleDeleteUser = async () => {
    try {
      await userApi.delete(currentUser.id_);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.DELETE }));
      handleCloseDeleteDialog();
      fetchUsers();
    } catch (error) {
      console.error("删除用户失败", error);
      dispatch(addAlert({ type: AlertType.ERROR, message: "删除用户失败" }));
    }
  };

  // 获取角色显示名称
  const getRoleLabel = (roleValue) => {
    const role = roleOptions.find((option) => option.value === roleValue);
    return role ? role.label : roleValue;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Typography variant="h4" component="h1">
          用户管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<Plus size={18} />}
          onClick={handleAddUser}
        >
          添加用户
        </Button>
      </Box>

      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <TableContainer sx={{ maxHeight: "calc(100vh - 200px)" }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>用户名</TableCell>
                <TableCell>角色</TableCell>
                <TableCell>创建时间</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    暂无数据
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow key={user.id_}>
                    <TableCell>{user.username}</TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        {user.roles?.map((role) => (
                          <Chip
                            key={role}
                            label={getRoleLabel(role)}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {user.create_at
                        ? new Date(user.create_at * 1000).toLocaleString()
                        : "-"}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="primary"
                        onClick={() => handleEditUser(user)}
                        size="small"
                      >
                        <Edit size={18} />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleOpenDeleteDialog(user)}
                        size="small"
                      >
                        <Trash2 size={18} />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除用户 "{currentUser?.username}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>取消</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
