"use client"

import {useEffect, useState} from 'react'
import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, CardContent, CardMedia, Chip, CircularProgress, Container, IconButton, LinearProgress, Paper, Stack, Typography} from '@mui/material'
import {useTheme} from '@mui/material/styles'
import {Eye, Plus, Trash2, RefreshCw} from 'lucide-react'
import {useRouter} from 'next/navigation'
import {advPromotionTaskApi} from '@/api/adv-promotion-task-api'
import ConfirmationDialog from '@/components/ConfirmationDialog';
import InfiniteScrollList from '@/core/components/InfiniteScrollList';

// Removed mock data

export default function ProductPromotion() {
    const theme = useTheme()
    const router = useRouter()
    const [page, setPage] = useState(1)
    const pageSize = 8

    // --- State for API data --- 
    const [tasks, setTasks] = useState([])
    const [totalCount, setTotalCount] = useState(0)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)
    const [hasMore, setHasMore] = useState(true)

    // --- State for deletion --- 
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [taskIdToDelete, setTaskIdToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false); // Optional: for disabling buttons during delete

    // --- Fetch Tasks Function --- 
    const fetchTasks = async (pageIndex = 1, append = false) => {
        if (!append) {
            setLoading(true)
        }
        setError(null)
        try {
            const response = await advPromotionTaskApi.queryPromotionTasks(
                pageIndex,
                pageSize,
            )
            if (response && response.results) {
                const newTasks = append ? [...tasks, ...response.results] : response.results
                setTasks(newTasks)
                setTotalCount(response.total || 0)
                setHasMore(newTasks.length < response.total)
            } else {
                if (!append) {
                    setTasks([])
                    setTotalCount(0)
                }
                setHasMore(false)
                console.warn("Unexpected API response structure:", response)
            }
        } catch (err) {
            console.error("Failed to fetch tasks:", err)
            const errorMsg = err?.response?.data?.message || err.message || "获取任务列表失败"
            setError(errorMsg)
            if (!append) {
                setTasks([])
                setTotalCount(0)
            }
        } finally {
            if (!append) {
                setLoading(false)
            }
        }
    }

    // --- useEffect to fetch initial data --- 
    useEffect(() => {
        fetchTasks(1, false)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    // --- Load More Function for Infinite Scroll --- 
    const loadMore = async () => {
        const nextPage = Math.floor(tasks.length / pageSize) + 1
        await fetchTasks(nextPage, true)
    }

    const handleCreateNewTask = () => {
        router.push('/protected/promotion-task/create') // Navigate to create page
    }

    const handleRefresh = async () => {
        setTasks([])
        setPage(1)
        await fetchTasks(1, false)
    }

    // Updated: Opens the confirmation dialog
    const handleDeleteTask = (taskId) => {
        setTaskIdToDelete(taskId);
        setOpenConfirmDialog(true);
    }

    // New: Executes the deletion after confirmation
    const confirmDeleteTask = async () => {
        if (!taskIdToDelete) return;

        setOpenConfirmDialog(false);
        setIsDeleting(true);
        setError(null); // Clear previous errors

        try {
            await advPromotionTaskApi.deletePromotionTask(taskIdToDelete);
            // Optionally add a success notification here if needed
            // Re-fetch tasks from the beginning
            setTasks([])
            setPage(1)
            await fetchTasks(1, false)
        } catch (err) {
            console.error("Failed to delete task:", err);
            const errorMsg = err?.response?.data?.message || err.message || "删除任务失败";
            setError(errorMsg); // Show error in the main error display area
            // Optionally add an error notification here
        } finally {
            setIsDeleting(false);
            setTaskIdToDelete(null);
        }
    };

    // Render function for Task Card
    const renderTask = (task, index) => {
        const completionRate = (task.taskCount && task.taskCount > 0)
            ? Math.round((task.completedCount / task.taskCount) * 100)
            : 0;
        
        // 任务状态判断
        const getTaskStatus = () => {
            if (completionRate === 100) return { label: '已完成', color: 'success' };
            if (completionRate === 0) return { label: '未开始', color: 'default' };
            return { label: '进行中', color: 'warning' };
        };
        
        const taskStatus = getTaskStatus();
        
        return (
            <Card 
                key={task._id}
                sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    borderRadius: 3,
                    border: `1px solid ${theme.palette.grey[200]}`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                    overflow: 'hidden',
                    position: 'relative',
                    backgroundColor: theme.palette.background.paper,
                    '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                        borderColor: theme.palette.primary.main,
                    }
                }}
                onClick={() => router.push(`/protected/promotion-task/${task._id}`)}
            >
                {/* 渐变状态指示条 */}
                <Box
                    sx={{
                        height: 3,
                        width: '100%',
                        backgroundColor: taskStatus.color === 'success'
                            ? theme.palette.success.main
                            : taskStatus.color === 'warning'
                                ? theme.palette.warning.main
                                : theme.palette.grey[400],
                    }}
                />
                
                <CardContent sx={{ flexGrow: 1, p: { xs: 2.5, md: 3 } }}>
                    {/* 产品名称与图片区域 */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2.5 }}>
                        {/* 产品图片 */}
                        {task.productImage && (
                            <Box
                                sx={{
                                    width: { xs: 60, md: 72 },
                                    height: { xs: 60, md: 72 },
                                    borderRadius: 2.5,
                                    overflow: 'hidden',
                                    border: `1px solid ${theme.palette.grey[200]}`,
                                    flexShrink: 0,
                                    bgcolor: theme.palette.grey[50],
                                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08)'
                                }}
                            >
                                <CardMedia
                                    component="img"
                                    image={task.productImage || '/api/placeholder/300/200'}
                                    alt={task.productName}
                                    sx={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover'
                                    }}
                                />
                            </Box>
                        )}
                        
                        {/* 产品名称和时间 */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Typography 
                                variant="h6" 
                                component="h3"
                                sx={{ 
                                    fontWeight: 600,
                                    fontSize: { xs: '1.05rem', md: '1.15rem' },
                                    lineHeight: 1.4,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    color: theme.palette.text.primary,
                                    mb: 1,
                                    letterSpacing: '-0.01em'
                                }}
                                title={task.productName}
                            >
                                {task.productName}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                <Chip
                                    label={task.platform || '未知'}
                                    size="small"
                                    sx={{ 
                                        bgcolor: 'rgba(0, 122, 255, 0.1)',
                                        color: 'primary.main',
                                        borderRadius: '8px',
                                        fontSize: '0.75rem',
                                        fontWeight: 500,
                                        height: 24,
                                        border: 'none'
                                    }}
                                />
                            </Box>
                            
                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem', opacity: 0.8 }}>
                                {task.createdAt ? task.createdAt : "N/A"}
                            </Typography>
                        </Box>
                    </Box>

                    {/* 任务统计区域 */}
                    <Box sx={{ mb: 2.5 }}>
                        <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'space-between',
                            mb: 2,
                            p: { xs: 1.5, md: 2 },
                            borderRadius: 2.5,
                            bgcolor: 'rgba(25, 118, 210, 0.04)',
                            border: `1px solid rgba(25, 118, 210, 0.08)`
                        }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: theme.palette.text.primary, fontSize: '0.9rem' }}>
                                任务进度统计
                            </Typography>
                            <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.primary.main, fontSize: { xs: '1.05rem', md: '1.15rem' } }}>
                                {completionRate}%
                            </Typography>
                        </Box>
                        
                        {/* 任务状态统计 */}
                        <Box sx={{ mb: 2.5 }}>
                            <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                mb: 1.5,
                                p: 1.5,
                                borderRadius: 2,
                                bgcolor: 'rgba(103, 58, 183, 0.05)',
                                border: `1px solid rgba(103, 58, 183, 0.1)`
                            }}>
                                <Box
                                    sx={{
                                        width: 12,
                                        height: 12,
                                        borderRadius: '50%',
                                        bgcolor: '#673ab7',
                                        mr: 1.5,
                                        boxShadow: '0 2px 4px rgba(103, 58, 183, 0.3)'
                                    }}
                                />
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#673ab7' }}>
                                    任务状态
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'nowrap' }}>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: 1.5, 
                                    borderRadius: 3,
                                    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(25, 118, 210, 0.05) 100%)',
                                    border: `1px solid rgba(25, 118, 210, 0.15)`,
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 4px 12px rgba(25, 118, 210, 0.15)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 800, color: theme.palette.primary.main, fontSize: '1.1rem', mb: 0.5 }}>
                                        {task.taskCount ?? 0}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        总数
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(76, 175, 80, 0.05)',
                                    border: `1px solid rgba(76, 175, 80, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(76, 175, 80, 0.08)',
                                        borderColor: 'rgba(76, 175, 80, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.success.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {task.completedCount ?? 0}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        已完成
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(255, 152, 0, 0.05)',
                                    border: `1px solid rgba(255, 152, 0, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(255, 152, 0, 0.08)',
                                        borderColor: 'rgba(255, 152, 0, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.warning.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {(task.taskCount ?? 0) - (task.completedCount ?? 0)}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        待完成
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </CardContent>
                
                {/* 操作按钮区域 */}
                <Box sx={{ 
                    p: { xs: 2.5, md: 3 }, 
                    pt: 0, 
                    display: 'flex', 
                    justifyContent: 'flex-end', 
                    gap: { xs: 1.5, md: 2 },
                    borderTop: `1px solid ${theme.palette.grey[200]}`,
                    flexDirection: { xs: 'column', sm: 'row' }
                }}>
                    <Button
                        size="medium"
                        variant="contained"
                        color="primary"
                        onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/protected/promotion-task/${task._id}`);
                        }}
                        startIcon={<Eye size={18}/>}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1.2,
                            fontSize: '0.9rem',
                            boxShadow: 'none',
                            backgroundColor: theme.palette.primary.main,
                            '&:hover': {
                                backgroundColor: theme.palette.primary.dark,
                                boxShadow: 'none',
                                transform: 'translateY(-1px)'
                            },
                            '&:active': {
                                transform: 'translateY(0px)'
                            },
                            transition: 'all 0.15s ease-in-out'
                        }}
                    >
                        查看详情
                    </Button>
                    <Button
                        size="medium"
                        variant="outlined"
                        color="error"
                        onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTask(task._id);
                        }}
                        disabled={isDeleting}
                        startIcon={<Trash2 size={18}/>}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1.2,
                            fontSize: '0.9rem',
                            borderWidth: '1px',
                            '&:hover': {
                                borderWidth: '1px',
                                backgroundColor: 'rgba(244, 67, 54, 0.04)',
                                transform: 'translateY(-1px)'
                            },
                            '&:active': {
                                transform: 'translateY(0px)'
                            },
                            '&:disabled': {
                                opacity: 0.5
                            },
                            transition: 'all 0.15s ease-in-out'
                        }}
                    >
                        删除
                    </Button>
                </Box>
            </Card>
        );
    };

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{mb: 4}}
            >
                <Typography variant="h4" fontWeight="bold">
                    推广任务中心
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                    <IconButton
                        color="primary"
                        onClick={handleRefresh}
                        disabled={loading}
                        title="刷新"
                        sx={{
                            borderRadius: 2,
                            border: `1px solid ${theme.palette.primary.main}`,
                            '&:hover': {
                                bgcolor: theme.palette.primary.light,
                                color: 'white'
                            }
                        }}
                    >
                        <RefreshCw size={20}/>
                    </IconButton>
                    <Button
                        variant="contained"
                        color="primary"
                        size="large"
                        startIcon={<Plus size={20}/>}
                        onClick={handleCreateNewTask}
                        sx={{
                            px: 4,
                            borderRadius: 2,
                            textTransform: "none",
                            fontWeight: "medium",
                        }}
                    >
                        创建新任务
                    </Button>
                </Stack>
            </Stack>


            <Paper
                elevation={0}
                sx={{
                    mb: 4,
                    overflow: "hidden",
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                }}
            >
                {/* Removed colored header bar for cleaner look */}
                {/* <Box sx={{ p: 2, bgcolor: ..., display: 'flex', ... }}> ... </Box> */}

                {loading ? (
                    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, minHeight: 300}}> {/* Increased minHeight */}
                        <CircularProgress/>
                    </Box>
                ) : error ? (
                    <Box sx={{p: 4}}>
                        <Alert severity="error">{error}</Alert>
                    </Box>
                ) : totalCount === 0 ? (
                    <Box sx={{p: 4, textAlign: "center", minHeight: 200, display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}> {/* Centered empty state */}
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            {"暂无任务记录"}
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<Plus size={16}/>}
                            onClick={handleCreateNewTask}
                            sx={{mt: 2}}
                        >
                            创建第一个任务
                        </Button>
                    </Box>
                ) : (
                    <Box sx={{p: {xs: 1, sm: 2, md: 3}}}>
                        <InfiniteScrollList
                            items={tasks}
                            renderItem={renderTask}
                            loadMore={loadMore}
                            hasMore={hasMore}
                            gridColumns={{
                                xs: 12,
                                sm: 12,
                                md: 6,
                                lg: 6
                            }}
                        />
                    </Box>
                )}
            </Paper>

            {/* Add Confirmation Dialog */}
            <ConfirmationDialog
                open={openConfirmDialog}
                onClose={() => setOpenConfirmDialog(false)} // Close without action
                onConfirm={confirmDeleteTask} // Call confirm function
                title="确认删除任务"
                description={`您确定要删除此推广任务吗？此操作将标记任务为已删除，但可能不会立即从所有视图中移除。`}
                confirmText="确认删除"
                cancelText="取消"
                confirmButtonColor="error" // Use error color for delete confirmation
            />

            {/* Footer Info - Optional */}
            {/* <Box sx={{display: "flex", justifyContent: "flex-end"}}> ... </Box> */}
        </Container>
    )
}
