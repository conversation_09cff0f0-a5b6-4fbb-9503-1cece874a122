import asyncio
import random
import re
from datetime import datetime, timedelta
from typing import Literal, Optional

from playwright.async_api import Locator

from omni.log.log import olog


async def smooth_scroll_down(element: Locator):
    """
    将鼠标移动到指定元素上，然后使用滚轮平滑向下滚动

    :param element: 要滚动的元素
    """
    # 将鼠标移动到元素中心
    await element.hover()
    
    # 使用鼠标滚轮随机滚动4-7次
    page = element.page
    scroll_count = random.randint(4, 7)
    for _ in range(scroll_count):
        await page.mouse.wheel(0, 300)
        await asyncio.sleep(0.1)


def parse_xhs_relative_time(time_str: str) -> int:
    """
    解析小红书的相对时间字符串 (例如："5分钟前", "昨天 14:05") 并转换为 Unix 时间戳。

    Args:
        time_str: 小红书笔记或评论区显示的时间字符串。

    Returns:
        解析成功则返回 int 型的 Unix 时间戳，否则会抛出异常。
    """
    now = datetime.now()
    time_str_original = time_str
    time_str_cleaned = re.sub(r'^(编辑于|发布于)\s+', '', time_str.strip()).strip()

    try:
        if "刚刚" in time_str_cleaned:
            dt = now
        elif "分钟前" in time_str_cleaned:
            match = re.search(r'(\d+)', time_str_cleaned)
            minutes = int(match.group(1))
            dt = now - timedelta(minutes=minutes)
        elif "小时前" in time_str_cleaned:
            match = re.search(r'(\d+)', time_str_cleaned)
            hours = int(match.group(1))
            dt = now - timedelta(hours=hours)
        elif "天前" in time_str_cleaned:
            match = re.search(r'(\d+)', time_str_cleaned)
            days = int(match.group(1))
            dt = now - timedelta(days=days)
        elif time_str_cleaned.startswith("今天"):
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                dt = datetime.strptime(f"{now.strftime('%Y-%m-%d')} {time_part}", "%Y-%m-%d %H:%M")
            else:
                raise ValueError(f"无法从 '今天' 格式提取时间: {time_str_original}")
        elif time_str_cleaned.startswith("昨天"):
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                yesterday = now - timedelta(days=1)
                dt = datetime.strptime(f"{yesterday.strftime('%Y-%m-%d')} {time_part}",
                                       "%Y-%m-%d %H:%M")
            else:
                raise ValueError(f"无法从 '昨天' 格式提取时间: {time_str_original}")
        elif time_str_cleaned.startswith("前天"):
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            match = re.search(r'(\d{1,2}:\d{2})$', time_str_no_suffix)
            if match:
                time_part = match.group(1)
                day_before = now - timedelta(days=2)
                dt = datetime.strptime(f"{day_before.strftime('%Y-%m-%d')} {time_part}",
                                       "%Y-%m-%d %H:%M")
            else:
                raise ValueError(f"无法从 '前天' 格式提取时间: {time_str_original}")
        else:
            time_str_no_suffix = re.sub(r'\s+([^\s\d:]+)$', '', time_str_cleaned).strip()
            if re.match(r"\d{1,2}-\d{1,2}", time_str_no_suffix):
                date_part = time_str_no_suffix
                time_part = "00:00"
                if ' ' in time_str_no_suffix:
                    parts = time_str_no_suffix.split(' ', 1)
                    date_part = parts[0]
                    if re.match(r'\d{1,2}:\d{2}', parts[1]):
                        time_part = parts[1]
                parsed_date = datetime.strptime(f"{now.year}-{date_part} {time_part}", "%Y-%m-%d %H:%M")
                dt = parsed_date.replace(year=now.year - 1) if parsed_date.date() > now.date() else parsed_date
            elif re.match(r"\d{4}-\d{1,2}-\d{1,2}", time_str_no_suffix):
                try:
                    dt = datetime.strptime(time_str_no_suffix, "%Y-%m-%d %H:%M")
                except ValueError:
                    dt = datetime.strptime(time_str_no_suffix, "%Y-%m-%d")
            else:
                raise ValueError(f"无法解析时间格式: {time_str_original}")
        return int(dt.timestamp())
    except Exception as e:
        olog.error(f"解析时间字符串 '{time_str_original}' 时出错: {e}")
        raise


def extract_url_from_text(text: str, platform_name: Literal["小红书"]) -> Optional[str]:
    """
    根据平台名称从分享文本中提取对应的 URL。

    Args:
        text: 分享文本内容。
        platform_name: 平台名称，目前支持 "小红书"。

    Returns:
        提取到的 URL 字符串，如果无法匹配则返回 None。
    """
    if not isinstance(text, str):
        return None

    if platform_name == "小红书":
        url_match = re.search(r"(https?://(?:www\.xiaohongshu\.com|xhslink\.com)/[^\s，]+)", text)
        return url_match.group(0) if url_match else None

    return None
