"use client";

import React, { useState } from "react";
import { Box, Button, TextField, Typography, Alert, Paper, Stack, InputAdornment } from "@mui/material";
import { UserPlus, Eye, EyeOff, Lock, User } from "lucide-react";
import { userApi } from "@/api/user-api";
import { useRouter } from "next/navigation";
import { DASHBOARD_PATH, PROJECT_DESCRIPTION, PROJECT_NAME } from "@/config";
import { Fade, useMediaQuery, useTheme } from "@mui/material";

// 注册页面组件
export default function RegisterPage() {
  // 表单状态
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPwd, setConfirmPwd] = useState("");
  const [crewInviteCode, setCrewInviteCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [successMsg, setSuccessMsg] = useState("");
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // 注册处理函数
  const handleRegister = async (e) => {
    e.preventDefault();
    setErrorMsg("");
    setSuccessMsg("");
    if (!username || !password) {
      setErrorMsg("用户名和密码不能为空");
      return;
    }
    if (password !== confirmPwd) {
      setErrorMsg("两次输入的密码不一致");
      return;
    }
    setLoading(true);
    try {
      // 调用注册API
      const res = await userApi.register(username, password, crewInviteCode);
      setSuccessMsg(res.message || "注册成功");
      // 1秒后跳转到登录页
      setTimeout(() => {
        router.push("/public/login");
      }, 1000);
    } catch (err) {
      setErrorMsg(err?.message || "注册失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      component="main"
      sx={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: theme.palette.background.default,
        backgroundImage: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.grey[100]} 100%)`,
        overflow: "hidden",
        px: {xs: 2, sm: 3, md: 0}
      }}
    >
      {/* 左侧装饰区域 - 仅在非移动设备上显示 */}
      <Box
        sx={{
          display: {xs: "none", md: "flex"},
          width: "50%",
          backgroundColor: theme.palette.primary.main,
          backgroundImage: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          justifyContent: "center",
          alignItems: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            position: "relative",
            zIndex: 2,
            p: 6,
            color: theme.palette.primary.contrastText,
            textAlign: "center",
            maxWidth: "600px",
          }}
        >
          <Typography
            variant="h2"
            component="h1"
            sx={{
              fontWeight: 700,
              mb: 3,
              letterSpacing: "-0.5px",
              textShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
              fontSize: {xs: "2.5rem", sm: "3rem", md: "3.5rem"}
            }}
          >
            {PROJECT_NAME}
          </Typography>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 500,
              opacity: 0.9,
              mb: 6,
              letterSpacing: "0.5px",
              position: "relative",
              "&::after": {
                content: '""',
                position: "absolute",
                bottom: "-20px",
                left: "50%",
                transform: "translateX(-50%)",
                width: "80px",
                height: "4px",
                borderRadius: "2px",
                backgroundColor: theme.palette.primary.light,
                opacity: 0.7
              }
            }}
          >
            {PROJECT_DESCRIPTION}
          </Typography>
        </Box>
        {/* 装饰性图形元素 */}
        <Box
          sx={{
            position: "absolute",
            bottom: "-10%",
            right: "-5%",
            width: "300px",
            height: "300px",
            borderRadius: "50%",
            backgroundColor: "rgba(255, 255, 255, 0.1)",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            top: "10%",
            left: "-5%",
            width: "200px",
            height: "200px",
            borderRadius: "50%",
            backgroundColor: "rgba(255, 255, 255, 0.05)",
          }}
        />
      </Box>
      {/* 注册区域 */}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          p: {xs: 2, sm: 4},
        }}
      >
        {/* 移动设备上的品牌标识 */}
        <Fade in={true} timeout={800}>
          <Box
            sx={{
              display: {xs: "flex", md: "none"},
              flexDirection: "column",
              alignItems: "center",
              mb: 4,
              textAlign: "center"
            }}
          >
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 700,
                color: theme.palette.primary.light,
                mb: 1
              }}
            >
              {PROJECT_NAME}
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: theme.palette.text.secondary,
                fontWeight: 500,
                position: "relative",
                pb: 2,
                "&::after": {
                  content: '""',
                  position: "absolute",
                  bottom: 0,
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "40px",
                  height: "3px",
                  borderRadius: "1.5px",
                  backgroundColor: theme.palette.primary.light,
                }
              }}
            >
              {PROJECT_DESCRIPTION}
            </Typography>
          </Box>
        </Fade>
        {/* 注册卡片 */}
        <Fade in={true} timeout={1000}>
          <Paper
            elevation={3}
            sx={{
              width: "100%",
              maxWidth: "400px",
              borderRadius: "16px",
              p: {xs: 3, sm: 4},
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography
              variant="h5"
              component="h2"
              sx={{
                mb: 3,
                fontWeight: 600,
                color: theme.palette.text.primary
              }}
            >
              用户注册
            </Typography>
            <form onSubmit={handleRegister}>
              <Stack spacing={3}>
                {errorMsg && (
                  <Alert severity="error" sx={{borderRadius: "8px"}}>
                    {errorMsg}
                  </Alert>
                )}
                {successMsg && (
                  <Alert severity="success" sx={{borderRadius: "8px"}}>
                    {successMsg}
                  </Alert>
                )}
                <TextField
                  fullWidth
                  required
                  id="username"
                  name="username"
                  label="用户名"
                  variant="outlined"
                  value={username}
                  onChange={(e) => { setUsername(e.target.value); setErrorMsg(""); setSuccessMsg(""); }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <User size={20}/>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    borderRadius: "8px",
                  }}
                />
                <TextField
                  fullWidth
                  required
                  id="password"
                  name="password"
                  label="密码"
                  type="password"
                  variant="outlined"
                  value={password}
                  onChange={(e) => { setPassword(e.target.value); setErrorMsg(""); setSuccessMsg(""); }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock size={20}/>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    borderRadius: "8px",
                  }}
                />
                <TextField
                  fullWidth
                  required
                  id="confirmPwd"
                  name="confirmPwd"
                  label="确认密码"
                  type="password"
                  variant="outlined"
                  value={confirmPwd}
                  onChange={(e) => { setConfirmPwd(e.target.value); setErrorMsg(""); setSuccessMsg(""); }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock size={20}/>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    borderRadius: "8px",
                  }}
                />
                <TextField
                  fullWidth
                  id="inviteCode"
                  name="inviteCode"
                  label="舰队邀请码 (可选)"
                  variant="outlined"
                  value={crewInviteCode}
                  onChange={(e) => setCrewInviteCode(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock size={20}/>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    borderRadius: "8px",
                  }}
                />
                <Button
                  fullWidth
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled={loading}
                  sx={{
                    py: 1.5,
                    mt: 1,
                    fontWeight: 500,
                    fontSize: "1rem",
                  }}
                >
                  {loading ? "注册中..." : "注册"}
                </Button>
              </Stack>
            </form>
            <Box sx={{mt: 3, textAlign: "center"}}>
              <Typography variant="body2" color="text.secondary">
                {new Date().getFullYear()} {PROJECT_NAME}. 保留所有权利
              </Typography>
              <Button
                color="secondary"
                sx={{ mt: 2 }}
                onClick={() => router.push("/public/login")}
              >
                已有账号？去登录
              </Button>
            </Box>
          </Paper>
        </Fade>
      </Box>
    </Box>
  );
}
