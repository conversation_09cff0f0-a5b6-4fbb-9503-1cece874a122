import api from "@/core/api/api";

export const advertiserManagementApi = {
  /**
   * 创建广告主
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<{message: string, user_id: string, username: string}>} 创建结果
   */
  create: async (username, password) => {
    return await api({
      resource: "advertiser",
      method_name: "create",
      username,
      password,
    });
  },

  /**
   * 查询广告主列表
   * @param {number} page - 页码
   * @param {number} page_size - 每页数量
   * @param {string} search - 搜索关键词
   * @returns {Promise<{advertisers: Array<{id_: string, username: string, create_at: number, balance: number}>, total: number, page: number, page_size: number}>} 广告主列表
   */
  queryList: async (page = 1, page_size = 10, search = "") => {
    return await api({
      resource: "advertiser",
      method_name: "query_list",
      page,
      page_size,
      search,
    });
  },

  /**
   * 查询广告主详情
   * @param {string} target_user_id - 目标用户ID
   * @returns {Promise<Object>} 广告主详情
   */
  queryDetail: async (target_user_id) => {
    return await api({
      resource: "advertiser",
      method_name: "query_detail",
      target_user_id,
    });
  },

  /**
   * 更新广告主信息
   * @param {string} target_user_id - 目标用户ID
   * @param {string} username - 用户名
   * @param {string} password - 密码（可选）
   * @returns {Promise<{message: string}>} 更新结果
   */
  update: async (target_user_id, username, password = null) => {
    const params = {
      resource: "advertiser",
      method_name: "update",
      target_user_id,
      username,
    };
    if (password) {
      params.password = password;
    }
    return await api(params);
  },

  /**
   * 删除广告主
   * @param {string} target_user_id - 目标用户ID
   * @returns {Promise<{message: string}>} 删除结果
   */
  delete: async (target_user_id) => {
    return await api({
      resource: "advertiser",
      method_name: "delete",
      target_user_id,
    });
  },
};