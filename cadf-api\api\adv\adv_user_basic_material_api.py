import time

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import UserBasicMaterial
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set


@register_handler('adv_user_basic_material_api')
class AdvUserBasicMaterialApi:
    @auth_required(['admin', 'advertiser'])
    async def create(self, data):
        user_id = data.get('user_id')
        share_url = data.get('share_url')
        platform = data.get('platform')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not share_url:
            raise MException("share_url 不能为空")
        if not platform:
            raise MException("platform 不能为空")

        # 验证platform值是否有效
        valid_platforms = ['小红书', '抖音', '公众号', '今日头条', '知乎']
        if platform not in valid_platforms:
            raise MException(f"不支持的平台: {platform}，支持的平台: {', '.join(valid_platforms)}")

        material = UserBasicMaterial(
            user_id=user_id,
            platform=platform,
            share_url=share_url,
            create_at=int(time.time()),
            is_deleted=False,
            fetch_status='待爬取'
        )
        await material.insert()

        await publish_messages_to_redis_set(RedisKeyConfig.XHS_NOTE_CONTENT_SPIDER_SET, {"id_": str(material.id)})

    @auth_required(['admin', 'advertiser'])
    async def delete(self, data):
        _id = data.get('_id')

        # 输入验证
        if not _id:
            raise MException("_id 不能为空")
        await UserBasicMaterial.find_one(UserBasicMaterial.id == PydanticObjectId(_id)).update(
            {"$set": {"is_deleted": True}}
        )

    @auth_required(['admin', 'advertiser'])
    async def query_one(self, data):
        _id = data.get('_id')
        user_id = data.get('user_id')

        material = await UserBasicMaterial.find_one(
            UserBasicMaterial.id == PydanticObjectId(_id),
            UserBasicMaterial.user_id == user_id,
            UserBasicMaterial.is_deleted != True
        )

        if not material:
            return None

        if material.images:
            for img in material.images:
                img.signed_url = await oss_client.signed_get_url(img.oss_key) if img.oss_key else None

        return material.to_dict()

    @auth_required(['admin', 'advertiser'])
    async def query_all(self, data):
        search_query = data.get('search')
        page = data.get('page', 1)
        page_size = data.get('page_size', 5)
        user_id = data.get('user_id')
        platform = data.get('platform')

        match_stage = {'user_id': user_id, 'is_deleted': {'$ne': True}}

        if search_query:
            match_stage['title'] = {'$regex': search_query, '$options': 'i'}
        if platform:
            match_stage['platform'] = platform

        pipeline = [
            {'$match': match_stage},
            {'$sort': {'create_at': -1}},
            {'$skip': (page - 1) * page_size},
            {'$limit': page_size},
            {'$addFields': {'_id': {'$toString': '$_id'}}},
            {'$unset': 'id'}
        ]

        materials = await UserBasicMaterial.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [{'$match': match_stage}, {'$count': 'total'}]
        total_result = await UserBasicMaterial.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        # 处理图片签名URL
        for material in materials:
            if material.get('images'):
                for img in material['images']:
                    img['signed_url'] = await oss_client.signed_get_url(img['oss_key']) if img.get('oss_key') else None

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=materials
        )

    @auth_required(['admin', 'advertiser'])
    async def refetch(self, data):
        _id = data.get('_id')

        # 输入验证
        if not _id:
            raise MException("_id 不能为空")

        # 查找素材
        material = await UserBasicMaterial.find_one(
            UserBasicMaterial.id == PydanticObjectId(_id),
            UserBasicMaterial.is_deleted != True
        )

        if not material:
            raise MException("素材不存在或已删除")

        # 更新获取状态为"待爬取"
        await UserBasicMaterial.find_one(UserBasicMaterial.id == PydanticObjectId(_id)).update(
            {"$set": {"fetch_status": "待爬取"}}
        )

        # 重新发布到爬虫队列
        await publish_messages_to_redis_set(RedisKeyConfig.XHS_NOTE_CONTENT_SPIDER_SET, {"id_": str(_id)})
