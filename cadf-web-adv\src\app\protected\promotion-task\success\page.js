"use client";

import { useEffect } from "react";
import { Box, Button, Container, Paper, Typography, Stack, Avatar, Chip } from "@mui/material";
import { useRouter } from "next/navigation";
import { Check<PERSON>ircle, BarChart3, ArrowRight, Home } from "lucide-react";

export default function TaskSuccessPage() {
    const router = useRouter();

    useEffect(() => {
        // 页面加载时添加一些动画效果
        const timer = setTimeout(() => {
            const element = document.querySelector('.success-animation');
            if (element) {
                element.classList.add('animate-in');
            }
        }, 100);

        return () => clearTimeout(timer);
    }, []);

    const handleGoToTrafficCenter = () => {
        router.push('/protected/product-traffic');
    };

    const handleBackToTasks = () => {
        router.push('/protected/promotion-task');
    };

    const handleGoHome = () => {
        router.push('/protected/dashboard');
    };

    return (
        <Container maxWidth="md" sx={{ py: { xs: 4, md: 8 }, px: { xs: 2, md: 4 } }}>
            <Paper
                className="success-animation"
                sx={{
                    p: { xs: 4, md: 6 },
                    textAlign: 'center',
                    borderRadius: 4,
                    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
                    bgcolor: 'background.paper',
                    opacity: 0,
                    transform: 'translateY(20px)',
                    transition: 'all 0.6s ease-out',
                    '&.animate-in': {
                        opacity: 1,
                        transform: 'translateY(0px)'
                    }
                }}
            >
                {/* 成功图标 */}
                <Box sx={{ mb: 3 }}>
                    <Box
                        sx={{
                            width: 80,
                            height: 80,
                            borderRadius: '50%',
                            bgcolor: 'success.main',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 2,
                            animation: 'pulse 2s ease-in-out infinite'
                        }}
                    >
                        <CheckCircle size={40} color="white" />
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="success.main" sx={{ mb: 1 }}>
                        任务创建成功！
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                        您的推广任务已成功创建，系统正在处理中
                    </Typography>
                </Box>

                {/* 状态信息 */}
                <Box sx={{ mb: 4 }}>
                    <Stack direction="row" justifyContent="center" spacing={2} sx={{ mb: 3 }}>
                        <Chip 
                            label="任务已创建" 
                            color="success" 
                            variant="filled"
                            sx={{ fontWeight: 'medium' }}
                        />
                        <Chip 
                            label="正在处理" 
                            color="info" 
                            variant="outlined"
                            sx={{ fontWeight: 'medium' }}
                        />
                    </Stack>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
                        系统正在根据您的配置生成推广内容，您可以在推广任务列表中查看进度，或前往流量中心查看数据分析。
                    </Typography>
                </Box>

                {/* 推荐操作 */}
                <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" fontWeight="600" sx={{ mb: 3, color: 'text.primary' }}>
                        接下来您可以：
                    </Typography>
                    
                    <Stack spacing={3} direction={{ xs: 'column', md: 'row' }} justifyContent="center">
                        {/* 流量中心卡片 */}
                        <Paper
                            onClick={handleGoToTrafficCenter}
                            sx={{
                                p: 3,
                                borderRadius: 3,
                                cursor: 'pointer',
                                border: '2px solid',
                                borderColor: 'primary.main',
                                bgcolor: 'primary.50',
                                transition: 'all 0.3s ease',
                                minWidth: 200,
                                '&:hover': {
                                    bgcolor: 'primary.100',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.12)'
                                }
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
                                    <BarChart3 size={24} />
                                </Avatar>
                            </Box>
                            <Typography variant="subtitle1" fontWeight="600" sx={{ mb: 1 }}>
                                查看流量中心
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                实时监控推广效果和数据分析
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'primary.main' }}>
                                <Typography variant="body2" fontWeight="medium" sx={{ mr: 1 }}>
                                    立即查看
                                </Typography>
                                <ArrowRight size={16} />
                            </Box>
                        </Paper>

                        {/* 任务列表卡片 */}
                        <Paper
                            onClick={handleBackToTasks}
                            sx={{
                                p: 3,
                                borderRadius: 3,
                                cursor: 'pointer',
                                border: '1px solid',
                                borderColor: 'divider',
                                bgcolor: 'background.paper',
                                transition: 'all 0.3s ease',
                                minWidth: 200,
                                '&:hover': {
                                    bgcolor: 'grey.50',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.12)'
                                }
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                <Avatar sx={{ bgcolor: 'grey.600', width: 48, height: 48 }}>
                                    <Typography variant="h6" fontWeight="bold">
                                        任
                                    </Typography>
                                </Avatar>
                            </Box>
                            <Typography variant="subtitle1" fontWeight="600" sx={{ mb: 1 }}>
                                返回任务列表
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                查看所有推广任务状态
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'text.primary' }}>
                                <Typography variant="body2" fontWeight="medium" sx={{ mr: 1 }}>
                                    查看列表
                                </Typography>
                                <ArrowRight size={16} />
                            </Box>
                        </Paper>
                    </Stack>
                </Box>

                {/* 底部操作按钮 */}
                <Box sx={{ pt: 3, borderTop: '1px solid', borderColor: 'divider' }}>
                    <Button
                        variant="outlined"
                        onClick={handleGoHome}
                        startIcon={<Home size={18} />}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 500,
                            px: 3
                        }}
                    >
                        返回首页
                    </Button>
                </Box>
            </Paper>

            {/* 添加动画样式 */}
            <style jsx>{`
                @keyframes pulse {
                    0% {
                        transform: scale(1);
                        opacity: 1;
                    }
                    50% {
                        transform: scale(1.05);
                        opacity: 0.8;
                    }
                    100% {
                        transform: scale(1);
                        opacity: 1;
                    }
                }
            `}</style>
        </Container>
    );
}