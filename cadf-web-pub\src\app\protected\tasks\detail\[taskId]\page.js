"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import {
    Alert, Box, Button, CardContent, Chip, CircularProgress, Container, Divider, Grid, IconButton,
    LinearProgress, Paper, Stack, TextField, Typography, useMediaQuery, useTheme, InputAdornment
} from '@mui/material';
import { Clock, ChevronLeft, ChevronRight, Home, Download, Copy, Link as LinkIcon, AlertTriangle, CheckCircle, Eye } from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useSnackbar } from 'notistack';
import NextLink from 'next/link';



function TaskDetailDisplay({ taskData, isMobile, onCopyText, activeImageStep, onImageNext, onImageBack }) {
    const theme = useTheme();
    const imageUrls = taskData?.imageUrls || [];

    return (
        <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: 3,
            mb: 3
        }}>
            {/* 图片展示区域 */}
            <Paper 
                elevation={3} 
                sx={{ 
                    borderRadius: 3,
                    overflow: 'hidden',
                    bgcolor: 'background.paper',
                    boxShadow: '0 6px 16px rgba(0,0,0,0.08)'
                }}
            >
                {/* 图片区域 */}
                <Box sx={{ 
                    position: 'relative', 
                    bgcolor: 'grey.50', 
                    height: { xs: 380, sm: 480, md: 550 },
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%'
                }}>
                    {imageUrls.length > 0 ? (
                        <>
                            <Box
                                component="img"
                                src={imageUrls[activeImageStep]}
                                alt={`任务图片 ${activeImageStep + 1}`}
                                onError={(e) => { e.target.onerror = null; e.target.src = 'https://placehold.co/600x400/f5f5f5/666666?text=Image+Not+Available'; }}
                                sx={{ 
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'contain',
                                    display: 'block' 
                                }}
                            />

                            {/* 长按下载提示 (所有设备) */}
                            <Box
                                sx={{
                                    position: 'absolute',
                                    bottom: 16,
                                    left: 0,
                                    right: 0,
                                    textAlign: 'center',
                                    zIndex: 3,
                                    py: 1,
                                    px: 2,
                                    mx: 'auto',
                                    width: 'fit-content',
                                    bgcolor: 'rgba(0,0,0,0.6)',
                                    color: 'white',
                                    borderRadius: 4,
                                    fontSize: '0.75rem',
                                    backdropFilter: 'blur(4px)',
                                    boxShadow: 1,
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 1
                                }}
                            >
                                <Download size={14} />
                                <Typography variant="caption">长按图片可下载保存</Typography>
                            </Box>
                        </>
                    ) : (
                        <Box sx={{ textAlign: 'center', p: 3 }}>
                            <Box 
                                component="img" 
                                src="https://placehold.co/600x400/f5f5f5/666666?text=No+Images+Available" 
                                alt="No Images" 
                                sx={{ maxWidth: '100%', maxHeight: 300 }}
                            />
                        </Box>
                    )}
                    {imageUrls.length > 1 && (
                        <>
                            <IconButton 
                                onClick={onImageBack} 
                                sx={{ 
                                    position: 'absolute', 
                                    top: '50%', 
                                    left: 16, 
                                    transform: 'translateY(-50%)', 
                                    bgcolor: 'background.paper', 
                                    boxShadow: 2,
                                    '&:hover': { bgcolor: 'background.paper', opacity: 0.9 } 
                                }} 
                                size="medium"
                            >
                                <ChevronLeft />
                            </IconButton>
                            <IconButton 
                                onClick={onImageNext} 
                                sx={{ 
                                    position: 'absolute', 
                                    top: '50%', 
                                    right: 16, 
                                    transform: 'translateY(-50%)', 
                                    bgcolor: 'background.paper', 
                                    boxShadow: 2,
                                    '&:hover': { bgcolor: 'background.paper', opacity: 0.9 } 
                                }} 
                                size="medium"
                            >
                                <ChevronRight />
                            </IconButton>
                            <Box sx={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                position: 'absolute', 
                                bottom: {xs: 48, sm: 55},
                                left: 0, 
                                right: 0,
                                zIndex: 2
                            }}>
                                <Paper elevation={3} sx={{ 
                                    display: 'flex',
                                    p: 0.5,
                                    borderRadius: 5,
                                    bgcolor: 'rgba(255,255,255,0.7)',
                                    backdropFilter: 'blur(4px)'
                                }}>
                                    {imageUrls.map((_, index) => (
                                        <Box 
                                            key={index} 
                                            sx={{ 
                                                width: 8, 
                                                height: 8, 
                                                borderRadius: '50%', 
                                                mx: 0.5, 
                                                bgcolor: index === activeImageStep ? 'primary.main' : 'grey.400',
                                                cursor: 'pointer' 
                                            }} 
                                        />
                                    ))}
                                </Paper>
                            </Box>
                        </>
                    )}
                </Box>
            </Paper>

            {/* 复制操作区域 */}
            <Box sx={{ 
                display: 'flex', 
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <Button 
                    onClick={() => onCopyText(taskData.title)} 
                    startIcon={<Copy size={20} />} 
                    variant="contained" 
                    color="primary"
                    size="large"
                    sx={{
                        borderRadius: 6,
                        textTransform: 'none',
                        py: 1.5,
                        px: 4,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        fontSize: '1rem',
                        fontWeight: 600,
                        minWidth: { xs: '100%', sm: '160px' },
                        '&:hover': {
                            boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
                            transform: 'translateY(-2px)',
                            background: theme.palette.primary.dark
                        },
                        transition: 'all 0.2s ease-in-out'
                    }}
                >
                    复制标题
                </Button>
                
                <Button 
                    onClick={() => onCopyText(taskData.content)} 
                    startIcon={<Copy size={20} />} 
                    variant="contained" 
                    color="success"
                    size="large"
                    sx={{
                        borderRadius: 6,
                        textTransform: 'none',
                        py: 1.5,
                        px: 4,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        fontSize: '1rem',
                        fontWeight: 600,
                        minWidth: { xs: '100%', sm: '160px' },
                        '&:hover': {
                            boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
                            transform: 'translateY(-2px)',
                            background: theme.palette.success.dark
                        },
                        transition: 'all 0.2s ease-in-out'
                    }}
                >
                    复制内容
                </Button>
            </Box>
        </Box>
    );
}

function LinkSubmissionForm({ taskData, onSubmitLink, isSubmitting, currentLink, onLinkChange, validationError, onRefreshStatus, isRefreshing }) {
    const theme = useTheme();
    return (
        <Paper 
            elevation={2} 
            sx={{ 
                borderRadius: 2, 
                mb: 3,
                overflow: 'hidden'
            }}
        >
            <Box sx={{ 
                p: 2, 
                borderBottom: 1, 
                borderColor: 'divider',
                bgcolor: 'success.light',
                color: 'success.contrastText',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexWrap: 'wrap', // 允许在小屏幕上换行
                gap: 1 // 添加间距
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinkIcon size={20} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                        {taskData.publish_url ? '发布链接' : '提交发布链接'}
                    </Typography>
                </Box>
                {taskData.platform && (
                    <Chip
                        label={`${taskData.platform}`}
                        color="secondary"
                        size="small"
                        sx={{ 
                            height: 28, 
                            borderRadius: 2,
                            fontWeight: 'medium',
                            bgcolor: 'rgba(255,255,255,0.85)',
                            color: 'success.dark',
                            '& .MuiChip-label': { px: 1.5 }
                        }}
                    />
                )}
            </Box>
            <Box sx={{ p: 3, bgcolor: 'background.paper' }}>
                <TextField
                    fullWidth
                    label="发布链接 (例如: https://www.xiaohongshu.com/discovery/item/...)"
                    variant="outlined"
                    value={currentLink}
                    onChange={onLinkChange}
                    placeholder="请粘贴您发布的链接"
                    InputProps={{ 
                        startAdornment: (<InputAdornment position="start"><LinkIcon size={20} /></InputAdornment>),
                        sx: { borderRadius: 1.5 }
                    }}
                    sx={{ mb: 2 }}
                    error={!!validationError}
                    helperText={validationError || `请粘贴您在${taskData.platform || '指定平台'}发布的链接，确保链接可访问。`}
                />
                
                {taskData.validation_status && (
                    <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 1 }}>
                        <Chip
                            icon={taskData.validation_status === '成功' ? <CheckCircle size={16} /> : <AlertTriangle size={16} />}
                            label={`当前验证状态: ${taskData.validation_status}`}
                            color={
                                taskData.validation_status === '成功' ? 'success' :
                                taskData.validation_status === '失败' ? 'error' :
                                taskData.validation_status === '待验证' ? 'warning' : 'default'
                            }
                            variant={taskData.validation_status === '成功' ? 'filled' : 'outlined'}
                            size="medium"
                            sx={{ py: 1, height: 36, borderRadius: 4 }}
                        />
                        <Button
                            variant="outlined"
                            color="primary"
                            onClick={onRefreshStatus}
                            disabled={isRefreshing}
                            startIcon={isRefreshing ? <CircularProgress size={16} color="inherit" /> : <Clock size={16} />}
                            sx={{ 
                                borderRadius: 2,
                                py: 0.8,
                                px: 2,
                                textTransform: 'none',
                                fontWeight: 'medium',
                                flexShrink: 0
                            }}
                            size="small"
                        >
                            {isRefreshing ? '刷新中...' : '刷新状态'}
                        </Button>
                    </Box>
                )}
                
                {taskData.validation_status === '失败' && taskData.validation_details && (
                    <Alert severity="error" variant="outlined" sx={{ mb: 2, borderRadius: 1.5 }}>
                        <Typography variant="body2">
                            失败原因: {taskData.validation_details}
                        </Typography>
                    </Alert>
                )}
                
                <Button
                    variant="contained"
                    color="success"
                    onClick={onSubmitLink}
                    disabled={isSubmitting || !currentLink.trim()}
                    startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <LinkIcon />}
                    sx={{ 
                        borderRadius: 2,
                        py: 1.2,
                        px: 3,
                        textTransform: 'none',
                        fontWeight: 'medium'
                    }}
                    size="large"
                >
                    {isSubmitting ? '正在提交...' : (taskData.publish_url ? '重新提交' : '提交链接验证')}
                </Button>
            </Box>
        </Paper>
    );
}

function TaskDetailPageContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams(); // For reading URL query parameters
    const { enqueueSnackbar } = useSnackbar();


    const taskId = params.taskId; // This is PromotionTaskDetail ID

    const [taskData, setTaskData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isSubmittingLink, setIsSubmittingLink] = useState(false);
    const [publishLinkInput, setPublishLinkInput] = useState('');
    const [linkValidationError, setLinkValidationError] = useState('');
    const [activeImageStep, setActiveImageStep] = useState(0);
    const [isGivingUpTask, setIsGivingUpTask] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const fetchTask = async (currentTaskId) => {
        setLoading(true); setError(null);
        try {
            const response = await pubPromotionTaskApi.getTaskDetailForView(currentTaskId);
            if (response && response.task_detail) {
                const taskDetail = response.task_detail;
                // 处理图片数据格式转换
                const processedTaskData = {
                    ...taskDetail,
                    imageUrls: taskDetail.image_urls || (taskDetail.image_url ? [taskDetail.image_url] : [])
                };
                setTaskData(processedTaskData);
                setPublishLinkInput(taskDetail.publish_url || '');
            } else {
                throw new Error("未能获取有效的任务详情数据");
            }
        } catch (err) {
            console.error("获取任务详情失败:", err);
            setError(err.message || "获取任务详情失败，请重试。");
            enqueueSnackbar(err.message || "获取任务详情失败", { variant: 'error' });
        }
        setLoading(false);
    };

    useEffect(() => {
        if (taskId) {
            fetchTask(taskId);
        }
    }, [taskId]);

    const handleRefreshStatus = async () => {
        setIsRefreshing(true);
        try {
            await fetchTask(taskId);
            enqueueSnackbar("状态已刷新", { variant: 'success' });
        } catch (err) {
            console.error("刷新状态失败:", err);
            // 错误处理已在fetchTask中完成
        }
        setIsRefreshing(false);
    };

    const handleSubmitLink = async () => {
        if (!publishLinkInput.trim()) {
            setLinkValidationError("链接不能为空。");
            return;
        }
        setLinkValidationError('');
        setIsSubmittingLink(true);
        try {
            await pubPromotionTaskApi.updatePublishLink(taskId, publishLinkInput.trim());
            enqueueSnackbar("发布链接已提交成功！", { variant: 'success' });
            fetchTask(taskId); // Re-fetch to get updated validation status
        } catch (err) {
            console.error("提交链接失败:", err);
            enqueueSnackbar(`提交链接失败: ${err.message || '未知错误'}`, { variant: 'error' });
        }
        setIsSubmittingLink(false);
    };

    const handleGiveUpTask = async () => {
        setIsGivingUpTask(true);
        try {
            await pubPromotionTaskApi.giveUpTask(taskId);
            enqueueSnackbar("任务已成功放弃！", { variant: 'success' });
            // 放弃任务成功后，重定向到账户任务页面
            router.push('/protected/tasks/account');
        } catch (err) {
            console.error("放弃任务失败:", err);
            enqueueSnackbar(`放弃任务失败: ${err.message || '未知错误'}`, { variant: 'error' });
            setIsGivingUpTask(false);
        }
    };

    const copyTextToClipboard = (text) => {
        if (!text) return;

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(String(text))
                .then(() => enqueueSnackbar("已复制到剪贴板", { variant: 'success' }))
                .catch(err => {
                    console.warn("navigator.clipboard.writeText 失败, 尝试回退: ", err);
                    // 回退到 execCommand
                    fallbackCopyTextToClipboard(String(text));
                });
        } else {
            // 如果 navigator.clipboard API 不存在，直接使用回退
            fallbackCopyTextToClipboard(String(text));
        }
    };

    // 回退复制方法
    const fallbackCopyTextToClipboard = (text) => {
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 避免在屏幕上显示 textArea
        textArea.style.position = "fixed"; // 或者 'absolute'
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.width = "2em";
        textArea.style.height = "2em";
        textArea.style.padding = "0";
        textArea.style.border = "none";
        textArea.style.outline = "none";
        textArea.style.boxShadow = "none";
        textArea.style.background = "transparent";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                enqueueSnackbar("已复制到剪贴板 (回退)", { variant: 'success' });
            } else {
                enqueueSnackbar("复制失败 (回退)", { variant: 'error' });
            }
        } catch (err) {
            console.error("回退复制失败: ", err);
            enqueueSnackbar("复制操作不支持或失败", { variant: 'error' });
        }

        document.body.removeChild(textArea);
    };

    const handleImageNext = () => setActiveImageStep(prev => (prev + 1) % (taskData?.imageUrls?.length || 1));
    const handleImageBack = () => setActiveImageStep(prev => (prev - 1 + (taskData?.imageUrls?.length || 1)) % (taskData?.imageUrls?.length || 1));

    // Derived state computations - must be before early returns.
    // Use optional chaining for safe access to taskData properties.
    const isTaskAssigned = !!taskData?.user_id;

    if (loading) return <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh', overflowX: 'hidden' }}><CircularProgress size={60} /></Container>;
    if (error) return <Container sx={{ py: 3 }}><Alert severity="error">{error}</Alert></Container>;
    if (!taskData) return <Container sx={{ py: 3 }}><Alert severity="info">未找到任务详情。</Alert></Container>;

    const pageModeTitle = "任务详情";

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 }, px: { xs: 2, sm: 3 }, overflowX: 'hidden' }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Typography variant="caption" color="text.secondary" onClick={() => router.back()} sx={{cursor: 'pointer', '&:hover': {textDecoration: 'underline'}}}>
                    返回
                </Typography>
            </Stack>

            <Typography variant={isMobile ? "h6" : "h5"} component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 2}}>
                {pageModeTitle}
            </Typography>

            <TaskDetailDisplay 
                taskData={taskData} 
                isMobile={isMobile} 
                onCopyText={copyTextToClipboard}
                activeImageStep={activeImageStep}
                onImageNext={handleImageNext}
                onImageBack={handleImageBack}
            />

            {/* 提交发布链接部分 */}
            <LinkSubmissionForm 
                taskData={taskData} 
                onSubmitLink={handleSubmitLink} 
                isSubmitting={isSubmittingLink} 
                currentLink={publishLinkInput} 
                onLinkChange={(e) => setPublishLinkInput(e.target.value)}
                validationError={linkValidationError}
                onRefreshStatus={handleRefreshStatus}
                isRefreshing={isRefreshing}
            />
            
            {/* 放弃任务按钮 */}
            {taskData?.validation_status !== '成功' && (
                <Box sx={{ mb: 3 }}>
                    <Button
                        variant="outlined"
                        color="error"
                        onClick={handleGiveUpTask}
                        disabled={isGivingUpTask}
                        startIcon={isGivingUpTask ? <CircularProgress size={20} color="inherit" /> : <AlertTriangle />}
                        fullWidth
                        sx={{ 
                            borderRadius: 2,
                            py: 1.2,
                            px: 3,
                            textTransform: 'none',
                            fontWeight: 'medium',
                            borderWidth: 2
                        }}
                        size="large"
                    >
                        {isGivingUpTask ? '正在放弃...' : '放弃任务'}
                    </Button>
                </Box>
            )}
            
            {!isTaskAssigned && ( // Task free
                <Alert severity="info" variant="filled" sx={{mt:1, borderRadius: 2}}>此任务当前未被分配或已被放弃。您可以从任务市场或账户管理页面查找并接取新任务。</Alert>
            )}

        </Container>
    );
}

export default function TaskDetailPage() {
    return (
        <Suspense fallback={<Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh', overflowX: 'hidden' }}><CircularProgress size={60} /></Container>}>
            <TaskDetailPageContent />
        </Suspense>
    );
} 