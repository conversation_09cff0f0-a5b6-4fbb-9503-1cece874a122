import time
from typing import Any, Dict, List

from beanie import PydanticObjectId

from models.models import User
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.log.log import olog


@register_handler("advertiser")
class AdvertiserManagementApi:

    @auth_required(["admin"])
    async def create(self, data: Dict[str, Any]):
        """创建广告主账户"""
        username = data.get("username")
        password = data.get("password")

        # 输入验证
        if not username:
            raise MException("用户名不能为空")
        if not password:
            raise MException("密码不能为空")

        # 检查用户名是否已存在
        existing_user = await User.find_one(User.username == username)
        if existing_user:
            raise MException("用户名已存在，请更换用户名")

        # 创建新的广告主用户
        new_user = User(
            username=username,
            password=password,
            roles=["advertiser"],
            create_at=int(time.time())
        )
        await new_user.insert()

        olog.info(f"管理员创建了新的广告主账户: {username}")

    @auth_required(["admin"])
    async def modify(self, data: Dict[str, Any]):
        """更新广告主信息"""
        target_user_id = data.pop("target_user_id", None)
        username = data.get("username")
        password = data.get("password")

        # 输入验证
        if not target_user_id:
            raise MException("target_user_id 不能为空")

        user_to_update = await User.find_one(User.id == PydanticObjectId(target_user_id))

        # 检查用户名是否被其他用户使用
        if username and username != user_to_update.username:
            existing_user = await User.find_one(User.username == username)
            if existing_user:
                raise MException("用户名已存在，请更换用户名")
            user_to_update.username = username

        # 更新密码
        if password:
            user_to_update.password = password

        await user_to_update.save()

        olog.info(f"管理员更新了广告主账户信息: {user_to_update.username}")

    @auth_required(["admin"])
    async def delete(self, data: Dict[str, Any]):
        """删除广告主"""
        target_user_id = data.get("target_user_id")

        # 输入验证
        if not target_user_id:
            raise MException("target_user_id 不能为空")

        user = await User.find_one(User.id == PydanticObjectId(target_user_id))
        username = user.username
        await user.delete()

        olog.info(f"管理员删除了广告主账户: {username}")

    @auth_required(["admin"])
    async def query_list(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """查询广告主列表"""
        page = data.get("page", 1)
        page_size = data.get("page_size", 10)
        search = data.get("search", "")

        # 构建查询条件
        match_stage = {"$match": {"roles": "advertiser"}}
        if search:
            match_stage["$match"]["username"] = {"$regex": search, "$options": "i"}

        # 分页查询
        pipeline = [
            match_stage,
            {"$sort": {"create_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {"$addFields": {"_id": {"$toString": "$_id"}}},
            {"$unset": "id"}
        ]

        advertisers: List[Dict[str, Any]] = await User.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [match_stage]
        total_result = await User.aggregate(count_pipeline + [{"$count": "total"}]).to_list()
        total = total_result[0]["total"] if total_result else 0

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=advertisers
        )

    @auth_required(["admin"])
    async def query_detail(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """查询单个广告主详情"""
        target_user_id = data.get("target_user_id")

        user = await User.find_one(User.id == PydanticObjectId(target_user_id))
        return user.to_dict() if user else None
