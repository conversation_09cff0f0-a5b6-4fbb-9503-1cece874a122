# 关于 API 编写规范

## 整体规范

-   不要使用防御型编程。
-   函数签名必须明确标识参数类型和返回类型。
-   主动报错使用 `raise MException("错误信息")`，这个错误信息需要对用户友好。
-   在方法开头统一用 `data.get('')` 提取参数，提升可读性。
-   只要方法上有 `@auth_required` 装饰器，方法参数 `data` 中一定会包含 `user_id`，无需额外校验或获取。
-   API接收 `id` 参数时，统一使用 `_id` 字段，避免直接使用 `id`，防止与系统保留字段冲突。
-   方法内部不需要 `try...catch...`。
-   查询方法可以直接返回 Pydantic 模型。
-   非查询方法无需返回值。
-   复杂或分页查询用 MongoDB 聚合管道（pipeline）实现。
-   查询方法以 `query_` 开头。
-   创建方法 `create` 。
-   修改方法 `modify` 。
-   删除方法 `delete` 。
-   非查询方法只需要输入参数是否为空验证。

## 示例代码
```python
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.api.exception import MException
from beanie import Document, PydanticObjectId
from pydantic import Field
from typing import Optional, List, Dict, Any
from omni.api.pageable import PageResponse
import time

# 示例 Beanie Document 模型
class StudyPlan(Document):
    user_id: Optional[str] = Field(default=None, title="用户ID")
    title: Optional[str] = Field(default=None, title="学习计划标题")
    description: Optional[str] = Field(default=None, title="描述")
    create_at: Optional[int] = Field(default=None, title="创建时间")
    is_deleted: Optional[bool] = Field(default=False, title="删除标记")

    class Settings:
        name = "study_plans"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data

# 用于 fastapi 注册处理器，只有被注册的类才能通过 http 接口访问
@register_handler('study_plan_api')
class StudyPlanApi:
    
    @auth_required(['user', 'admin']) # 认证与鉴权，指定权限可执行
    async def create(self, data):
        # 在方法开头统一用 data.get('') 提取参数，提升可读性
        user_id = data.get('user_id')
        title = data.get('title')
        description = data.get('description')

        # 保存数据时，务必使用 insert 方法，并将各参数单独列出，避免直接传递 data
        study_plan = StudyPlan(user_id=user_id, title=title, description=description, create_at=int(time.time()))
        await study_plan.insert()

    @auth_required(['user', 'admin'])
    async def modify(self, data):
        # 在方法开头统一用 data.get('') 提取参数，提升可读性
        _id = data.pop('_id', None) # 移除 _id

        # 更新数据使用 find_one 和 update 方法
        await StudyPlan.find_one(StudyPlan.id == PydanticObjectId(_id)).update({"$set": data})

    @auth_required(['user', 'admin'])
    async def delete(self, data):
        # 在方法开头统一用 data.get('') 提取参数，提升可读性
        _id = data.get('_id')
        # 软删除：设置 is_deleted 字段为 True
        await StudyPlan.find_one(StudyPlan.id == PydanticObjectId(_id)).update({"$set": {"is_deleted": True}})

    @auth_required(['user', 'admin'])
    async def query_one(self, data):
        # 在方法开头统一用 data.get('') 提取参数，提升可读性
        _id = data.get('_id')
        # 查询单个数据，排除已删除的记录
        study_plan = await StudyPlan.find_one(StudyPlan.id == PydanticObjectId(_id), StudyPlan.is_deleted != True)
        return study_plan.to_dict() if study_plan else None

    @auth_required(['user', 'admin'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[StudyPlan]:
        # 在方法开头统一用 data.get('') 提取参数，提升可读性
        user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        # 复杂或分页查询用 MongoDB 聚合管道（pipeline）实现

        match_stage = {"$match": {"user_id": user_id, "is_deleted": {"$ne": True}}}

        pipeline = [
            match_stage,
            {"$sort": {"create_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {"$addFields": {"_id": {"$toString": "$_id"}}}, # 需要把id转化为字符串的_id
            {"$unset": "id"} # 删除原有ID
        ]

        study_plans: List[StudyPlan] = await StudyPlan.aggregate(pipeline).to_list()
        
        # 获取总数
        total_pipeline = [
            match_stage,
            {"$count": "total"}
        ]
        total_result = await StudyPlan.aggregate(total_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0
        
        # 分页使用PageResponse
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=study_plans
        )
```
