"use client";

import {usePathname} from 'next/navigation';
import Link from 'next/link';
import {ListItem, ListItemButton, ListItemIcon, ListItemText} from '@mui/material';
import {useTheme} from '@mui/material/styles';

export default function SidebarLink({item, onClick}) {
    const theme = useTheme();
    const pathname = usePathname();
    const isActive = pathname === item.path;

    // 如果没有 path 属性，不渲染链接
    if (!item.path) {
        return null;
    }

    return (
        <ListItem disablePadding sx={{my: 1}}>
            <Link href={item.path} style={{width: '100%', textDecoration: 'none', color: 'inherit'}}>
                <ListItemButton
                    onClick={onClick}
                    sx={{
                        px: 3,
                        py: 1.5,
                        borderRadius: 3,
                        mx: 2,
                        position: 'relative',
                        overflow: 'hidden',
                        bgcolor: isActive ? theme.palette.primary.main : 'transparent',
                        color: isActive ? theme.palette.primary.contrastText : theme.palette.text.primary,
                        '& .MuiListItemIcon-root': {
                            color: isActive ? theme.palette.primary.contrastText : theme.palette.text.secondary,
                            minWidth: 40,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        },
                        '&:hover': {
                            backgroundColor: isActive ? theme.palette.primary.dark : theme.palette.grey[100],
                            transform: 'translateX(4px)',
                            '& .MuiListItemIcon-root': {
                                color: isActive ? theme.palette.primary.contrastText : theme.palette.primary.main,
                            },
                        },
                        transition: 'all 0.3s ease',
                        boxShadow: isActive ? theme.shadows[2] : 'none',
                    }}
                >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <ListItemText
                        primary={item.text}
                        primaryTypographyProps={{
                            fontWeight: isActive ? 500 : 400,
                            fontSize: '0.95rem',
                        }}
                    />
                </ListItemButton>
            </Link>
        </ListItem>
    );
} 