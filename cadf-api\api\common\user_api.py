import json
import time
from typing import Dict, Any, List

from beanie import PydanticObjectId

from models.models import User, CrewManagement
from omni.api.auth import auth_required, generate_token
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from omni.redis.redis_client import rc


@register_handler("user_api")
class UserApi:

    async def login(self, data: Dict[str, Any]):
        username = data.get("username")
        password = data.get("password")

        # 输入验证
        if not username:
            raise MException("用户名不能为空")
        if not password:
            raise MException("密码不能为空")

        # 登录验证
        user = await User.find_one(User.username == username)
        if not user or user.password != password:
            raise MException("登录失败,错误的用户名或密码")
        access_token = generate_token(str(user.id))
        roles = user.roles
        return {"access_token": access_token, "roles": roles}

    async def creator_register(self, data: Dict[str, Any]):
        username = data.get("username")
        password = data.get("password")
        crew_invite_code = data.get("crew_invite_code")
        roles = ["creator"]

        # 输入验证
        if not username:
            raise MException("用户名不能为空")
        if not password:
            raise MException("密码不能为空")

        # 检查用户名是否已存在
        existing_user = await User.find_one(User.username == username)
        if existing_user:
            raise MException("用户名已存在，请更换用户名")

        # 创建新用户
        new_user = User(
            username=username,
            password=password,
            roles=roles,
            create_at=int(time.time())
        )
        await new_user.insert()

        # 处理舰队加入逻辑
        if crew_invite_code:
            captain_user = await User.find_one(User.crew_invite_code == crew_invite_code, User.roles == "captain")
            if captain_user:
                # 创建 CrewManagement 记录
                crew_management = CrewManagement(
                    captain_user_id=str(captain_user.id),
                    crew_user_id=str(new_user.id),
                    create_at=int(time.time())
                )
                await crew_management.insert()
                olog.info(f"用户 {new_user.id} 成功加入舰队 {captain_user.id}")
            else:
                olog.warning(f"用户 {new_user.id} 使用的邀请码 {crew_invite_code} 无效")

        # 返回用户信息和token
        access_token = generate_token(str(new_user.id))

        return {
            "access_token": access_token,
            "roles": roles,
        }

    @auth_required(["admin"])
    async def create(self, data: Dict[str, Any]):
        username = data.get("username")
        password = data.get("password")
        roles = data.get("roles", ["user"])

        # 输入验证
        if not username:
            raise MException("用户名不能为空")
        if not password:
            raise MException("密码不能为空")

        # 检查用户名是否已存在
        existing_user = await User.find_one(User.username == username)
        if existing_user:
            raise MException("用户名已存在，请更换用户名")

        # 创建新用户
        new_user = User(
            username=username,
            password=password,
            roles=roles,
            create_at=int(time.time())
        )
        await new_user.insert()

    @auth_required(["admin"])
    async def modify(self, data: Dict[str, Any]):
        user_id = data.get("_id")

        # 输入验证
        if not user_id:
            raise MException("_id 不能为空")
        user = await User.find_one(User.id == PydanticObjectId(user_id))

        if not user:
            raise MException("用户不存在")

        update_fields = {}
        # 更新用户信息
        if "username" in data:
            # 检查用户名是否已被其他用户使用
            existing_user = await User.find_one(User.username == data["username"])
            if existing_user and str(existing_user.id) != user_id:
                raise MException("用户名已存在，请更换用户名")
            update_fields["username"] = data["username"]

        if "password" in data and data["password"]:
            update_fields["password"] = data["password"]

        if "roles" in data:
            update_fields["roles"] = data["roles"]

        if update_fields:
            await user.update({"$set": update_fields})

        # 如果更新了角色，清除Redis缓存
        if "roles" in data:
            redis_key = f"session:roles:{user_id}"
            await rc.delete(redis_key)

    @auth_required(["admin"])
    async def delete(self, data: Dict[str, Any]):
        user_id = data.get("_id")

        # 输入验证
        if not user_id:
            raise MException("_id 不能为空")
        user = await User.find_one(User.id == PydanticObjectId(user_id))
        if not user:
            raise MException("用户不存在")
        await user.delete()

    @auth_required(["user", "admin"])
    async def query_one(self, data: Dict[str, Any]) -> Dict[str, Any]:
        target_user_id = data.get("target_user_id")

        user = await User.find_one(User.id == PydanticObjectId(target_user_id))
        if not user:
            raise MException("用户不存在")
        return user.to_dict()

    @auth_required(["admin"])
    async def query_all(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        users = await User.find_all().to_list()
        return [user.to_dict() for user in users]

    @auth_required(["user", "advertiser", "creator", "captain", "admin"])
    async def query_roles(self, data: Dict[str, Any]):
        """获取用户角色，使用 Redis 缓存"""
        user_id = data.get("user_id")

        redis_key = f"session:roles:{user_id}"

        cached_roles = await rc.get(redis_key)
        if cached_roles:
            olog.info(f"从 Redis 获取用户 {user_id} 的角色缓存")
            return {"roles": json.loads(cached_roles)}

        user = await User.find_one(User.id == PydanticObjectId(user_id))
        roles = user.roles if user else []

        await rc.setex(redis_key, 300, json.dumps(roles))
        olog.info(f"从数据库获取用户 {user_id} 的角色并存入 Redis 缓存")

        return {"roles": roles}
