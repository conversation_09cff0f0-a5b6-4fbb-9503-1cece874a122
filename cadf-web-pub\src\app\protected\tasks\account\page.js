"use client";

import {useEffect, useState} from 'react';
import {<PERSON>ert, Avatar, Box, Button, Chip, Container, Grid, IconButton, LinearProgress, Paper, Stack, Typography, useMediaQuery, useTheme} from '@mui/material';
import {CheckCircle, ChevronLeft, Eye, ListChecks, PowerOff, Users, AlertOctagon, Clock, Target} from 'lucide-react';
import {pubPromotionTaskApi} from '@/api/pub-promotion-task-api';
import { useSnackbar } from 'notistack';
import { useRouter } from 'next/navigation';


function AccountSelector({
                             accounts,
                             loadingAccounts,
                             errorAccounts,
                             onAccountClick,
                             onViewAcceptedTasksClick,
                             isMobile,
                         }) {
    const theme = useTheme();

    const getStatusConfig = (status) => {
        switch (status) {
            case '在线':
                return { color: 'success', icon: CheckCircle, text: '在线' };
            case '离线':
                return { color: 'warning', icon: PowerOff, text: '离线' };
            case '封禁':
                return { color: 'error', icon: AlertOctagon, text: '封禁' };
            default:
                return { color: 'default', icon: PowerOff, text: '未知' };
        }
    };

    if (loadingAccounts) {
        return (
            <Box sx={{ mt: 4 }}>
                <LinearProgress sx={{ borderRadius: 1, height: 6 }} />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                    正在加载账户信息...
                </Typography>
            </Box>
        );
    }

    if (errorAccounts) {
        return (
            <Alert 
                severity="error" 
                sx={{ 
                    mt: 4, 
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'error.light',
                    bgcolor: 'error.50'
                }}
            >
                {errorAccounts}
            </Alert>
        );
    }

    if (accounts.length === 0) {
        return (
            <Paper
                elevation={0}
                sx={{
                    p: 6,
                    mt: 4,
                    borderRadius: 3,
                    bgcolor: 'grey.50',
                    border: '1px solid',
                    borderColor: 'divider',
                    textAlign: 'center'
                }}
            >
                <Users size={48} color={theme.palette.text.secondary} style={{ marginBottom: 16 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                    暂无可用账户
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    请先在"账号管理"页面添加账户
                </Typography>
            </Paper>
        );
    }

    return (
        <Grid container spacing={3} sx={{ mt: 0 }}>
            {accounts.map((account) => {
                let isDisabled = false;
                let disabledReason = '';

                if (account.status !== '在线') {
                    isDisabled = true;
                    disabledReason = '账户非在线状态';
                }

                const dailyLimitReached = typeof account.current_usage_level === 'number' &&
                                        typeof account.limit === 'number' &&
                                        account.limit > 0 &&
                                        account.current_usage_level >= account.limit;

                if (dailyLimitReached) {
                    isDisabled = true;
                    disabledReason = '今日接任务已达上限';
                }

                const uncompletedLimitReached = typeof account.uncompleted_tasks_count === 'number' &&
                                              typeof account.max_uncompleted_limit === 'number' &&
                                              account.max_uncompleted_limit > 0 &&
                                              account.uncompleted_tasks_count >= account.max_uncompleted_limit;
                
                if (uncompletedLimitReached) {
                    isDisabled = true;
                    disabledReason = '未完成任务已达上限';
                }

                const statusConfig = getStatusConfig(account.status);
                const StatusIcon = statusConfig.icon;

                return (
                    <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={account._id}>
                        <Paper
                            elevation={0}
                            sx={{
                                p: 3,
                                borderRadius: 3,
                                border: '1px solid',
                                borderColor: isDisabled ? 'divider' : 'transparent',
                                bgcolor: isDisabled ? 'grey.50' : 'background.paper',
                                transition: 'all 0.2s ease-in-out',
                                cursor: isDisabled ? 'not-allowed' : 'pointer',
                                opacity: isDisabled ? 0.7 : 1,
                                '&:hover': isDisabled ? {} : {
                                    borderColor: 'primary.main',
                                    transform: 'translateY(-2px)',
                                    boxShadow: theme.shadows[4],
                                }
                            }}
                        >
                            <Stack spacing={2.5}>
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Avatar 
                                        sx={{
                                            width: 56,
                                            height: 56,
                                            bgcolor: statusConfig.color === 'success' ? 'success.main' : 'grey.400',
                                            fontSize: '1.5rem',
                                            fontWeight: 600,
                                        }}
                                    >
                                        {account.name ? account.name.charAt(0).toUpperCase() : '?'}
                                    </Avatar>
                                    <Box sx={{ flex: 1, minWidth: 0 }}>
                                        <Typography
                                            variant="h6"
                                            sx={{ 
                                                fontWeight: 600, 
                                                mb: 0.5,
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap'
                                            }}
                                        >
                                            {account.name || '未命名账号'}
                                        </Typography>
                                        <Typography 
                                            variant="body2" 
                                            color="text.secondary"
                                            sx={{ 
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                                mb: 1
                                            }}
                                        >
                                            {account.domain || '未知领域'}
                                        </Typography>
                                        <Stack direction="row" spacing={1} alignItems="center">
                                            <Chip
                                                label={account.platform}
                                                size="small"
                                                variant="outlined"
                                                sx={{ 
                                                    height: 24, 
                                                    borderRadius: 3,
                                                    fontSize: '0.75rem',
                                                    fontWeight: 500
                                                }}
                                            />
                                            <Chip
                                                icon={<StatusIcon size={14} />}
                                                label={statusConfig.text}
                                                size="small"
                                                color={statusConfig.color}
                                                variant="filled"
                                                sx={{ 
                                                    height: 24, 
                                                    borderRadius: 3,
                                                    fontSize: '0.75rem',
                                                    fontWeight: 500
                                                }}
                                            />
                                        </Stack>
                                    </Box>
                                </Stack>

                                {/* 任务统计 */}
                                <Stack spacing={1.5}>
                                    {(typeof account.current_usage_level === 'number' && typeof account.limit === 'number' && account.limit > 0) && (
                                        <Box
                                            sx={{
                                                p: 1.5,
                                                borderRadius: 2,
                                                bgcolor: dailyLimitReached ? 'error.50' : 'primary.50',
                                                border: '1px solid',
                                                borderColor: dailyLimitReached ? 'error.200' : 'primary.200',
                                            }}
                                        >
                                            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 0.5 }}>
                                                <Target size={16} color={dailyLimitReached ? theme.palette.error.main : theme.palette.primary.main} />
                                                <Typography variant="body2" fontWeight={500}>
                                                    今日任务进度
                                                </Typography>
                                            </Stack>
                                            <Typography variant="h6" fontWeight={600}>
                                                {account.current_usage_level} / {account.limit}
                                            </Typography>
                                        </Box>
                                    )}

                                    {(typeof account.uncompleted_tasks_count === 'number' && typeof account.max_uncompleted_limit === 'number' && account.max_uncompleted_limit > 0) && (
                                        <Box
                                            sx={{
                                                p: 1.5,
                                                borderRadius: 2,
                                                bgcolor: uncompletedLimitReached ? 'warning.50' : 'info.50',
                                                border: '1px solid',
                                                borderColor: uncompletedLimitReached ? 'warning.200' : 'info.200',
                                            }}
                                        >
                                            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 0.5 }}>
                                                <Clock size={16} color={uncompletedLimitReached ? theme.palette.warning.main : theme.palette.info.main} />
                                                <Typography variant="body2" fontWeight={500}>
                                                    未完成任务
                                                </Typography>
                                            </Stack>
                                            <Typography variant="h6" fontWeight={600}>
                                                {account.uncompleted_tasks_count} / {account.max_uncompleted_limit}
                                            </Typography>
                                        </Box>
                                    )}
                                </Stack>

                                {/* 操作按钮 */}
                                <Stack spacing={1.5} sx={{ pt: 1 }}>
                                    <Button 
                                        variant="contained" 
                                        size="large"
                                        fullWidth 
                                        onClick={() => onAccountClick(account)} 
                                        startIcon={<Eye size={18}/>}
                                        disabled={isDisabled}
                                        title={isDisabled ? disabledReason : '查看推荐任务'}
                                        sx={{ 
                                            borderRadius: 2.5,
                                            textTransform: 'none',
                                            fontWeight: 600,
                                            py: 1.5,
                                            fontSize: '0.95rem'
                                        }}
                                    >
                                        推荐任务
                                    </Button>
                                    <Button 
                                        variant="outlined" 
                                        size="large"
                                        fullWidth 
                                        onClick={() => onViewAcceptedTasksClick(account)} 
                                        startIcon={<ListChecks size={18}/>}
                                        sx={{ 
                                            borderRadius: 2.5,
                                            textTransform: 'none',
                                            fontWeight: 600,
                                            py: 1.5,
                                            fontSize: '0.95rem'
                                        }}
                                    >
                                        已接任务
                                    </Button>
                                </Stack>
                            </Stack>
                        </Paper>
                    </Grid>
                );
            })}
        </Grid>
    );
}


export default function TasksEntryPointPage() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();

    const [accountsState, setAccountsState] = useState({
        accounts: [],
        loading: true,
        error: null,
    });

    useEffect(() => {
        const fetchAccountsAndStatus = async () => {
            setAccountsState({accounts: [], loading: true, error: null});
            try {
                const response = await pubPromotionTaskApi.queryAccountsWithTaskStatus();
                if (response && Array.isArray(response)) {
                    setAccountsState({accounts: response, loading: false, error: null});
                } else {
                    setAccountsState({accounts: [], loading: false, error: '未能获取有效的账户数据'});
                }
            } catch (error) {
                console.error("获取账户列表及状态失败:", error);
                const errorMsg = `获取账户列表失败: ${error.message || '未知错误'}`;
                setAccountsState({accounts: [], loading: false, error: errorMsg});
            }
        };
        fetchAccountsAndStatus();
    }, []);

    const handleAccountCardClick = (account) => {
        const accountId = account?.id_ || account?._id;
        if (account && accountId) {
            if (account.status !== '在线') {
                 enqueueSnackbar(`账户 ${account.name || ''} 非在线状态，无法查看任务市场。`, { variant: 'warning' });
                return;
            }
            const queryParams = new URLSearchParams();
            queryParams.append('accountId', accountId);

            router.push(`/protected/tasks/market?${queryParams.toString()}`);
        } else {
            enqueueSnackbar("无效的账户信息", { variant: 'error' });
        }
    };

    const handleViewAcceptedTasks = (account) => {
        const accountId = account?.id_ || account?._id;
        if (account && accountId) {
            router.push(`/protected/tasks/accepted/${accountId}`);
        } else {
             enqueueSnackbar("选择账户以查看其已接任务，或稍后实现查看所有已接任务。", { variant: 'info' });
        }
    };

    return (
        <Container
            maxWidth="lg"
            sx={{
                py: { xs: 3, sm: 4, md: 5 },
                px: { xs: 2, sm: 3 }
            }}
        >
            {/* Header */}
            <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 4 }}>
                <IconButton 
                    onClick={() => router.back()} 
                    sx={{ 
                        bgcolor: 'background.paper',
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 2,
                        '&:hover': {
                            bgcolor: 'action.hover',
                        }
                    }}
                >
                    <ChevronLeft size={20} />
                </IconButton>
                <Box sx={{ flex: 1 }}>
                    <Typography 
                        variant={isMobile ? "h4" : "h3"} 
                        component="h1" 
                        sx={{ 
                            fontWeight: 700,
                            color: 'text.primary',
                            mb: 0.5
                        }}
                    >
                        任务中心
                    </Typography>
                    <Typography 
                        variant="body1" 
                        color="text.secondary"
                        sx={{ 
                            fontSize: '1.1rem',
                            display: { xs: 'none', sm: 'block' }
                        }}
                    >
                        选择一个账户开始管理或接取图文发布任务
                    </Typography>
                </Box>
            </Stack>

            {/* Account Grid */}
            <AccountSelector
                accounts={accountsState.accounts}
                loadingAccounts={accountsState.loading}
                errorAccounts={accountsState.error}
                onAccountClick={handleAccountCardClick}
                onViewAcceptedTasksClick={handleViewAcceptedTasks}
                isMobile={isMobile}
            />
        </Container>
    );
}
