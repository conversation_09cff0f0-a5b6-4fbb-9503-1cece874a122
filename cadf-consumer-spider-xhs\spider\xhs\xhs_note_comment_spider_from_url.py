"""
从URL进入小红书笔记页面，并爬取所有评论 - Playwright版本
"""
import traceback
from typing import Dict, List, Tuple

from config.config import SpiderConfig
from omni.exception.retry_decorator import retry_on_exception
from omni.log.log import olog
from spider.schemas.comment_thread_schema import CommentThread, Reply
from spider.schemas.xhs_comment_api_response_schema import CommentApiResponse
from spider.tools.browser_context import new_playwright_page
from spider.tools.xhs_tools import smooth_scroll_down

# 笔记详情页面-滚动容器的CSS选择器
NOTE_SCROLLER_SELECTOR = '.note-scroller'


async def process_comment_response(response, all_comments_data: List[CommentThread]) -> bool:
    """
    处理评论API响应
    :param response: API响应对象
    :param all_comments_data: 存储评论数据的列表
    :return: 是否还有更多数据
    """
    json_data = await response.json()
    api_response = CommentApiResponse.model_validate(json_data)

    # 检查是否有评论数据
    if not api_response.data.comments:
        olog.debug("该笔记没有评论")
        return False

    olog.debug(f"获取到 {len(api_response.data.comments)} 条主评论")

    for comment in api_response.data.comments:
        replies = []
        for sub_comment in comment.sub_comments:
            replies.append(Reply(
                author=sub_comment.user_info.nickname,
                author_id=sub_comment.user_info.user_id,
                content=sub_comment.content,
                date=sub_comment.create_time,
                like_count=sub_comment.like_count,
                ip_location=sub_comment.ip_location
            ))

        all_comments_data.append(CommentThread(
            author=comment.user_info.nickname,
            author_id=comment.user_info.user_id,
            content=comment.content,
            date=comment.create_time,
            like_count=comment.like_count,
            ip_location=comment.ip_location,
            replies=replies
        ))

    return api_response.data.has_more

@retry_on_exception(max_retries=3)
async def crawl_comments_from_url(url: str, cookies: List[Dict]) -> Tuple[List[CommentThread], bool]:
    """
    从指定的小红书笔记URL爬取所有评论（包括主评论和子评论）。

    :param url: 小红书笔记URL
    :param cookies: cookies列表
    :return: 包含所有评论信息的列表和登录状态的元组 (评论数据, 是否登录)
    """
    olog.info(f"开始爬取小红书笔记评论: {url}")
    all_comments_data: List[CommentThread] = []
    comment_api_pattern = "/api/sns/web/v2/comment/page"

    # 登录状态检查的API
    login_api_pattern = "/api/sns/web/v2/user/me"
    is_logged_in = False

    async with new_playwright_page(
            cookies=cookies,
            headless=SpiderConfig.HEADLESS,
            use_proxy=SpiderConfig.USE_PROXY,
            no_imgs=SpiderConfig.NO_IMGS
    ) as page:

        # 检查登录状态、导航并获取初始评论数据
        olog.debug("检查登录状态并获取初始评论数据")

        async with page.expect_response(lambda r: login_api_pattern in r.url, timeout=10000) as login_info, \
                page.expect_response(lambda r: comment_api_pattern in r.url, timeout=10000) as comment_info:
            await page.goto(url, wait_until="domcontentloaded")

        # 获取响应
        login_response = await login_info.value
        comment_response = await comment_info.value

        # 检查登录状态
        response_data = await login_response.json()
        data = response_data.get("data", {})
        if data.get("guest") is not True:
            is_logged_in = True
            olog.info("用户已登录")
        else:
            olog.warning("用户处于游客状态，未登录")

        # 处理初始评论数据
        has_more = await process_comment_response(comment_response, all_comments_data)
        if not has_more:
            return all_comments_data, is_logged_in

        # 等待滚动容器出现
        await page.wait_for_selector(NOTE_SCROLLER_SELECTOR, timeout=10000)
        note_scroller = page.locator(NOTE_SCROLLER_SELECTOR)

        olog.debug("开始滚动加载更多评论")

        while True:
            try:
                async with page.expect_response(
                        lambda r: comment_api_pattern in r.url,
                        timeout=3000
                ) as response_info:
                    await smooth_scroll_down(note_scroller)
                response = await response_info.value

                # 处理响应数据并检查是否还有更多数据
                has_more = await process_comment_response(response, all_comments_data)

                # 如果没有更多数据，停止滚动
                if not has_more:
                    olog.info("API响应表示没有更多评论，准备停止滚动")
                    break

            except Exception as e:
                if 'playwright._impl._errors.TimeoutError' in traceback.format_exc():
                    olog.debug("滚动未触发数据加载，将继续滚动")
                else:
                    olog.error(f"评论爬取过程中发生错误: {e}")
                    olog.error(f"错误详情: {traceback.format_exc()}")
                    raise e

    # 去重处理
    olog.info(f"评论爬取完成，共获取 {len(all_comments_data)} 条主评论，，登录状态: {is_logged_in}")
    return all_comments_data, is_logged_in
