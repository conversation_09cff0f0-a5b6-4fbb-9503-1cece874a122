from typing import List, Dict, Any

from pydantic import BaseModel


class XHSNoteImage(BaseModel):
    url: str


class XHSNoteGoodsInfo(BaseModel):
    goods_name: List[str]


class XHSNote(BaseModel):
    id: str
    display_title: str
    time: str
    likes: int
    view_count: int
    comments_count: int
    shared_count: int
    collected_count: int
    sticky: bool
    permission_code: int
    level: int
    type: str
    schedule_post_time: int
    tab_status: int
    xsec_token: str
    xsec_source: str
    goods_info: XHSNoteGoodsInfo
    images_list: List[XHSNoteImage]
    permission_msg: str


class XHSUserPostedNotesData(BaseModel):
    tags: List[Dict[str, Any]]
    page: int
    notes: List[XHSNote]


class XHSUserPostedNotesResponse(BaseModel):
    code: int
    success: bool
    msg: str
    data: XHSUserPostedNotesData
