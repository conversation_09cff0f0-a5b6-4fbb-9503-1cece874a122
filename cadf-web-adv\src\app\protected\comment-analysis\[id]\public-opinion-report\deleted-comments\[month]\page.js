"use client"

import React from 'react';
import { 
  Container, 
  Typography, 
  Card, 
  CardContent, 
  Stack, 
  Grid, 
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Breadcrumbs,
  Tooltip,
  Button
} from '@mui/material';
import { 
  ArrowLeft,
  AlertCircle,
  Trash2,
  Info
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import NextLink from 'next/link';

// 模拟每个月的删评详情数据
const monthlyDeletedComments = {
  '1月': [
    { id: 1, content: "这个产品质量太差了，不推荐购买", time: "2023-01-15 14:23", reason: "负面评价" },
    { id: 2, content: "用了两天就坏了，客服态度还很差", time: "2023-01-14 09:45", reason: "负面评价" },
    { id: 3, content: "竞品的功能更好，价格还便宜", time: "2023-01-13 16:30", reason: "竞品宣传" },
    { id: 4, content: "收到的和图片不一样，差评", time: "2023-01-12 11:20", reason: "负面评价" },
    { id: 5, content: "这是诈骗产品，大家不要上当", time: "2023-01-11 18:05", reason: "恶意评价" },
  ],
  '2月': [
    { id: 1, content: "产品设计不合理，很难用", time: "2023-02-15 14:23", reason: "负面评价" },
    { id: 2, content: "材质很差，一点也不耐用", time: "2023-02-12 09:45", reason: "负面评价" },
    { id: 3, content: "不值这个价格，太贵了", time: "2023-02-10 16:30", reason: "价格投诉" },
  ],
  '3月': [
    { id: 1, content: "这个产品有安全隐患，大家小心", time: "2023-03-22 14:23", reason: "安全问题" },
    { id: 2, content: "使用后出现过敏，完全没有提示", time: "2023-03-19 09:45", reason: "负面评价" },
    { id: 3, content: "广告宣传与实际效果差距太大", time: "2023-03-15 16:30", reason: "虚假宣传" },
    { id: 4, content: "其他品牌的同类产品好用多了", time: "2023-03-05 11:20", reason: "竞品宣传" },
  ],
  '4月': [
    { id: 1, content: "这款产品完全是智商税，没用", time: "2023-04-18 14:23", reason: "负面评价" },
    { id: 2, content: "服务态度差，遇到问题不解决", time: "2023-04-10 09:45", reason: "服务投诉" },
    { id: 3, content: "我朋友用了这个产品后很后悔", time: "2023-04-05 16:30", reason: "负面评价" },
  ],
  '5月': [
    { id: 1, content: "包装很简陋，像假货一样", time: "2023-05-25 14:23", reason: "负面评价" },
    { id: 2, content: "退款流程太复杂，故意刁难顾客", time: "2023-05-18 09:45", reason: "服务投诉" },
  ],
  '6月': [
    { id: 1, content: "产品更新后变得更差了", time: "2023-06-15 14:23", reason: "负面评价" },
    { id: 2, content: "物流太慢，客服还找借口", time: "2023-06-08 09:45", reason: "服务投诉" },
  ],
};

export default function DeletedCommentsDetails() {
  const params = useParams();
  const theme = useTheme();
  const router = useRouter();
  
  // 获取当前月份
  const month = params.month;
  
  // 根据URL参数获取对应的数据
  const commentsData = monthlyDeletedComments[month] || [];
  
  // 如果没有找到对应月份的数据，显示提示信息
  if (commentsData.length === 0) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" color="error" gutterBottom>
          未找到该月份的删评数据
        </Typography>
        <NextLink href={`/protected/comment-analysis/${params.id}/public-opinion-report`} passHref>
          <Typography 
            variant="body1" 
            sx={{ color: theme.palette.primary.main, textDecoration: 'underline', cursor: 'pointer' }}
          >
            返回舆论引导反馈报表
          </Typography>
        </NextLink>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <NextLink href={`/protected/comment-analysis/${params.id}/public-opinion-report`} passHref style={{ textDecoration: 'none', color: theme.palette.text.secondary }}>
          <Typography variant="body2" display="flex" alignItems="center">
            <ArrowLeft size={16} style={{ marginRight: '4px' }} />
            返回舆论引导反馈报表
          </Typography>
        </NextLink>
        <Typography variant="body2" color="text.primary">
          {month}删评详情
        </Typography>
      </Breadcrumbs>
      
      {/* 标题 */}
      <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 4 }}>
        <Trash2 size={28} color={theme.palette.error.main} />
        <Typography variant="h4" component="h1">
          {month}删评详情
        </Typography>
      </Stack>
      
      {/* 统计卡片 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} sx={{ width: '100%' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Stack>
                <Typography variant="body2" color="text.secondary">删评总数</Typography>
                <Typography variant="h4">{commentsData.length}</Typography>
              </Stack>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Stack>
                <Typography variant="body2" color="text.secondary">主要删评原因</Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                  {Array.from(new Set(commentsData.map(item => item.reason))).map((reason) => (
                    <Chip 
                      key={reason}
                      label={reason} 
                      color={reason.includes('负面') ? 'error' : reason.includes('竞品') ? 'warning' : 'default'}
                      size="small"
                    />
                  ))}
                </Box>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* 删评列表 */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <AlertCircle size={20} color={theme.palette.error.main} style={{ marginRight: '8px' }} />
            删除评论列表
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            以下评论已被AI系统自动识别并删除，这些评论可能包含负面、虚假或不当内容
          </Typography>
          
          <List>
            {commentsData.map((comment) => (
              <ListItem 
                key={comment.id} 
                divider 
                sx={{ 
                  py: 2,
                  borderLeft: '4px solid',
                  borderLeftColor: 
                    comment.reason.includes('负面') ? 'error.main' : 
                    comment.reason.includes('竞品') ? 'warning.main' : 
                    comment.reason.includes('服务') ? 'info.main' : 
                    'text.disabled',
                  pl: 2,
                  borderRadius: '4px 0 0 4px',
                }}
              >
                <ListItemText
                  primary={
                    <Typography variant="body1" sx={{ mb: 1 }}>{comment.content}</Typography>
                  }
                  secondary={
                    <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                      <Chip 
                        label={comment.time} 
                        size="small" 
                        sx={{ bgcolor: 'background.paper', color: 'text.secondary' }} 
                      />
                      <Chip 
                        label={comment.reason} 
                        size="small"
                        color={
                          comment.reason.includes('负面') ? 'error' : 
                          comment.reason.includes('竞品') ? 'warning' : 
                          'default'
                        }
                      />
                    </Stack>
                  }
                />
                <ListItemSecondaryAction>
                  <Tooltip title="查看详情">
                    <IconButton edge="end" color="primary">
                      <Info size={18} />
                    </IconButton>
                  </Tooltip>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<Trash2 size={16} />}
              sx={{
                borderRadius: 2,
                textTransform: 'none'
              }}
            >
              下载完整删评记录
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
} 