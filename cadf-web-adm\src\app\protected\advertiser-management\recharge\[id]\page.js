"use client";

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { costRechargeRecordApi } from "@/api/cost-recharge-record-api"; // 确保引入正确的API
import { ArrowLeft } from 'lucide-react';

export default function RechargeAdvertiser() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const advertiserId = params.id;
  const advertiserUsername = searchParams.get('username');

  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  const validateForm = () => {
    const errors = {};
    if (!amount) {
      errors.amount = "充值金额不能为空";
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      errors.amount = "请输入有效的充值金额（必须大于0）";
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleRecharge = async () => {
    if (!validateForm()) {
      return;
    }
    setLoading(true);
    try {
      await costRechargeRecordApi.create({
        cost_user_account_id: advertiserId,
        amount: parseFloat(amount),
        status: "成功", // 默认为成功，后端会处理账户余额
        recharge_method: "手工" // 默认为手工
      });
      dispatch(addAlert({ type: AlertType.SUCCESS, message: '充值成功' }));
      router.push('/protected/advertiser-management'); 
    } catch (error) {
      dispatch(addAlert({
        type: AlertType.ERROR,
        message: error.message || '充值失败'
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Button
        startIcon={<ArrowLeft size={18} />}
        onClick={() => router.back()}
        sx={{ mb: 2 }}
      >
        返回
      </Button>
      <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: 3 }}>
        为广告主充值: {advertiserUsername || advertiserId}
      </Typography>

      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          p: 3,
        }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="充值金额 (元)"
              variant="outlined"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              error={!!formErrors.amount}
              helperText={formErrors.amount}
              InputProps={{
                inputProps: {
                  min: 0.01,
                  step: 0.01
                }
              }}
            />
          </Grid>
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              onClick={handleRecharge}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {loading ? '处理中...' : '确认充值'}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
} 