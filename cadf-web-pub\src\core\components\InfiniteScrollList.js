"use client";

import React from "react";
import { useState, useEffect, useCallback } from "react";
import { useInView } from "react-intersection-observer";
import { Box, Typography, Grid, Button } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { CircularProgress } from "@mui/material";

// 无限滚动钩子
const useInfiniteScroll = ({ loadMore, hasMore = true }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { ref, inView } = useInView({
    threshold: 1,
    rootMargin: "10px",
    triggerOnce: false,
  });

  const handleLoadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      setError(null);
      await loadMore();
    } catch (err) {
      setError(err);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loadMore, loading, hasMore]);

  useEffect(() => {
    if (inView && hasMore && !loading) {
      handleLoadMore();
    }
  }, [inView, hasMore, loading, handleLoadMore]);

  return {
    ref,
    loading,
    error,
    retry: handleLoadMore,
  };
};

// 无限滚动列表组件
const InfiniteScrollList = ({
  items = [],
  renderItem,
  loadMore,
  hasMore,
  gridColumns = {
    xs: 12,
    sm: 6,
    md: 4,
    lg: 3,
  },
}) => {
  const theme = useTheme();
  const { ref, loading, error, retry } = useInfiniteScroll({
    loadMore,
    hasMore,
  });

  // 空状态
  if (items.length === 0 && !loading && !error) {
    return (
      <Box sx={{ 
        textAlign: "center", 
        py: 6,
        px: 4,
        bgcolor: theme.palette.grey[50],
        borderRadius: 3,
        border: `1px solid ${theme.palette.grey[200]}`,
      }}>
        <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
          暂无数据
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%" }}>
      {/* 列表内容 */}
      <Grid container spacing={3}>
        {items.map((item, index) => (
          <Grid key={index} size={gridColumns}>
            {renderItem(item, index)}
          </Grid>
        ))}
      </Grid>

      {/* 加载更多触发器 */}
      {hasMore && (
        <Box
          ref={ref}
          sx={{
            display: "flex",
            justifyContent: "center",
            py: 4,
            width: "100%",
          }}
        >
          {loading && (
            <Box
              sx={{
                textAlign: "center",
                display: "flex",
                alignItems: "center",
                gap: 2,
                px: 3,
                py: 2,
                bgcolor: theme.palette.background.paper,
                borderRadius: 3,
                border: `1px solid ${theme.palette.grey[200]}`,
                boxShadow: theme.shadows[1],
              }}
            >
              <CircularProgress size={20} color="primary" />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                加载中...
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* 错误状态 */}
      {error && (
        <Box sx={{ 
          textAlign: "center", 
          py: 4,
          px: 3,
          bgcolor: 'rgba(255, 59, 48, 0.1)',
          borderRadius: 3,
          border: `1px solid ${theme.palette.error.light}`,
        }}>
          <Typography variant="body2" color="error" sx={{ mb: 2, fontWeight: 500 }}>
            加载失败
          </Typography>
          <Button
            variant="outlined"
            color="error"
            size="small"
            onClick={retry}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            重试
          </Button>
        </Box>
      )}

      {/* 加载完成状态 */}
      {!hasMore && items.length > 0 && (
        <Box sx={{ 
          textAlign: "center", 
          py: 3,
          mt: 2,
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
            没有更多数据了
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default InfiniteScrollList;
