"use client";

import {Box, Grid, Paper, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import SimpleWordCloud from "@/components/SimpleWordCloud";

// 模拟四象限词云数据
const mockWordcloudData = {
    usefulPositive: [
        {text: "舒适", weight: 100},
        {text: "好看", weight: 85},
        {text: "耐用", weight: 75},
        {text: "高端", weight: 70},
        {text: "质感", weight: 65}
    ],
    usefulNegative: [
        {text: "价格高", weight: 90},
        {text: "物流慢", weight: 80},
        {text: "尺寸偏小", weight: 75},
        {text: "接缝处理", weight: 65},
        {text: "色差", weight: 60}
    ],
    uselessPositive: [
        {text: "挺好的", weight: 55},
        {text: "不错", weight: 50},
        {text: "还行", weight: 45},
        {text: "买回来了", weight: 40},
        {text: "日常使用", weight: 35}
    ],
    uselessNegative: [
        {text: "不怎么样", weight: 45},
        {text: "一般般", weight: 40},
        {text: "随便买的", weight: 35},
        {text: "不喜欢", weight: 30},
        {text: "凑合用", weight: 25}
    ]
};

// 词云象限组件
const WordCloudQuadrant = ({ title, words, color = 'primary' }) => {
    const theme = useTheme();
    
    return (
        <Paper
            elevation={0}
            sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                border: `1px solid ${theme.palette.divider}`,
                display: 'flex',
                flexDirection: 'column'
            }}
        >
            <Typography 
                variant="h6" 
                gutterBottom 
                sx={{ 
                    color: theme.palette[color]?.main || theme.palette.text.primary,
                    fontWeight: 'medium',
                    mb: 2
                }}
            >
                {title}
            </Typography>
            <Box sx={{ flex: 1, position: 'relative', width: '100%' }}>
                <SimpleWordCloud
                    words={words.map(item => ({text: item.text, value: item.weight}))}
                />
            </Box>
        </Paper>
    );
};

const WordCloudQuadrantAnalysis = ({ wordcloudData = mockWordcloudData }) => {
    const theme = useTheme();

    return (
        <>
            <Typography variant="h5" fontWeight="medium" sx={{mb: 3}}>
                意图词云四象限分析
            </Typography>

            <Paper
                elevation={0}
                sx={{
                    p: 3,
                    pb: 10,
                    borderRadius: 2,
                    mb: 4,
                    border: `1px solid ${theme.palette.divider}`,
                    overflow: 'visible'
                }}
            >
                <Grid container rowSpacing={6} columnSpacing={4} sx={{width: '100%'}}>
                    {/* 有用+正面 */}
                    <Grid size={{xs: 12, md: 6}} sx={{mb: 4}}>
                        <WordCloudQuadrant 
                            title="有用 + 正面" 
                            words={wordcloudData.usefulPositive}
                            color="success"
                        />
                    </Grid>
                    
                    {/* 有用+负面 */}
                    <Grid size={{xs: 12, md: 6}} sx={{mb: 4}}>
                        <WordCloudQuadrant 
                            title="有用 + 负面" 
                            words={wordcloudData.usefulNegative}
                            color="error"
                        />
                    </Grid>
                    
                    {/* 无用+正面 */}
                    <Grid size={{xs: 12, md: 6}} sx={{mb: {xs: 4, md: 0}}}>
                        <WordCloudQuadrant 
                            title="无用 + 正面" 
                            words={wordcloudData.uselessPositive}
                            color="primary"
                        />
                    </Grid>
                    
                    {/* 无用+负面 */}
                    <Grid size={{xs: 12, md: 6}}>
                        <WordCloudQuadrant 
                            title="无用 + 负面" 
                            words={wordcloudData.uselessNegative}
                            color="warning"
                        />
                    </Grid>
                </Grid>
            </Paper>
        </>
    );
};

export default WordCloudQuadrantAnalysis; 