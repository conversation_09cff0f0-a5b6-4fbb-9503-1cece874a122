"""
小红书账户流量指标爬虫测试例子
"""
import asyncio
import json
from typing import List

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from spider.xhs.xhs_account_traffic_metrics_spider import crawl_account_metrics
from spider.schemas.note_metrics_schema import NoteMetrics
from models.models import Account


async def test_account_traffic_metrics():
    """
    测试小红书账户流量指标爬虫
    """
    try:
        await init_models()
        # 从数据库获取指定ID的账户cookie
        account = await Account.get("68724c33435ce73710ab0bc8")
        if not account or not account.cookie:
            olog.error("未找到ID为123123的账户或该账户没有cookie")
            return
        
        cookies = account.cookie
        olog.info(f"使用账户 {account.name} 的cookie进行测试")
        
        # 调用爬虫函数 - 爬取最近7天的数据
        metrics, is_logged_in = await crawl_account_metrics(
            cookies=cookies,
            days_ago=7
        )
        
        # 打印结果
        olog.info(f"成功获取 {len(metrics)} 条账户流量指标数据，登录状态: {is_logged_in}")
        olog.info("账户流量指标爬取结果:")
        
        # 使用model_dump将metrics转化为JSON
        metrics_dict = [metric.model_dump() for metric in metrics]
        formatted_json = json.dumps(metrics_dict, ensure_ascii=False, indent=2, default=str)
        olog.info(formatted_json)
            
    except Exception as e:
        olog.exception(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_account_traffic_metrics())