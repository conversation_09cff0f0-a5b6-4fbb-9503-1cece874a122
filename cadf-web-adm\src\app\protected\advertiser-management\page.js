"use client";

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Tooltip,
  Button,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  RefreshCw,
  DollarSign,
} from 'lucide-react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { advertiserManagementApi } from "@/api/advertiser-management-api";

export default function AdvertiserManagement() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  
  // 状态管理
  const [advertisers, setAdvertisers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  
  // 对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  const [selectedAdvertiser, setSelectedAdvertiser] = useState(null);

  // 加载广告主列表
  const loadAdvertisers = async (searchQuery = searchTerm) => {
    setLoading(true);
    try {
      const response = await advertiserManagementApi.queryList(
        page + 1, 
        rowsPerPage, 
        searchQuery
      );
      setAdvertisers(response.advertisers);
      setTotal(response.total);
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '加载广告主列表失败' 
      }));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadAdvertisers();
  }, [page, rowsPerPage]);

  // 处理搜索
  const handleSearch = () => {
    setPage(0); // 重置到第一页
    loadAdvertisers(searchTerm);
  };

  // 处理刷新
  const handleRefresh = () => {
    setSearchTerm('');
    setPage(0);
    loadAdvertisers('');
  };

  // 处理分页
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 删除广告主
  const handleDelete = async () => {
    try {
      await advertiserManagementApi.delete(selectedAdvertiser.id_);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.DELETE }));
      setDeleteDialogOpen(false);
      setSelectedAdvertiser(null);
      loadAdvertisers();
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '删除广告主失败' 
      }));
    }
  };

  // 跳转到编辑页面
  const navigateToEdit = (advertiser) => {
    router.push(`/protected/advertiser-management/edit/${advertiser.id_}`);
  };

  // 跳转到创建页面
  const navigateToCreate = () => {
    router.push('/protected/advertiser-management/create');
  };

  // 跳转到充值页面
  const navigateToRecharge = (advertiser) => {
    router.push(`/protected/advertiser-management/recharge/${advertiser.id_}?username=${advertiser.username}`);
  };

  // 打开删除对话框
  const openDeleteDialog = (advertiser) => {
    setSelectedAdvertiser(advertiser);
    setDeleteDialogOpen(true);
  };

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h1" fontWeight="bold">
          广告主管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<Plus size={18} />}
          onClick={navigateToCreate}
        >
          新增广告主
        </Button>
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        {/* 搜索栏 */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TextField
              placeholder="搜索广告主用户名..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              sx={{ width: 300 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={18} />
                  </InputAdornment>
                ),
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
            <Tooltip title="搜索">
              <IconButton
                size="small"
                onClick={handleSearch}
                sx={{ color: 'primary.main' }}
              >
                <Search size={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title="刷新">
              <IconButton
                size="small"
                onClick={handleRefresh}
                sx={{ color: 'text.secondary' }}
              >
                <RefreshCw size={16} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* 表格 */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>用户名</TableCell>
                <TableCell>创建时间</TableCell>
                <TableCell>余额</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center" sx={{ py: 4 }}>
                    加载中...
                  </TableCell>
                </TableRow>
              ) : advertisers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center" sx={{ py: 4 }}>
                    暂无数据
                  </TableCell>
                </TableRow>
              ) : (
                advertisers.map((advertiser) => (
                  <TableRow key={advertiser.id_} hover>
                    <TableCell>{advertiser.username}</TableCell>
                    <TableCell>{formatTime(advertiser.create_at)}</TableCell>
                    <TableCell>{advertiser.balance !== undefined ? advertiser.balance.toFixed(2) : '-'}</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Tooltip title="编辑">
                          <IconButton
                            size="small"
                            onClick={() => navigateToEdit(advertiser)}
                            sx={{ color: 'primary.main' }}
                          >
                            <Edit size={16} />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="充值">
                          <IconButton
                            size="small"
                            onClick={() => navigateToRecharge(advertiser)}
                            sx={{ color: 'success.main' }}
                          >
                            <DollarSign size={16} />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除">
                          <IconButton
                            size="small"
                            onClick={() => openDeleteDialog(advertiser)}
                            sx={{ color: 'error.main' }}
                          >
                            <Trash2 size={16} />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* 分页 */}
        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => 
            `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`} 条`
          }
        />
      </Paper>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除广告主 "{selectedAdvertiser?.username}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button variant="contained" color="error" onClick={handleDelete}>
            删除
          </Button>
        </DialogActions>
      </Dialog>


    </Box>
  );
} 