from omni.llm.output_agent import text_output_handler
from agent.image_tools import download_image_to_temp

PROMPT = """
# 角色
你是一名专业的产品描述员。

# 背景
你收到了多张关于某个产品或服务的图片。

# 任务
根据输入的图片内容，为图片中展示的产品或服务生成一段客观的产品描述。
整合所有图片的信息，生成一段连贯的描述。
描述应准确反映图片内容。

# 返回值
返回一段完整的、客观的产品描述文本。
不要使用markdown格式
"""


async def product_description_recog(image_urls: list[str]) -> str:
    # 将图片下载到临时目录（只处理第一张图片，如需处理多张需要修改output_agent）
    image_path = download_image_to_temp(image_urls[0])

    result = await text_output_handler(
        prompt_template=PROMPT,
        params={},
        llm_name="QWEN_VL_MAX",
        tags=["product_description_recognition"],
        image_path=image_path
    )
    return result
