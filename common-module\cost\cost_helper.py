import time
from typing import Dict, Literal

from models.models import CostUserAccount, CostConsumptionRecord
from omni.log.log import olog


async def check_user_balance(
        user_id: str,
        amount: float
) -> bool:
    """
    检查用户余额是否充足

    Args:
        user_id: 用户ID
        amount: 需要的金额

    Returns:
        bool: 余额是否充足
    """
    olog.info(f"检查用户 {user_id} 余额，需要金额：{amount}")

    # 获取或创建用户账户
    user_account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
    if not user_account:
        user_account = CostUserAccount(
            user_id=user_id, balance=0.0, updated_at=int(time.time())
        )
        await user_account.save()
        olog.info(f"为用户 {user_id} 创建新账户")

    # 检查余额是否充足
    if user_account.balance < amount:
        olog.warning(
            f"用户 {user_id} 余额不足，当前余额：{user_account.balance}，需要：{amount}"
        )
        return False

    olog.info(f"用户 {user_id} 余额充足，当前余额：{user_account.balance}")
    return True


async def deduct_user_balance(
        user_id: str,
        project_name: Literal["图片生成", "文本生成", "流量结算"],
        amount: float,
        description: str = ""
) -> bool:
    """
    扣除用户余额并创建消费记录

    Args:
        user_id: 用户ID
        project_name: 项目名称（中文），可选值：图片生成、文本生成、流量结算
        amount: 消费金额
        description: 消费描述

    Returns:
        bool: 是否成功扣费
    """
    olog.info(f"开始处理用户 {user_id} 消费 {amount} 元，项目：{project_name}")

    # 获取用户账户
    user_account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
    if not user_account:
        olog.error(f"用户 {user_id} 账户不存在")
        return False

    # 扣除余额
    user_account.balance -= amount
    user_account.updated_at = int(time.time())
    await user_account.save()

    # 创建消费记录
    consumption_record = CostConsumptionRecord(
        cost_user_account_id=user_id,
        project_name=project_name,
        amount=amount,
        consumption_type="一次性",
        description=description,
        created_at=int(time.time()),
    )
    await consumption_record.save()

    olog.info(
        f"用户 {user_id} 消费成功，消费金额：{amount}，剩余余额：{user_account.balance}"
    )

    return True
