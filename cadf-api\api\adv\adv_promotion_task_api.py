import time
from collections import Counter
from datetime import datetime
from typing import Dict, Any, List, Set

from beanie import PydanticObjectId

from models.models import (
    AiGeneratedMaterial,
    PromotionTaskDetail,
    Product,
    PromotionTask,
    AiGenerationTask
)
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog


@register_handler('adv_promotion_task_api')
class AdvPromotionTaskApi:
    
    async def _get_available_materials(self, product_ids: List[str]) -> List[Dict[str, Any]]:
        """获取可用素材 - 使用单个管道查询优化性能"""
        # 使用一个大的管道查询一次性完成所有查询和过滤
        pipeline = [
            # 首先从AI生成素材开始
            {
                '$match': {
                    'product_id': {'$in': product_ids},
                    'image_generation_status': '已完成',
                    'text_generation_status': '已完成',
                    'is_deleted': False
                }
            },
            # 左关联查询已锁定的素材
            {
                '$lookup': {
                    'from': 'promotion_task_detail',
                    'let': {'material_id': {'$toString': '$_id'}},
                    'pipeline': [
                        {
                            '$match': {
                                '$expr': {
                                    '$and': [
                                        {'$ne': ['$ai_generated_material_id', None]},
                                        {'$eq': ['$ai_generated_material_id', '$$material_id']}
                                    ]
                                }
                            }
                        },
                        {'$limit': 1}
                    ],
                    'as': 'locked_details'
                }
            },
            # 过滤条件：仅过滤未被锁定的素材
            {
                '$match': {
                    'locked_details': {'$size': 0}  # 未被锁定
                }
            },
            # 清理输出，移除临时字段
            {
                '$project': {
                    'locked_details': 0
                }
            }
        ]

        available_materials = await AiGeneratedMaterial.aggregate(pipeline).to_list()
        olog.debug(f"为产品 {product_ids} 找到 {len(available_materials)}个可用素材")
        return available_materials

    @auth_required(['admin', 'advertiser'])
    async def create(self, data: dict) -> None:
        user_id: str = data.get('user_id')
        product_id: str = data.get('product_id')
        count: int = data.get('count', 0)
        platform: str = data.get('platform')
        end_date_str: str = data.get('end_date')
        olog.info(f"开始创建推广任务，用户ID: {user_id}")

        # 输入验证
        if not product_id:
            raise MException("product_id 不能为空")
        if not platform:
            raise MException("platform 不能为空")
        if not end_date_str:
            raise MException("end_date 不能为空")
        if count <= 0:
            raise MException("count 必须大于0")

        end_timestamp: int = int(time.mktime(time.strptime(end_date_str + " 23:59:59", "%Y-%m-%d %H:%M:%S"))) if end_date_str else None

        # 获取可用素材
        available_materials: List[Dict[str, Any]] = await self._get_available_materials([product_id]) if product_id else []
        
        material_ids_to_use: List[str] = [str(mat['_id']) for mat in available_materials[:count]]

        promotion_task = PromotionTask(
            user_id=user_id,
            product_id=product_id,
            platform=platform,
            end_date=end_timestamp,
            create_at=int(time.time()),
            is_deleted=False
        )

        await promotion_task.insert()
        
        if material_ids_to_use:
            task_details_to_create = [
                PromotionTaskDetail(
                    promotion_task_id=str(promotion_task.id),
                    ai_generated_material_id=material_id
                ) for material_id in material_ids_to_use
            ]
            await PromotionTaskDetail.insert_many(task_details_to_create)
                
        olog.info(f"推广任务创建成功，任务ID: {promotion_task.id}，素材数量: {len(material_ids_to_use)}")

    @auth_required(['admin', 'advertiser'])
    async def delete(self, data: dict) -> None:
        user_id: str = data.get('user_id')
        task_id: str = data.get('task_id')
        olog.info(f"开始删除推广任务，用户ID: {user_id}，任务ID: {task_id}")

        # 输入验证
        if not task_id:
            raise MException("task_id 不能为空")

        # 验证 task_id 格式
        try:
            PydanticObjectId(task_id)
        except Exception as e:
            olog.exception(f"task_id格式验证失败，task_id: {task_id}")
            raise MException("task_id 格式错误")

        try:
            task = await PromotionTask.find_one(
                PromotionTask.id == PydanticObjectId(task_id),
                PromotionTask.user_id == user_id,
                PromotionTask.is_deleted == False
            )
            if not task:
                raise MException(f"未找到 ID 为 {task_id} 的推广任务或已被删除")

            await task.set({PromotionTask.is_deleted: True})
            olog.info(f"推广任务删除成功，任务ID: {task_id}")
        except Exception as e:
            olog.exception(f"删除推广任务失败，任务ID: {task_id}")
            if isinstance(e, MException):
                raise
            raise MException("删除推广任务失败")

    @auth_required(['admin', 'advertiser'])
    async def query_product_overview(self, data: dict) -> PageResponse[Dict[str, Any]]:
        user_id: str = data.get('user_id')
        page: int = data.get('page', 1)
        page_size: int = data.get('page_size', 10)
        search_filter: str = data.get('search')
        olog.info(f"开始查询产品概览，用户ID: {user_id}")

        match_stage = {
            '$match': {
                'user_id': user_id,
                'is_deleted': False
            }
        }
        if search_filter:
            match_stage['$match']['title'] = {'$regex': search_filter}

        pipeline = Product.aggregate([
            match_stage,
            {'$sort': {'create_at': -1}},
            {'$skip': (page - 1) * page_size},
            {'$limit': page_size}
        ])
        paginated_products = await pipeline.to_list()

        # 计算总条数
        count_pipeline = [match_stage, {'$count': 'total'}]
        total_result = await Product.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        result_list = []
        if paginated_products:
            product_ids = [str(p['_id']) for p in paginated_products]
            # 获取可用素材
            available_materials = await self._get_available_materials(product_ids) if product_ids else []
                
            material_counts = Counter(mat['product_id'] for mat in available_materials)

            for product_data in paginated_products:
                product_id_str = str(product_data['_id'])
                signed_image_url = None
                images = product_data.get('images', [])
                if images:
                    image_to_use = next((img for img in images if img.get('order') == 1), images[0])
                    oss_key = image_to_use.get('oss_key')
                    if oss_key:
                        signed_image_url = await oss_client.signed_get_url(oss_key)

                result_list.append({
                    '_id': product_id_str,
                    'title': product_data.get('title'),
                    'image_url': signed_image_url,
                    'material_count': material_counts.get(product_id_str, 0)
                })

            return PageResponse(
                page=page,
                page_size=page_size,
                total=total,
                results=result_list
            )

    @auth_required(['admin', 'advertiser'])
    async def query_promotion_tasks(self, data: dict) -> PageResponse[Dict[str, Any]]:
        user_id: str = data.get('user_id')
        page: int = data.get('page', 1)
        page_size: int = data.get('page_size', 10)
        olog.info(f"开始查询推广任务列表，用户ID: {user_id}")

        match_stage = {
            '$match': {
                'user_id': user_id,
                'is_deleted': False
            }
        }

        pipeline = [
            match_stage,
            {'$sort': {'create_at': -1}},
            {'$skip': (page - 1) * page_size},
            {'$limit': page_size},
            {
                '$lookup': {
                    'from': 'product',
                    'let': {'product_id_str': '$product_id'},
                    'pipeline': [
                        {
                            '$match': {
                                '$expr': {
                                    '$and': [
                                        {'$ne': ['$$product_id_str', None]},
                                        {'$ne': ['$$product_id_str', '']},
                                        {'$eq': [{'$toString': '$_id'}, '$$product_id_str']}
                                    ]
                                }
                            }
                        }
                    ],
                    'as': 'productInfo'
                }
            },
            {'$unwind': {'path': '$productInfo', 'preserveNullAndEmptyArrays': True}},
            {
                '$lookup': {
                    'from': 'promotion_task_detail',
                    'let': {'task_id_str': {'$toString': '$_id'}},
                    'pipeline': [
                        {'$match': {'$expr': {'$eq': ['$promotion_task_id', '$$task_id_str']}}}
                    ],
                    'as': 'details'
                }
            },
        ]

        paginated_tasks = await PromotionTask.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [match_stage, {'$count': 'total'}]
        total_result = await PromotionTask.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        result_list = []
        if paginated_tasks:
            for task_data in paginated_tasks:
                product_info = task_data.get('productInfo', {})

                signed_image_url = None
                if product_info and product_info.get('images'):
                    images = product_info.get('images', [])
                    image_to_use = next((img for img in images if img.get('order') == 1), images[0])
                    oss_key = image_to_use.get('oss_key')
                    if oss_key:
                        signed_image_url = await oss_client.signed_get_url(oss_key)

                details = task_data.get('details', [])
                completed_count = sum(1 for d in details if d.get('validation_status') == '成功')

                result_list.append({
                    '_id': str(task_data['_id']),
                    'productName': product_info.get('title', "未知产品"),
                    'productImage': signed_image_url,
                    'platform': task_data.get('platform'),
                    'taskCount': len(details),
                    'completedCount': completed_count,
                    'createdAt': (datetime.fromtimestamp(int(task_data.get('create_at'))).strftime("%Y-%m-%d %H:%M") if task_data.get('create_at') is not None else None),
                })

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=result_list
        )

    @auth_required(['admin', 'advertiser'])
    async def query_promotion_task_details(self, data: dict) -> PageResponse[Dict[str, Any]]:
        user_id: str = data.get('user_id')
        task_id: str = data.get('task_id')
        page: int = data.get('page', 1)
        page_size: int = data.get('page_size', 5)
        olog.info(f"开始查询推广任务详情，任务ID: {task_id}")

        try:
            task = await PromotionTask.find_one(PromotionTask.id == PydanticObjectId(task_id),
                                                PromotionTask.user_id == user_id,
                                                PromotionTask.is_deleted == False)
        except Exception as e:
            olog.exception(f"查询推广任务失败，任务ID: {task_id}")
            raise MException("查询推广任务失败")
            
        if not task:
            raise MException(f"未找到 ID 为 {task_id} 的推广任务或您无权访问")

        # 在 Beanie 中，关联查询通常使用 lookup。为了正确分页，我们需要对 details 进行分页。
        # promotion_task_id 在 PromotionTaskDetail 中是字符串，需要与task_id匹配
        match_stage = {'$match': {'promotion_task_id': task_id}}

        pipeline = [
            match_stage,
            {'$sort': {'_id': 1}},
            {'$skip': (page - 1) * page_size},
            {'$limit': page_size},
            {
                '$lookup': {
                    'from': 'ai_generated_material',
                    'let': {'material_id_str': '$ai_generated_material_id'},
                    'pipeline': [
                        {
                            '$match': {
                                '$expr': {
                                    '$and': [
                                        {'$ne': ['$$material_id_str', None]},
                                        {'$ne': ['$$material_id_str', '']},
                                        {'$eq': ['$_id', {'$toObjectId': '$$material_id_str'}]}
                                    ]
                                }
                            }
                        }
                    ],
                    'as': 'materialDetails'
                }
            },
            {'$unwind': {'path': '$materialDetails', 'preserveNullAndEmptyArrays': True}}
        ]

        paginated_details = await PromotionTaskDetail.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [match_stage, {'$count': 'total'}]
        total_result = await PromotionTaskDetail.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        material_details_list = []
        if paginated_details:
            for detail_data in paginated_details:
                mat_details = detail_data.get('materialDetails')

                # 确定发布状态
                is_published = bool(detail_data.get('publish_url') and detail_data.get('publish_url').strip())
                material_status = '已发布' if is_published else '待发布'
                published_at = (datetime.fromtimestamp(int(detail_data.get('publish_at'))).strftime("%Y-%m-%d %H:%M:%S") if detail_data.get('publish_at') is not None else None) if is_published else None

                # 获取关联的AI素材信息
                mat_details = detail_data.get('materialDetails')
                first_image_url = None
                if mat_details and mat_details.get('images') and len(mat_details['images']) > 0:
                    first_img_info = mat_details['images'][0]
                    oss_key = first_img_info.get('oss_key')
                    if oss_key:
                        first_image_url = await oss_client.signed_get_url(oss_key)

                material_details_list.append({
                    '_id': str(mat_details['_id']) if mat_details else str(detail_data['_id']),
                    'title': mat_details.get('title') if mat_details else '素材准备中',
                    'image_url': first_image_url,
                    'status': material_status,
                    'publishedAt': published_at,
                    'accepted_at': (datetime.fromtimestamp(int(detail_data.get('accepted_at'))).strftime("%Y-%m-%d %H:%M:%S") if detail_data.get('accepted_at') is not None else None),
                    'publish_url': detail_data.get('publish_url'),
                    'publish_at': (datetime.fromtimestamp(int(detail_data.get('publish_at'))).strftime("%Y-%m-%d %H:%M:%S") if detail_data.get('publish_at') is not None else None),
                    'validation_status': detail_data.get('validation_status'),
                })

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=material_details_list
        )
