import multiprocessing
import subprocess
import time

import uvicorn

from models.models import User
from omni.config.config_loader import config_dict
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.schedule_register import init_scheduler


def _run_uvicorn(options):
    workers = (multiprocessing.cpu_count() * 2) + 1
    command = [
        "gunicorn",
        "omni.api.gateway:app",
        "-w",
        str(workers),
        "-k",
        "uvicorn.workers.UvicornWorker",
        "--bind",
        f"0.0.0.0:{options['port']}",
    ]
    subprocess.run(command)


async def _run_uvicorn_dev(options):
    config = uvicorn.Config(
        "omni.api.gateway:app",
        host="0.0.0.0",
        port=options["port"],
        log_level="debug"
    )
    server = uvicorn.Server(config)
    await server.serve()


async def _start_api_server():
    process_env = config_dict.get('process_env', 'dev')
    port = config_dict.get('server', {}).get('port', 5000)
    timeout = config_dict.get('server', {}).get('timeout', 30)

    options = {
        'port': port,
        'timeout': timeout
    }

    if process_env == 'dev':
        await _run_uvicorn_dev(options)
    else:
        _run_uvicorn(options)


async def _init_admin_user():
    username = config_dict.get('admin_user', {}).get('username', 'admin')
    password = config_dict.get('admin_user', {}).get('password', 'y708sWekpvoRdIpF')
    user = await User.find_one(User.username == username)
    if not user:
        user = User(
            username=username,
            password=password,
            roles=['admin'],
            create_at=int(time.time()),
        )
        await user.insert()


async def start_api_server():
    await init_models()
    await _init_admin_user()
    await init_scheduler()
    await _start_api_server()
