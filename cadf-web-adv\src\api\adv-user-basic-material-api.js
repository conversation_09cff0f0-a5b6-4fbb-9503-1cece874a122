import api from "@/core/api/api";

const RESOURCE = "adv_user_basic_material_api";

export const advUserBasicMaterialApi = {
    create: async (share_url, platform) => {
        return await api({
            resource: RESOURCE,
            method_name: "create",
            data: {
                share_url,
                platform,
            },
        });
    },
    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    },
    queryAll: async (search, page, page_size) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_all",
            data: {
                search,
                page,
                page_size,
            },
        });
    },
    getById: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_one",
            data: {
                _id: id_,
            },
        });
    },
    refetch: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "refetch",
            data: {
                _id: id_,
            },
        });
    },
}