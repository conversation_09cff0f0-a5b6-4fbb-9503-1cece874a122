"use client";

import React, { useState } from 'react';
import { 
  Box, 
  Container, 
  Typography,  
  Paper, 
  Button,
  Chip,
  CircularProgress,
  Card,
  CardContent
} from '@mui/material';
import Grid from '@mui/material/Grid';
import { MessageSquare, Users, FileText } from 'lucide-react';
import { useTheme } from '@mui/material/styles';
import { useRouter } from 'next/navigation';
import ImageCard from '@/components/ImageCard';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// 模拟产品数据
const mockProducts = [
  {
    id: 1,
    name: '简约风格双肩包',
    image: 'https://placehold.co/300x400/e0f2fe/0c4a6e?text=Backpack',
    platform: '小红书',
    comments: 1352,
    date: '2023-05-15',
    category: '箱包'
  },
  {
    id: 2,
    name: '男士休闲运动鞋',
    image: 'https://placehold.co/300x400/f0fdf4/166534?text=SportShoes',
    platform: '抖音',
    comments: 2568,
    date: '2023-05-18',
    category: '鞋类'
  },
  {
    id: 3,
    name: '精致小巧耳环',
    image: 'https://placehold.co/300x400/fef2f2/991b1b?text=Earrings',
    platform: '小红书',
    comments: 987,
    date: '2023-05-20',
    category: '配饰'
  },
  {
    id: 4,
    name: '防晒隔离霜',
    image: 'https://placehold.co/300x400/f5f3ff/4c1d95?text=Sunscreen',
    platform: '小红书',
    comments: 3452,
    date: '2023-05-22',
    category: '护肤'
  },
  {
    id: 5,
    name: '智能手表',
    image: 'https://placehold.co/300x400/f0f9ff/0369a1?text=SmartWatch',
    platform: '抖音',
    comments: 1845,
    date: '2023-05-23',
    category: '电子'
  },
  {
    id: 6,
    name: '有机绿茶',
    image: 'https://placehold.co/300x400/f0fdf4/166534?text=GreenTea',
    platform: '快手',
    comments: 756,
    date: '2023-05-24',
    category: '食品'
  },
  {
    id: 7,
    name: '儿童益智玩具',
    image: 'https://placehold.co/300x400/fff7ed/9a3412?text=Toy',
    platform: '抖音',
    comments: 1298,
    date: '2023-05-25',
    category: '玩具'
  },
  {
    id: 8,
    name: '健身瑜伽垫',
    image: 'https://placehold.co/300x400/fdf4ff/86198f?text=YogaMat',
    platform: '小红书',
    comments: 876,
    date: '2023-05-26',
    category: '运动'
  }
];

// 仪表板数据
const dashboardData = {
  totalComments: 12432,
  intentCustomers: 3245,
  totalDistributions: 8975
};

// 增长趋势数据
const growthData = [
  { month: '1月', comments: 5200, intentCustomers: 1300, distributions: 3800 },
  { month: '2月', comments: 6500, intentCustomers: 1600, distributions: 4200 },
  { month: '3月', comments: 7800, intentCustomers: 1900, distributions: 5100 },
  { month: '4月', comments: 8400, intentCustomers: 2200, distributions: 5800 },
  { month: '5月', comments: 10200, intentCustomers: 2700, distributions: 6500 },
  { month: '6月', comments: 12432, intentCustomers: 3245, distributions: 8975 },
];

export default function CommentAnalysis() {
  const theme = useTheme();
  const router = useRouter();
  const [products, setProducts] = useState(mockProducts);
  const [loading, setLoading] = useState(false);
  
  // 处理产品点击，跳转到评论分析详情页
  const handleProductClick = (productId) => {
    router.push(`/protected/comment-analysis/${productId}`);
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* 产品评论分析标题部分 */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: 2,
          mb: 4,
          bgcolor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <Typography variant="h4" component="h1" sx={{ mb: 1, fontWeight: 600 }}>
          产品评论分析
        </Typography>
        <Typography color="text.secondary" variant="body1">
          选择需要分析的产品，查看详细的评论数据和分析报告
        </Typography>
      </Paper>
      
      {/* 仪表板数据展示 */}
      <Grid container spacing={3} sx={{ width: '100%', mb: 3 }}>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card elevation={0} sx={{ borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ 
                bgcolor: theme.palette.primary.light, 
                borderRadius: '50%', 
                width: 45, 
                height: 45, 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                mr: 2
              }}>
                <MessageSquare color={theme.palette.primary.main} size={24} />
              </Box>
              <Box>
                <Typography color="text.secondary" variant="body2">评论数量</Typography>
                <Typography variant="h5" component="div" fontWeight="600">
                  {dashboardData.totalComments.toLocaleString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card elevation={0} sx={{ borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ 
                bgcolor: theme.palette.success.light, 
                borderRadius: '50%', 
                width: 45, 
                height: 45, 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                mr: 2
              }}>
                <Users color={theme.palette.success.main} size={24} />
              </Box>
              <Box>
                <Typography color="text.secondary" variant="body2">意向客户个数</Typography>
                <Typography variant="h5" component="div" fontWeight="600">
                  {dashboardData.intentCustomers.toLocaleString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card elevation={0} sx={{ borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ 
                bgcolor: theme.palette.info.light, 
                borderRadius: '50%', 
                width: 45, 
                height: 45, 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                mr: 2
              }}>
                <FileText color={theme.palette.info.main} size={24} />
              </Box>
              <Box>
                <Typography color="text.secondary" variant="body2">布评总数</Typography>
                <Typography variant="h5" component="div" fontWeight="600">
                  {dashboardData.totalDistributions.toLocaleString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* 增长趋势图表 */}
      <Card elevation={0} sx={{ mb: 4, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
        <CardContent>
          <Typography variant="h6" component="div" sx={{ mb: 2 }}>
            数据趋势
          </Typography>
          <Box sx={{ height: { xs: 300, md: 400 } }}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={growthData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                <XAxis dataKey="month" tick={{ fill: theme.palette.text.secondary }} />
                <YAxis tick={{ fill: theme.palette.text.secondary }} />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: theme.palette.background.paper,
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 8
                  }}
                  labelStyle={{ color: theme.palette.text.primary, fontWeight: 'bold' }}
                />
                <Legend wrapperStyle={{ paddingTop: 10 }} />
                <defs>
                  <linearGradient id="colorComments" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorIntent" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={theme.palette.success.main} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={theme.palette.success.main} stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorDistributions" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={theme.palette.info.main} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={theme.palette.info.main} stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <Area 
                  type="monotone" 
                  dataKey="comments" 
                  name="评论数量" 
                  stroke={theme.palette.primary.main} 
                  fillOpacity={1} 
                  fill="url(#colorComments)" 
                  activeDot={{ r: 8 }}
                />
                <Area 
                  type="monotone" 
                  dataKey="intentCustomers" 
                  name="意向客户个数" 
                  stroke={theme.palette.success.main} 
                  fillOpacity={1} 
                  fill="url(#colorIntent)"
                />
                <Area 
                  type="monotone" 
                  dataKey="distributions" 
                  name="布评总数" 
                  stroke={theme.palette.info.main} 
                  fillOpacity={1} 
                  fill="url(#colorDistributions)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </Card>
      
      {/* 产品列表 */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" component="div">
              产品列表
            </Typography>
            <Chip 
              label={`共 ${products.length} 个产品`}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          
          <Grid container spacing={3} sx={{ width: '100%' }}>
            {products.map((product) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={product.id}>
                <Box sx={{ height: '100%' }}>
                  <ImageCard
                    image={product.image}
                    onClick={() => handleProductClick(product.id)}
                  >
                    <Box sx={{ p: 2 }}>
                      <Typography variant="subtitle1" component="div" fontWeight="medium" noWrap>
                        {product.name}
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, mb: 1 }}>
                        <Chip 
                          label={product.platform} 
                          size="small" 
                          variant="outlined" 
                          color="primary"
                        />
                        <Typography variant="body2" color="text.secondary">
                          {product.comments} 评论
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          {product.category}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {product.date}
                        </Typography>
                      </Box>
                    </Box>
                  </ImageCard>
                </Box>
              </Grid>
            ))}
          </Grid>
          
          {products.length === 0 && (
            <Paper
              elevation={0}
              sx={{
                p: 6,
                borderRadius: 2,
                textAlign: 'center',
                border: `1px dashed ${theme.palette.divider}`
              }}
            >
              <Typography color="text.secondary">
                未找到匹配的产品，请尝试调整筛选条件
              </Typography>
            </Paper>
          )}
        </>
      )}
    </Container>
  );
}
