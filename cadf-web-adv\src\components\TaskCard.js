import React from 'react';
import {
    Box,
    Card,
    CardContent,
    CardActionArea,
    CardMedia,
    Typography,
    Stack,
    Chip,
    IconButton,
    Badge,
    Divider
} from '@mui/material';
import {
    Link as LinkIcon,
    CheckCircle,
    Clock,
    AlertCircle,
    Launch
} from 'lucide-react';
import {useTheme} from '@mui/material/styles';

const TaskCard = ({
    task,
    onClick,
    onLinkClick
}) => {
    const theme = useTheme();
    
    const defaultImage = 'https://placehold.co/300x400/e0f2fe/0c4a6e?text=NoImage';
    const imageToShow = task?.image_url || defaultImage;
    
    // 获取状态颜色和图标
    const getStatusConfig = (status) => {
        switch (status) {
            case '已发布':
                return {
                    color: 'success',
                    icon: <CheckCircle size={16} />,
                    bgColor: theme.palette.success.main,
                    textColor: 'white'
                };
            case '待发布':
                return {
                    color: 'warning',
                    icon: <Clock size={16} />,
                    bgColor: theme.palette.warning.main,
                    textColor: 'white'
                };
            default:
                return {
                    color: 'default',
                    icon: <AlertCircle size={16} />,
                    bgColor: theme.palette.grey[500],
                    textColor: 'white'
                };
        }
    };
    
    // 获取验证状态配置
    const getValidationConfig = (validationStatus) => {
        switch (validationStatus) {
            case '成功':
                return {
                    color: 'success',
                    variant: 'filled'
                };
            case '失败':
                return {
                    color: 'error',
                    variant: 'filled'
                };
            case '待验证':
                return {
                    color: 'warning',
                    variant: 'outlined'
                };
            default:
                return {
                    color: 'default',
                    variant: 'outlined'
                };
        }
    };
    
    const statusConfig = getStatusConfig(task?.status);
    const validationConfig = getValidationConfig(task?.validation_status);
    const hasPublishUrl = task?.publish_url && task?.publish_url.trim();
    
    return (
        <Card 
            sx={{
                width: '100%',
                height: '100%',
                boxShadow: theme.shadows[2],
                borderRadius: 3,
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                border: `1px solid ${theme.palette.grey[200]}`,
                bgcolor: theme.palette.background.paper,
                transition: 'all 0.3s ease',
                '&:hover': {
                    boxShadow: theme.shadows[8],
                    transform: 'translateY(-4px)',
                    borderColor: theme.palette.primary.main,
                }
            }}
        >
            {/* 状态标签 - 左上角 */}
            <Box
                sx={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    zIndex: 10,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    bgcolor: statusConfig.bgColor,
                    color: statusConfig.textColor,
                    px: 1,
                    py: 0.5,
                    borderRadius: 1,
                    fontSize: '0.75rem',
                    fontWeight: 'bold',
                    boxShadow: theme.shadows[2]
                }}
            >
                {statusConfig.icon}
                <Typography variant="caption" color="inherit" fontWeight="bold">
                    {task?.status || '未知'}
                </Typography>
            </Box>
            
            {/* 发布链接按钮 - 右上角 */}
            {hasPublishUrl && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        zIndex: 10,
                    }}
                >
                    <Badge
                        badgeContent={<Launch size={10} />}
                        color="primary"
                        sx={{
                            '& .MuiBadge-badge': {
                                bgcolor: theme.palette.primary.main,
                                color: 'white',
                                minWidth: 16,
                                height: 16,
                                borderRadius: '50%'
                            }
                        }}
                    >
                        <IconButton
                            size="small"
                            onClick={(e) => {
                                e.stopPropagation();
                                if (onLinkClick) {
                                    onLinkClick(task.publish_url);
                                } else {
                                    window.open(task.publish_url, '_blank');
                                }
                            }}
                            sx={{
                                bgcolor: 'rgba(255, 255, 255, 0.9)',
                                color: theme.palette.primary.main,
                                '&:hover': {
                                    bgcolor: 'white',
                                    transform: 'scale(1.1)',
                                },
                                boxShadow: theme.shadows[2]
                            }}
                        >
                            <LinkIcon size={16} />
                        </IconButton>
                    </Badge>
                </Box>
            )}
            
            {/* 图片部分 - 缩小尺寸 */}
            <CardActionArea 
                onClick={onClick}
                sx={{
                    position: 'relative',
                    '&:hover .MuiCardActionArea-focusHighlight': {
                        opacity: 0.04,
                    }
                }}
            >
                <Box sx={{
                    width: '100%',
                    paddingTop: '40%', // 大幅缩小图片高度比例
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: '12px 12px 0 0',
                }}>
                    <CardMedia
                        component="img"
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover', // 改为cover以更好适应较小空间
                            transition: 'transform 0.3s ease',
                        }}
                        image={imageToShow}
                        alt={task?.title || '任务图片'}
                    />
                </Box>
            </CardActionArea>
            
            {/* 内容部分 - 突出状态信息 */}
            <CardContent sx={{ flexGrow: 1, p: 2 }}>
                <Stack spacing={2}>
                    {/* 标题 */}
                    <Typography 
                        variant="subtitle2" 
                        fontWeight="medium" 
                        noWrap
                        sx={{ 
                            fontSize: '0.875rem',
                            lineHeight: 1.2,
                            color: 'text.secondary'
                        }}
                    >
                        {task?.title || '无标题'}
                    </Typography>
                    
                    {/* 主要状态区域 - 更突出 */}
                    <Box sx={{ 
                        display: 'flex', 
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: 2
                    }}>
                        {/* 大号状态显示 */}
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                bgcolor: statusConfig.bgColor,
                                color: statusConfig.textColor,
                                px: 2,
                                py: 1,
                                borderRadius: 2,
                                fontSize: '0.875rem',
                                fontWeight: 'bold',
                                boxShadow: theme.shadows[1],
                                minWidth: 100,
                                justifyContent: 'center'
                            }}
                        >
                            {statusConfig.icon}
                            <Typography variant="body2" color="inherit" fontWeight="bold">
                                {task?.status || '未知'}
                            </Typography>
                        </Box>
                        
                        {/* 验证状态 */}
                        {task?.validation_status && (
                            <Chip
                                label={task.validation_status}
                                size="medium"
                                color={validationConfig.color}
                                variant={validationConfig.variant}
                                sx={{
                                    fontWeight: 'bold',
                                    minWidth: 90,
                                    fontSize: '0.8rem'
                                }}
                            />
                        )}
                    </Box>
                    
                    {/* 发布链接 - 更突出 */}
                    {hasPublishUrl && (
                        <Box
                            sx={{
                                p: 1.5,
                                bgcolor: theme.palette.primary.main + '15',
                                borderRadius: 2,
                                border: `2px solid ${theme.palette.primary.main}40`,
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    bgcolor: theme.palette.primary.main + '25',
                                    borderColor: theme.palette.primary.main + '60',
                                    transform: 'translateY(-1px)',
                                    boxShadow: theme.shadows[2]
                                }
                            }}
                            onClick={(e) => {
                                e.stopPropagation();
                                if (onLinkClick) {
                                    onLinkClick(task.publish_url);
                                } else {
                                    window.open(task.publish_url, '_blank');
                                }
                            }}
                        >
                            <Stack direction="row" spacing={1} alignItems="center">
                                <LinkIcon size={16} color={theme.palette.primary.main} />
                                <Typography
                                    variant="body2"
                                    color="primary.main"
                                    fontWeight="bold"
                                    sx={{ 
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        flexGrow: 1
                                    }}
                                >
                                    查看发布内容
                                </Typography>
                                <Launch size={14} color={theme.palette.primary.main} />
                            </Stack>
                        </Box>
                    )}
                    
                    {/* 时间信息 - 缩小 */}
                    <Stack spacing={0.5}>
                        {task?.accepted_at && (
                            <Typography variant="caption" color="text.secondary" fontSize="0.7rem">
                                接取: {task.accepted_at}
                            </Typography>
                        )}
                        {task?.publish_at && (
                            <Typography variant="caption" color="text.secondary" fontSize="0.7rem">
                                发布: {task.publish_at}
                            </Typography>
                        )}
                    </Stack>
                </Stack>
            </CardContent>
        </Card>
    );
};

export default TaskCard;