"use client";

import {useState, useEffect, useCallback} from 'react';
import {
    Box, Button, Card, CardContent, Chip, IconButton, Pagination, Paper, Stack, Table, TableBody, 
    TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, 
    useMediaQuery, useTheme, CircularProgress, Alert, Tabs, Tab, FormControl, 
    InputLabel, Select, MenuItem
} from '@mui/material';
import {
    ArrowLeft, UserCheck, UserX, Clock, AlertTriangle, 
    DollarSign, Calendar, Wifi, WifiOff
} from 'lucide-react';
import {useRouter, useSearchParams} from 'next/navigation';
import { pubCrewManagementApi } from '@/api/pub-crew-management-api';

const STATUS_OPTIONS = [
    { value: 'all', label: '全部', color: 'default' },
    { value: 'active', label: '活跃', color: 'success' },
    { value: 'inactive', label: '不活跃', color: 'error' },
    { value: 'online', label: '在线', color: 'primary' },
    { value: 'offline', label: '离线', color: 'default' },
];

export default function CrewManagementUnified() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    
    const [crewMembers, setCrewMembers] = useState([]);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all');
    const rowsPerPage = 10;

    const fetchCrewMembers = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await pubCrewManagementApi.queryCrewListUnified(statusFilter, page, rowsPerPage);
            
            if (response && response.results) {
                setCrewMembers(response.results);
                setTotalCount(response.total || 0);
            } else {
                setCrewMembers([]);
                setTotalCount(0);
            }
        } catch (err) {
            console.error("获取舰员失败:", err);
            setError(err.message || '加载数据时发生错误');
            setCrewMembers([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    }, [page, statusFilter, rowsPerPage]);

    useEffect(() => {
        fetchCrewMembers();
    }, [fetchCrewMembers]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handleStatusChange = (event, newValue) => {
        setStatusFilter(newValue);
        setPage(1); // 重置页码
    };

    const getActivityStatusChip = (member) => {
        const isActive = member.activity_status === '活跃';
        return (
            <Chip 
                label={isActive ? "活跃" : "不活跃"} 
                color={isActive ? "success" : "error"} 
                size="small" 
                variant="outlined"
            />
        );
    };

    const getLoginStatusChip = (member) => {
        const status = member.login_status;
        if (status === '在线') {
            return <Chip label="在线" color="primary" size="small" />;
        } else if (status === '离线') {
            return <Chip label="离线" color="default" size="small" />;
        } else if (status === '封禁') {
            return <Chip label="封禁" color="error" size="small" />;
        } else {
            return <Chip label="未知" color="default" size="small" />;
        }
    };

    const getStatusIcon = (filterType) => {
        switch (filterType) {
            case 'active':
                return <UserCheck size={16} />;
            case 'inactive':
                return <UserX size={16} />;
            case 'online':
                return <Wifi size={16} />;
            case 'offline':
                return <WifiOff size={16} />;
            default:
                return null;
        }
    };

    const getLastLoginDuration = (lastLoginStr) => {
        if (!lastLoginStr) return '未知';
        try {
            const parts = lastLoginStr.split(/[- :]/);
            if (parts.length < 6) return '无效日期';
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1;
            const day = parseInt(parts[2], 10);
            const hour = parseInt(parts[3], 10);
            const minute = parseInt(parts[4], 10);
            const second = parseInt(parts[5], 10);
            
            const lastDate = new Date(year, month, day, hour, minute, second);
            if (isNaN(lastDate.getTime())) {
                return '无效日期';
            }

            const now = new Date();
            const diffTime = now.getTime() - lastDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) return '未来时间?';
            if (diffDays === 0) return '今天';
            if (diffDays === 1) return '昨天';
            if (diffDays < 7) return `${diffDays}天前`;
            if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
            if (diffDays < 365) return `${Math.floor(diffDays / 30)}月前`;
            return `${Math.floor(diffDays / 365)}年前`;
        } catch (e) {
            console.error("解析日期失败:", lastLoginStr, e);
            return '解析错误';
        }
    };

    const filteredCrewMembers = crewMembers;

    return (
        <Box sx={{maxWidth: '100%', mb: 4, px: {xs: 1, sm: 2, md: 3}}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <IconButton 
                    onClick={() => router.back()}
                    sx={{mr: 1}}
                    color="primary"
                >
                    <ArrowLeft />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{
                    fontWeight: 500, 
                    fontSize: {xs: '1.25rem', sm: '1.5rem', md: '2rem'}
                }}>
                    舰员管理 ({loading ? '...' : totalCount})
                </Typography>
            </Box>

            <Card sx={{mb: 4}}>
                <CardContent sx={{p: {xs: 1.5, sm: 2}}}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                        统一管理所有舰员信息，支持按不同状态筛选查看舰员列表。
                    </Typography>

                    {/* 状态筛选 */}
                    <Box sx={{ mb: 3 }}>
                        <Tabs
                            value={statusFilter}
                            onChange={handleStatusChange}
                            variant="scrollable"
                            scrollButtons="auto"
                            sx={{
                                '& .MuiTab-root': {
                                    minWidth: {xs: '60px', sm: 'auto'},
                                    px: {xs: 1, sm: 2},
                                    py: 1,
                                    fontSize: {xs: '0.75rem', sm: '0.875rem'}
                                }
                            }}
                        >
                            {STATUS_OPTIONS.map((option) => (
                                <Tab
                                    key={option.value}
                                    label={
                                        <Box sx={{display: 'flex', alignItems: 'center', gap: {xs: 0.5, sm: 1}}}>
                                            {getStatusIcon(option.value)}
                                            <Box sx={{ display: {xs: 'none', sm: 'block'} }}>
                                                {option.label}
                                            </Box>
                                            <Box sx={{ display: {xs: 'block', sm: 'none'} }}>
                                                {option.label.length > 2 ? option.label.slice(0, 2) : option.label}
                                            </Box>
                                        </Box>
                                    }
                                    value={option.value}
                                />
                            ))}
                        </Tabs>
                    </Box>

                    {loading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                            <CircularProgress />
                        </Box>
                    )}

                    {error && (
                        <Alert severity="error" sx={{ my: 2 }}>{error}</Alert>
                    )}

                    {!loading && !error && (
                        <>
                            {isMobile ? (
                                <Box>
                                    {filteredCrewMembers.length > 0 ? (
                                        filteredCrewMembers.map((member) => (
                                            <Card 
                                                key={member._id}
                                                sx={{
                                                    mb: 2,
                                                    borderRadius: 2,
                                                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                                                    transition: 'transform 0.2s ease, box-shadow 0.3s ease',
                                                    '&:hover': {
                                                        transform: 'translateY(-2px)',
                                                        boxShadow: '0 6px 16px rgba(0,0,0,0.08)'
                                                    },
                                                    overflow: 'hidden',
                                                    border: '1px solid rgba(0,0,0,0.08)'
                                                }}
                                            >
                                                <Box sx={{
                                                    p: 0.5,
                                                    background: statusFilter === 'active' ? theme.palette.success.main :
                                                                statusFilter === 'inactive' ? theme.palette.error.main :
                                                                statusFilter === 'online' ? theme.palette.success.main :
                                                                theme.palette.grey[400],
                                                    height: 4
                                                }} />
                                                <CardContent sx={{p: 2}}>
                                                    <Box sx={{ mb: 1.5 }}>
                                                        <Typography 
                                                            variant="subtitle1" 
                                                            sx={{
                                                                fontWeight: 600,
                                                                fontSize: '1.1rem',
                                                                color: theme.palette.text.primary,
                                                                mb: 1
                                                            }}
                                                        >
                                                            {member.user_account}
                                                        </Typography>
                                                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                                                            {getActivityStatusChip(member)}
                                                            {getLoginStatusChip(member)}
                                                        </Box>
                                                    </Box>
                                                    
                                                    <Box sx={{
                                                        display: 'flex', 
                                                        flexDirection: 'column', 
                                                        gap: 1.2,
                                                        background: 'rgba(0,0,0,0.02)',
                                                        p: 1.5,
                                                        borderRadius: 1.5,
                                                        mb: 1
                                                    }}>
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center'
                                                        }}>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.secondary,
                                                                fontWeight: 500,
                                                                minWidth: '80px'
                                                            }}>
                                                                平台账号:
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: theme.palette.text.primary,
                                                                ml: 1
                                                            }}>
                                                                {member.platform_account || '未设置'}
                                                            </Typography>
                                                        </Box>
                                                        
                                                        {member.last_login && (
                                                            <Box sx={{
                                                                display: 'flex',
                                                                alignItems: 'center'
                                                            }}>
                                                                <Typography variant="body2" sx={{
                                                                    color: theme.palette.text.secondary,
                                                                    fontWeight: 500,
                                                                    minWidth: '80px'
                                                                }}>
                                                                    最后登录:
                                                                </Typography>
                                                                <Box sx={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    ml: 1
                                                                }}>
                                                                    <Clock size={14} style={{marginRight: '4px'}} color={theme.palette.text.secondary}/>
                                                                    <Typography variant="body2" sx={{
                                                                        color: theme.palette.text.primary
                                                                    }}>
                                                                        {member.last_login}
                                                                    </Typography>
                                                                </Box>
                                                            </Box>
                                                        )}
                                                    </Box>
                                                    
                                                    {member.last_login && (
                                                        <Typography 
                                                            variant="caption" 
                                                            sx={{
                                                                display: 'inline-block',
                                                                color: theme.palette.text.secondary,
                                                                background: theme.palette.grey[100],
                                                                py: 0.5,
                                                                px: 1,
                                                                borderRadius: 5,
                                                                fontWeight: 500
                                                            }}
                                                        >
                                                            {getLastLoginDuration(member.last_login)} 前登录
                                                        </Typography>
                                                    )}
                                                </CardContent>
                                            </Card>
                                        ))
                                    ) : (
                                        <Card variant="outlined" sx={{
                                            p: 3, 
                                            textAlign: 'center',
                                            borderRadius: 2,
                                            borderStyle: 'dashed',
                                            background: 'rgba(0,0,0,0.02)'
                                        }}>
                                            <AlertTriangle size={40} color="#FF4842" style={{marginBottom: '16px', opacity: 0.6}} />
                                            <Typography>没有找到匹配的舰员</Typography>
                                        </Card>
                                    )}
                                </Box>
                            ) : (
                                <TableContainer component={Paper} variant="outlined" sx={{overflowX: 'auto'}}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>用户账号</TableCell>
                                                <TableCell>平台账号</TableCell>
                                                <TableCell>活跃状态</TableCell>
                                                <TableCell>在线状态</TableCell>
                                                <TableCell>最后登录时间</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {filteredCrewMembers.length > 0 ? (
                                                filteredCrewMembers.map((member) => (
                                                    <TableRow key={member._id} hover>
                                                        <TableCell>{member.user_account}</TableCell>
                                                        <TableCell>{member.platform_account || '未设置'}</TableCell>
                                                        <TableCell>
                                                            {getActivityStatusChip(member)}
                                                        </TableCell>
                                                        <TableCell>
                                                            {getLoginStatusChip(member)}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                                {member.last_login && (
                                                                    <Clock size={16} color={theme.palette.text.secondary} style={{marginRight: '8px'}} />
                                                                )}
                                                                {member.last_login || '未知'}
                                                                {member.last_login && (
                                                                    <Typography variant="caption" sx={{ml: 1, color: 'text.secondary'}}>
                                                                        ({getLastLoginDuration(member.last_login)})
                                                                    </Typography>
                                                                )}
                                                            </Box>
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <TableRow>
                                                    <TableCell colSpan={5} align="center">
                                                        没有找到匹配的舰员
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            )}

                            {totalCount > rowsPerPage && (
                                <Stack spacing={2} sx={{mt: 3, alignItems: 'center'}}>
                                    <Pagination
                                        count={Math.max(1, Math.ceil(totalCount / rowsPerPage))}
                                        page={page}
                                        onChange={handlePageChange}
                                        color="primary"
                                        size={isMobile ? "small" : "medium"}
                                        disabled={loading}
                                    />
                                </Stack>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
} 