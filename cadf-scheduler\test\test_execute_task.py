"""
测试 product_traffic_metrics_scheduler 中 execute_task 函数的简单调用方法
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from omni.mongo.mongo_client import init_models
from scheduler.traffic.product_traffic_metrics_scheduler import execute_task


async def test_execute_task():
    """
    测试 product_traffic_metrics_scheduler 中的 execute_task 函数的简单调用
    """
    print("开始测试产品流量统计 execute_task 函数...")

    try:
        # 初始化数据库模型
        await init_models()
        print("数据库模型初始化完成")

        # 调用 execute_task 函数
        await execute_task()
        print("产品流量统计 execute_task 函数执行完成")

    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        raise


async def main():
    """
    主函数
    """
    await test_execute_task()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
