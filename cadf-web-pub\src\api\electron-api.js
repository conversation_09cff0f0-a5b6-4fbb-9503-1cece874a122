import {invokeElectron, isElectron} from "@/core/tools/electron-bridge";

async function checkElectronEnvironment() {
    if (!isElectron()) {
        return {
            success: false,
            message: '请在桌面客户端中使用此功能'
        };
    }
    return {
        success: true
    };
}

async function invokeElectronCommand(command, failureMessagePrefix, ...args) {
    const envCheck = await checkElectronEnvironment();
    if (!envCheck.success) {
        return {
            success: false,
            message: envCheck.message
        };
    }

    try {
        const result = await invokeElectron(command, ...args);
        console.log(`调用 ${command} 结果:`, result);

        if (result.success) {
            return result;
        } else {
            const errorMessage = `${failureMessagePrefix}: ${result.message || '未知错误'}`;
            console.error(errorMessage);
            return {
                success: false,
                message: errorMessage
            };
        }
    } catch (error) {
        const errorMessage = `与 Electron 通信失败 (${command}): ${error.message}`;
        console.error(errorMessage, error);
        return {
            success: false,
            message: errorMessage
        };
    }
}

async function handleOpenLoginWindow(url) {
    if (!url) {
        console.error("handleOpenLoginWindow requires a URL parameter.");
        return {
            success: false,
            message: "缺少登录 URL"
        };
    }
    console.log(`Calling open-login-window with URL: ${url}`);
    return await invokeElectronCommand("open-login-window", "打开登录窗口失败", url);
}

async function handleGetCookiesAndClose() {
    const result = await invokeElectronCommand("get-cookies-and-close", "获取 Cookie 失败");
    if (result?.success) {
        return result.cookies ?? null;
    }
    return null;
}

export {handleOpenLoginWindow, handleGetCookiesAndClose, checkElectronEnvironment};
