"use client";

import SidebarLink from "@/app/protected/components/SidebarLink";
import {LOGIN_PATH, PROJECT_DESCRIPTION, PROJECT_NAME} from "@/config";
import {Box, Divider, Drawer, IconButton, List, ListItemButton, ListItemIcon, ListItemText, useMediaQuery} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Cookies from 'js-cookie';
import {BarChart, Bot, Brain, CreditCard, FileImage, LogOut, Menu, Share2, ShoppingBag} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {useState} from 'react';

const drawerWidth = 240;

export default function ProtectedLayout({children}) {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [mobileOpen, setMobileOpen] = useState(false);
    const router = useRouter();

    const handleDrawerToggle = () => {
        setMobileOpen(!mobileOpen);
    };

    const handleLogout = () => {
        Cookies.remove('access_token');
        router.push(LOGIN_PATH);
    };

    const menuItems = [
        {text: '流量中心', icon: <BarChart size={20}/>, path: '/protected/product-traffic'},
        {text: '云控评', icon: <BarChart size={20}/>, path: '/protected/comment-analysis'},
        {text: '产品库', icon: <ShoppingBag size={20}/>, path: '/protected/product-management'},
        {text: '用户素材库', icon: <FileImage size={20}/>, path: '/protected/user-base-material-management'},
        {text: 'AI图文生成', icon: <Bot size={20}/>, path: '/protected/ai-image-text-generation'},
        {text: '产品推广', icon: <Share2 size={20}/>, path: '/protected/promotion-task'},
        {text: '费用与账单', icon: <CreditCard size={20}/>, path: '/protected/billing-management'},
    ];

    const drawer = (
        <Box sx={{overflow: 'auto', height: '100%', display: 'flex', flexDirection: 'column', bgcolor: theme.palette.background.paper}}>
            <Box
                sx={{
                    py: 3,
                    px: 3,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderBottom: `1px solid ${theme.palette.grey[200]}`,
                    color: theme.palette.text.primary
                }}
            >
                <Brain
                    size={32}
                    color={theme.palette.primary.main}
                    style={{marginRight: '16px'}}
                />
                <Box>
                    <Typography
                        variant="subtitle1"
                        component="div"
                        sx={{
                            fontWeight: 500,
                            lineHeight: 1.2,
                            fontSize: '1rem',
                            color: theme.palette.text.primary
                        }}
                    >
                        {PROJECT_NAME}
                    </Typography>
                    <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                            fontSize: '0.75rem',
                            display: 'block',
                            mt: 0.5,
                            color: theme.palette.text.secondary
                        }}
                    >
                        {PROJECT_DESCRIPTION}
                    </Typography>
                </Box>
            </Box>
            <List sx={{flexGrow: 1, py: 2}}>
                {menuItems.map((item) => (
                    <SidebarLink
                        key={item.text}
                        item={item}
                        onClick={isMobile ? handleDrawerToggle : undefined}
                    />
                ))}
            </List>
            <Box sx={{p: 3}}>
                <ListItemButton
                    sx={{
                        p: 2,
                        borderRadius: 3,
                        border: `1px solid ${theme.palette.grey[300]}`,
                        bgcolor: theme.palette.background.default,
                        '&:hover': {
                            backgroundColor: theme.palette.error.light,
                            color: theme.palette.error.contrastText,
                            borderColor: theme.palette.error.main,
                            '& .MuiListItemIcon-root': {
                                color: theme.palette.error.contrastText,
                            },
                        },
                        transition: 'all 0.3s ease',
                    }}
                    onClick={handleLogout}
                >
                    <ListItemIcon>
                        <LogOut size={20} color={theme.palette.text.secondary}/>
                    </ListItemIcon>
                    <ListItemText
                        primary="退出登录"
                        primaryTypographyProps={{
                            fontSize: '0.95rem',
                            fontWeight: 500,
                        }}
                    />
                </ListItemButton>
            </Box>
        </Box>
    );

    return (
        <Box sx={{display: 'flex'}}>
            {/* 移动设备菜单按钮 */}
            {isMobile && (
                <Box sx={{position: 'fixed', top: 16, left: 16, zIndex: 1200}}>
                    <IconButton
                        color="primary"
                        aria-label="打开菜单"
                        edge="start"
                        onClick={handleDrawerToggle}
                        sx={{
                            bgcolor: theme.palette.background.paper,
                            boxShadow: theme.shadows[2],
                            borderRadius: 2,
                            width: 48,
                            height: 48,
                            '&:hover': {
                                bgcolor: theme.palette.background.paper,
                                boxShadow: theme.shadows[4],
                            },
                            transition: 'all 0.2s ease',
                        }}
                    >
                        <Menu/>
                    </IconButton>
                </Box>
            )}

            <Box
                component="nav"
                sx={{width: {md: drawerWidth}, flexShrink: {md: 0}}}
            >
                {/* 移动设备抽屉 */}
                <Drawer
                    variant="temporary"
                    open={mobileOpen}
                    onClose={handleDrawerToggle}
                    ModalProps={{
                        keepMounted: true, // 提高移动设备上的性能
                    }}
                    sx={{
                        display: {xs: 'block', md: 'none'},
                        '& .MuiDrawer-paper': {
                            boxSizing: 'border-box',
                            width: drawerWidth,
                            borderRadius: '0 16px 16px 0',
                            border: 'none',
                            boxShadow: theme.shadows[8],
                        },
                    }}
                >
                    {drawer}
                </Drawer>

                {/* 桌面端抽屉 */}
                <Drawer
                    variant="permanent"
                    sx={{
                        display: {xs: 'none', md: 'block'},
                        '& .MuiDrawer-paper': {
                            boxSizing: 'border-box',
                            width: drawerWidth,
                            borderRight: `1px solid ${theme.palette.grey[200]}`,
                            boxShadow: 'none',
                        },
                    }}
                    open
                >
                    {drawer}
                </Drawer>
            </Box>

            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: {xs: 2, sm: 3, md: 4},
                    width: {md: `calc(100% - ${drawerWidth}px)`},
                    minHeight: '100vh',
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: theme.palette.background.default,
                }}
            >
                <Box sx={{
                    flexGrow: 1,
                    maxWidth: '1200px',
                    mx: 'auto',
                    width: '100%'
                }}>
                    {children}
                </Box>
            </Box>
        </Box>
    );
} 