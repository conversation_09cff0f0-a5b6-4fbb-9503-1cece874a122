"""化妆品图片生成工作流 LangGraph 实现"""

from langgraph.graph import StateGraph, END

from omni.log.log import olog
from agent.node.add_caption_with_position_node import add_caption_with_position
from agent.node.classify_image_node import classify_image
from agent.node.clone_compliant_character_node import clone_compliant_character
from agent.node.clone_image_node import clone_image
from agent.node.generate_caption_node import generate_caption
from agent.node.generate_image_from_prompt_node import generate_image_from_prompt
from agent.node.generate_portrait_prompt_node import generate_portrait_prompt
from agent.node.identify_elements_node import identify_elements
from agent.node.identify_scene_products_node import identify_scene_products
from agent.node.identify_product_image_node import identify_product_image
from agent.node.ocr_image_text_node import ocr_image_text
from agent.node.replace_product_node import replace_product
from agent.state import InputState, OutputState, OverallState


def create_cosmetic_image_gen_graph():
    """创建化妆品图片生成工作流图"""
    olog.info("开始创建化妆品图片生成工作流图")

    # 创建状态图
    builder = StateGraph(OverallState, input_schema=InputState, output_schema=OutputState)

    # 添加节点
    builder.add_node("classify_image", classify_image)
    builder.add_node("identify_scene_products", identify_scene_products)
    builder.add_node("identify_product_image", identify_product_image)
    builder.add_node("replace_product", replace_product)
    builder.add_node("clone_compliant_character", clone_compliant_character)
    builder.add_node("clone_image", clone_image)
    builder.add_node("identify_elements", identify_elements)
    builder.add_node("generate_portrait_prompt", generate_portrait_prompt)
    builder.add_node("generate_image_from_prompt", generate_image_from_prompt)
    builder.add_node("ocr_image_text", ocr_image_text)
    builder.add_node("generate_caption", generate_caption)
    builder.add_node("add_caption_with_position", add_caption_with_position)

    # 设置入口点
    builder.set_entry_point("classify_image")

    # 添加条件边
    builder.add_conditional_edges(
        "classify_image",
        lambda state: "identify_scene_products" if (state.image_category or "其他") == "美妆产品"
        else "clone_compliant_character" if (state.image_category or "其他") == "人像"
        else "clone_image",
        {
            "identify_scene_products": "identify_scene_products",
            "clone_compliant_character": "clone_compliant_character",
            "clone_image": "clone_image"
        }
    )

    # 美妆产品处理流程
    builder.add_edge("identify_scene_products", "identify_product_image")
    builder.add_edge("identify_product_image", "replace_product")
    builder.add_edge("replace_product", "ocr_image_text")

    # 人像处理流程
    builder.add_conditional_edges(
        "clone_compliant_character",
        lambda state: "identify_elements" if (state.security_blocked or False) else "ocr_image_text",
        {
            "identify_elements": "identify_elements",
            "ocr_image_text": "ocr_image_text"
        }
    )

    # 元素识别和图片生成流程
    builder.add_edge("identify_elements", "generate_portrait_prompt")
    builder.add_edge("generate_portrait_prompt", "generate_image_from_prompt")
    builder.add_edge("generate_image_from_prompt", "ocr_image_text")

    # 其他类型图片处理流程
    builder.add_edge("clone_image", "ocr_image_text")

    # OCR和配文生成流程
    builder.add_conditional_edges(
        "ocr_image_text",
        lambda state: "generate_caption" if (state.has_text or False) else END,
        {
            "generate_caption": "generate_caption",
            END: END
        }
    )

    builder.add_edge("generate_caption", "add_caption_with_position")
    builder.add_edge("add_caption_with_position", END)

    # 编译图
    graph = builder.compile()

    olog.info("化妆品图片生成工作流图创建完成")

    return graph


# 创建图实例
graph = create_cosmetic_image_gen_graph()
