"use client";

import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Box, Button, Chip, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Divider, Grid, IconButton, Paper, Typography, useMediaQuery} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {ArrowLeft, ChevronLeft, ChevronRight, XCircle, RefreshCw, RotateCcw} from 'lucide-react';
import {useParams, useRouter} from 'next/navigation';
import {advAiGeneratedMaterialApi} from '@/api/adv-ai-generated-material-api';
import {useSnackbar} from 'notistack';

export default function ContentDetailPage() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams();
    const {enqueueSnackbar} = useSnackbar();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const [content, setContent] = useState(null);
    const [activeStep, setActiveStep] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [regeneratingImage, setRegeneratingImage] = useState(false);
    const [regeneratingText, setRegeneratingText] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        const contentId = params?.contentId;
        if (!contentId) {
            setError("缺少内容 ID");
            setLoading(false);
            return;
        }

        setLoading(true);
        setError(null);

        const fetchData = async () => {
            try {
                const response = await advAiGeneratedMaterialApi.queryById(contentId);

                if (!response || !response._id) {
                    throw new Error("未找到内容");
                }

                const imageUrls = (response.images || [])
                    .map(img => {
                        if (img.signed_url) {
                            return img.signed_url;
                        } else {
                            console.warn("Image missing signed_url or key:", img);
                            return 'https://placehold.co/300x200/cccccc/333333?text=URL+Error';
                        }
                    });

                setContent({
                    ...response,
                    id: response._id,
                    images: imageUrls,
                    title: response.title || '无标题',
                });
            } catch (err) {
                console.error("Failed to fetch content details:", err);
                setError(err.message || '获取内容详情失败');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [params?.contentId]);

    const handleGoBack = () => {
        router.push(`/protected/ai-image-text-generation/${params.id}`);
    };

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleDelete = () => {
        setOpenConfirmDialog(true);
    };

    const handleCloseConfirmDialog = () => {
        setOpenConfirmDialog(false);
    };

    const handleConfirmDelete = async () => {
        handleCloseConfirmDialog();

        try {
            await advAiGeneratedMaterialApi.delete(content.id);
            router.push(`/protected/ai-image-text-generation/${params.id}`);
        } catch (err) {
            console.error("Failed to delete material:", err);
            enqueueSnackbar(`删除失败: ${err.message || '未知错误'}`, {variant: 'error'});
        }
    };

    const handleRegenerateImage = async () => {
        if (!content) return;

        setRegeneratingImage(true);
        try {
            await advAiGeneratedMaterialApi.regenerateImage(content.id);
            enqueueSnackbar('重新生成图片请求已提交，请稍后查看结果', {variant: 'success'});
        } catch (err) {
            console.error("Failed to regenerate image:", err);
            enqueueSnackbar(`重新生成图片失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setRegeneratingImage(false);
        }
    };

    const handleRegenerateText = async () => {
        if (!content) return;

        setRegeneratingText(true);
        try {
            await advAiGeneratedMaterialApi.regenerateText(content.id);
            enqueueSnackbar('重新生成文案请求已提交，请稍后查看结果', {variant: 'success'});
        } catch (err) {
            console.error("Failed to regenerate text:", err);
            enqueueSnackbar(`重新生成文案失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setRegeneratingText(false);
        }
    };

    const handleRefresh = async () => {
        if (!params?.contentId) return;

        setRefreshing(true);
        try {
            const response = await advAiGeneratedMaterialApi.queryById(params.contentId);

            if (!response || !response._id) {
                throw new Error("未找到内容");
            }

            const imageUrls = (response.images || [])
                .map(img => {
                    if (img.signed_url) {
                        return img.signed_url;
                    } else {
                        console.warn("Image missing signed_url or key:", img);
                        return 'https://placehold.co/300x200/cccccc/333333?text=URL+Error';
                    }
                });

            setContent({
                ...response,
                id: response._id,
                images: imageUrls,
                title: response.title || '无标题',
            });

            enqueueSnackbar('内容已刷新', {variant: 'success'});
        } catch (err) {
            console.error("Failed to refresh content:", err);
            enqueueSnackbar(`刷新失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setRefreshing(false);
        }
    };

    return (
        <Container maxWidth="xl" sx={{py: {xs: 2, md: 3}, px: {xs: 2, md: 3}}}>
            <Box>
                <Box sx={{
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 2,
                    justifyContent: 'space-between'
                }}>
                    <Button
                        startIcon={<ArrowLeft size={18}/>}
                        onClick={handleGoBack}
                        size={isMobile ? "small" : "medium"}
                    >
                        返回列表
                    </Button>
                    <Typography 
                        variant="h5" 
                        component="h1" 
                        sx={{
                            fontWeight: 600,
                            fontSize: {xs: '1.25rem', sm: '1.5rem'}
                        }}
                    >
                        内容详情
                    </Typography>
                    <Box sx={{width: {xs: 80, sm: 100}}}></Box>
                </Box>

                {!loading && !error && content && (
                    <Box sx={{
                        mb: 3,
                        display: 'flex',
                        flexDirection: {xs: 'column', sm: 'row'},
                        justifyContent: {xs: 'stretch', sm: 'flex-end'},
                        gap: {xs: 1.5, sm: 2},
                        alignItems: {xs: 'stretch', sm: 'center'}
                    }}>
                        <Button
                            variant="outlined"
                            color="info"
                            startIcon={<RotateCcw size={18}/>}
                            onClick={handleRefresh}
                            disabled={refreshing}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                                minWidth: {xs: 'auto', sm: 'auto'},
                                flex: {xs: 1, sm: 'none'}
                            }}
                        >
                            {refreshing ? '刷新中...' : '刷新内容'}
                        </Button>
                        <Button
                            variant="outlined"
                            color="primary"
                            startIcon={<RefreshCw size={18}/>}
                            onClick={handleRegenerateImage}
                            disabled={regeneratingImage}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                                minWidth: {xs: 'auto', sm: 'auto'},
                                flex: {xs: 1, sm: 'none'}
                            }}
                        >
                            {regeneratingImage ? '生成图片中...' : '重新生成图片'}
                        </Button>
                        <Button
                            variant="outlined"
                            color="secondary"
                            startIcon={<RefreshCw size={18}/>}
                            onClick={handleRegenerateText}
                            disabled={regeneratingText}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                                minWidth: {xs: 'auto', sm: 'auto'},
                                flex: {xs: 1, sm: 'none'}
                            }}
                        >
                            {regeneratingText ? '生成文案中...' : '重新生成文案'}
                        </Button>
                        <Button
                            variant="outlined"
                            color="error"
                            startIcon={<XCircle size={18}/>}
                            onClick={handleDelete}
                            size={isMobile ? "small" : "medium"}
                            sx={{
                                minWidth: {xs: 'auto', sm: 'auto'},
                                flex: {xs: 1, sm: 'none'}
                            }}
                        >
                            删除内容
                        </Button>
                    </Box>
                )}

                {loading && (
                    <Box sx={{display: 'flex', justifyContent: 'center', py: 5}}>
                        <CircularProgress/>
                    </Box>
                )}

                {error && (
                    <Alert severity="error" sx={{mb: 2}}>{error}</Alert>
                )}

                {!loading && !error && content && (
                    <Paper
                        elevation={0}
                        sx={{
                            mb: 4,
                            p: 0,
                            overflow: 'hidden',
                            borderRadius: 2,
                            border: `1px solid ${theme.palette.divider}`,
                            maxWidth: '100%',
                            boxSizing: 'border-box'
                        }}
                    >
                        <Box sx={{
                            p: 2,
                            bgcolor: theme.palette.primary.light,
                            color: theme.palette.primary.contrastText,
                            borderBottom: `1px solid ${theme.palette.divider}`,
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                                {content.title}
                            </Typography>
                        </Box>

                        <Box sx={{
                            display: 'flex',
                            flexDirection: {xs: 'column', md: 'row'},
                            minHeight: {xs: 'auto', md: '70vh'},
                        }}>
                            <Paper
                                elevation={0}
                                sx={{
                                    borderRight: {md: `1px solid ${theme.palette.divider}`},
                                    borderBottom: {xs: `1px solid ${theme.palette.divider}`, md: 'none'},
                                    overflow: 'visible',
                                    position: 'relative',
                                    width: {xs: '100%', md: '45%'},
                                    height: {xs: '300px', sm: '400px', md: 'auto'},
                                    maxHeight: {xs: '50vh', md: 'none'},
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    bgcolor: theme.palette.grey[50],
                                    p: {xs: 1, md: 2},
                                }}
                            >
                                {content.images && content.images.length > 0 ? (
                                    <Box sx={{
                                        position: 'relative',
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        paddingBottom: content.images.length > 1 ? {xs: '40px', md: '56px'} : 0,
                                        padding: {xs: 1, md: 3},
                                    }}>
                                        <Box
                                            component="img"
                                            src={content.images[activeStep]}
                                            alt={`${content.title || 'Image'} - ${activeStep + 1}`}
                                            sx={{
                                                width: '100%',
                                                height: '100%',
                                                objectFit: 'cover',
                                                display: 'block',
                                                borderRadius: 1,
                                                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                                            }}
                                            onError={(e) => {
                                                e.target.src = 'https://placehold.co/600x400/ffcccc/333333?text=Error+Loading';
                                            }}
                                        />

                                        {content.images.length > 1 && (
                                            <>
                                                <IconButton
                                                    onClick={handleBack}
                                                    disabled={activeStep === 0}
                                                    sx={{
                                                        position: 'absolute',
                                                        left: {xs: 4, md: 8},
                                                        top: '50%',
                                                        transform: 'translateY(-50%)',
                                                        color: theme.palette.common.white,
                                                        bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                        '&:hover': {bgcolor: 'rgba(0, 0, 0, 0.7)'},
                                                        '&.Mui-disabled': {opacity: 0.5},
                                                        width: {xs: 32, md: 40},
                                                        height: {xs: 32, md: 40}
                                                    }}
                                                    aria-label="上一张图片"
                                                >
                                                    <ChevronLeft size={20}/>
                                                </IconButton>
                                                <IconButton
                                                    onClick={handleNext}
                                                    disabled={activeStep === content.images.length - 1}
                                                    sx={{
                                                        position: 'absolute',
                                                        right: {xs: 4, md: 8},
                                                        top: '50%',
                                                        transform: 'translateY(-50%)',
                                                        color: theme.palette.common.white,
                                                        bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                        '&:hover': {bgcolor: 'rgba(0, 0, 0, 0.7)'},
                                                        '&.Mui-disabled': {opacity: 0.5},
                                                        width: {xs: 32, md: 40},
                                                        height: {xs: 32, md: 40}
                                                    }}
                                                    aria-label="下一张图片"
                                                >
                                                    <ChevronRight size={20}/>
                                                </IconButton>
                                                <Box sx={{
                                                    position: 'absolute',
                                                    bottom: {xs: 4, md: 8},
                                                    left: '50%',
                                                    transform: 'translateX(-50%)',
                                                    bgcolor: 'rgba(0, 0, 0, 0.5)',
                                                    color: 'white',
                                                    px: {xs: 0.75, md: 1},
                                                    py: {xs: 0.25, md: 0.5},
                                                    borderRadius: 1,
                                                    fontSize: {xs: '0.7rem', md: '0.8rem'},
                                                }}>
                                                    {`${activeStep + 1} / ${content.images.length}`}
                                                </Box>
                                            </>
                                        )}
                                    </Box>
                                ) : (
                                    <Typography color="text.secondary">无图片</Typography>
                                )}
                            </Paper>

                            <Box
                                sx={{
                                    width: {xs: '100%', md: '55%'},
                                    p: {xs: 1.5, md: 3},
                                    pr: {xs: 1, md: 3},
                                    overflowY: 'auto',
                                    height: {xs: 'auto', md: '100%'},
                                    bgcolor: 'background.paper',
                                    overflowX: 'hidden',
                                    maxWidth: '100%',
                                    boxSizing: 'border-box'
                                }}
                            >
                                <Box sx={{
                                    mb: {xs: 1.5, md: 2}, 
                                    pb: {xs: 1.5, md: 2}, 
                                    borderBottom: `1px dashed ${theme.palette.divider}`
                                }}>
                                    <Typography 
                                        variant="subtitle1" 
                                        color="text.secondary" 
                                        gutterBottom 
                                        sx={{
                                            fontWeight: 'bold',
                                            fontSize: {xs: '0.9rem', md: '1rem'}
                                        }}
                                    >
                                        标题
                                    </Typography>
                                    <Typography 
                                        variant="h6" 
                                        component="div" 
                                        sx={{
                                            mb: 2,
                                            fontSize: {xs: '1.1rem', md: '1.25rem'},
                                            lineHeight: 1.4,
                                            wordBreak: 'break-word',
                                            overflowWrap: 'break-word',
                                            maxWidth: '100%',
                                            boxSizing: 'border-box'
                                        }}
                                    >
                                        {content.title || '无标题'}
                                    </Typography>
                                </Box>

                                <Box sx={{mt: {xs: 2, md: 3}, mb: {xs: 2, md: 3}}}>
                                    <Typography 
                                        variant="subtitle1" 
                                        color="text.secondary" 
                                        gutterBottom 
                                        sx={{
                                            fontWeight: 'bold',
                                            fontSize: {xs: '0.9rem', md: '1rem'}
                                        }}
                                    >
                                        内容
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            whiteSpace: 'pre-wrap',
                                            color: 'text.primary',
                                            lineHeight: {xs: 1.5, md: 1.6},
                                            fontSize: {xs: '0.875rem', md: '0.875rem'},
                                            wordBreak: 'break-word',
                                            overflowWrap: 'break-word',
                                            hyphens: 'auto',
                                            maxWidth: '100%',
                                            boxSizing: 'border-box'
                                        }}
                                    >
                                        {content.content || '无内容'}
                                    </Typography>
                                </Box>

                                <Divider sx={{my: 2}}/>

                                <Box sx={{
                                    mb: {xs: 1.5, md: 2}, 
                                    pb: {xs: 1.5, md: 2}
                                }}>
                                    <Typography 
                                        variant="subtitle1" 
                                        color="text.secondary" 
                                        gutterBottom 
                                        sx={{
                                            fontWeight: 'bold',
                                            fontSize: {xs: '0.9rem', md: '1rem'}
                                        }}
                                    >
                                        生成状态
                                    </Typography>
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: {xs: 'column', sm: 'row'},
                                        gap: 2,
                                        mb: 2
                                    }}>
                                        <Box sx={{ flex: 1 }}>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                                图片生成状态
                                            </Typography>
                                            <Chip
                                                label={content.image_generation_status || '未知'}
                                                color={
                                                    content.image_generation_status === '已完成' ? 'success' :
                                                    content.image_generation_status === '失败' ? 'error' :
                                                    content.image_generation_status === '生成中' ? 'warning' : 'default'
                                                }
                                                size="small"
                                                sx={{
                                                    fontWeight: 'medium',
                                                    minWidth: 80
                                                }}
                                            />
                                        </Box>
                                        <Box sx={{ flex: 1 }}>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                                文字生成状态
                                            </Typography>
                                            <Chip
                                                label={content.text_generation_status || '未知'}
                                                color={
                                                    content.text_generation_status === '已完成' ? 'success' :
                                                    content.text_generation_status === '失败' ? 'error' :
                                                    content.text_generation_status === '生成中' ? 'warning' : 'default'
                                                }
                                                size="small"
                                                sx={{
                                                    fontWeight: 'medium',
                                                    minWidth: 80
                                                }}
                                            />
                                        </Box>
                                    </Box>
                                    {content.generation_fail_reason && (
                                        <Alert 
                                            severity="error" 
                                            sx={{ 
                                                mt: 1,
                                                '& .MuiAlert-message': {
                                                    fontSize: {xs: '0.8rem', md: '0.875rem'}
                                                }
                                            }}
                                        >
                                            <Typography variant="body2" component="span" sx={{ fontWeight: 'medium' }}>
                                                失败原因：
                                            </Typography>
                                            {content.generation_fail_reason}
                                        </Alert>
                                    )}
                                </Box>

                            </Box>
                        </Box>

                    </Paper>
                )}
            </Box>

            <Dialog
                open={openConfirmDialog}
                onClose={handleCloseConfirmDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{"确认删除"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        确定要删除这个生成内容吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseConfirmDialog} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleConfirmDelete} color="error" autoFocus>
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
} 