"""
xhs homefeed api response schema
"""
from typing import List, Optional

from pydantic import BaseModel


class HomefeedUser(BaseModel):
    xsec_token: Optional[str] = None
    nick_name: str
    avatar: str
    user_id: str
    nickname: str


class HomefeedInteractInfo(BaseModel):
    liked: bool
    liked_count: str


class HomefeedCoverInfo(BaseModel):
    image_scene: str
    url: str


class HomefeedCover(BaseModel):
    width: int
    height: int
    url: str
    url_pre: str
    url_default: str
    file_id: str
    info_list: List[HomefeedCoverInfo]


class HomefeedVideoCapa(BaseModel):
    duration: int


class HomefeedVideo(BaseModel):
    capa: HomefeedVideoCapa


class HomefeedNoteCard(BaseModel):
    type: str
    display_title: str
    user: HomefeedUser
    interact_info: HomefeedInteractInfo
    cover: HomefeedCover
    video: Optional[HomefeedVideo] = None


class HomefeedItem(BaseModel):
    id: str
    model_type: str
    note_card: HomefeedNoteCard
    track_id: str
    ignore: bool
    xsec_token: Optional[str] = None


class HomefeedData(BaseModel):
    cursor_score: str
    items: List[HomefeedItem]


class XHSHomefeedResponse(BaseModel):
    msg: str
    data: HomefeedData
    code: int
    success: bool
