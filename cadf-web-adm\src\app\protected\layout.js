"use client";

import SidebarLink from "@/app/protected/components/SidebarLink";
import {LOGIN_PATH, PROJECT_DESCRIPTION, PROJECT_NAME} from "@/config";
import {Box, Divider, Drawer, IconButton, List, ListItemButton, ListItemIcon, ListItemText, useMediaQuery} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Cookies from 'js-cookie';
import {BarChart, Bot, Brain, FileImage, LogOut, Menu, Share2, ShoppingBag, Tag, CreditCard, UserPlus, Shield, Users} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {useState} from 'react';

const drawerWidth = 240;

export default function ProtectedLayout({children}) {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [mobileOpen, setMobileOpen] = useState(false);
    const router = useRouter();

    const handleDrawerToggle = () => {
        setMobileOpen(!mobileOpen);
    };

    const handleLogout = () => {
        Cookies.remove('access_token');
        router.push(LOGIN_PATH);
    };

    const menuItems = [
        {text: '字典管理', icon: <Tag size={20}/>, path: '/protected/category-management'},
        {text: '打款管理', icon: <CreditCard size={20}/>, path: '/protected/payment-management'},
        {text: '广告主管理', icon: <UserPlus size={20}/>, path: '/protected/advertiser-management'},
        {text: '舰长管理', icon: <Shield size={20}/>, path: '/protected/captain-management'},
        {text: '用户管理', icon: <Users size={20}/>, path: '/protected/user-management'},
    ];

    const drawer = (
        <Box sx={{overflow: 'auto', height: '100%', display: 'flex', flexDirection: 'column'}}>
            <Box
                sx={{
                    py: 2.5,
                    px: 2,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    color: theme.palette.text.primary
                }}
            >
                <Brain
                    size={28}
                    color={theme.palette.primary.main}
                    style={{marginRight: '12px'}}
                />
                <Box>
                    <Typography
                        variant="subtitle1"
                        component="div"
                        sx={{
                            fontWeight: 600,
                            lineHeight: 1.2,
                            fontSize: '0.95rem'
                        }}
                    >
                        {PROJECT_NAME}
                    </Typography>
                    <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                            fontSize: '0.7rem',
                            display: 'block',
                            mt: 0.3
                        }}
                    >
                        {PROJECT_DESCRIPTION}
                    </Typography>
                </Box>
            </Box>
            <Divider/>
            <List sx={{flexGrow: 1}}>
                {menuItems.map((item) => (
                    <SidebarLink
                        key={item.text}
                        item={item}
                        onClick={isMobile ? handleDrawerToggle : undefined}
                    />
                ))}
            </List>
            <Divider/>
            <Box sx={{p: 2}}>
                <ListItemButton
                    sx={{
                        p: 1.5,
                        borderRadius: 2,
                        border: `1px dashed ${theme.palette.divider}`,
                        '&:hover': {
                            backgroundColor: `rgba(${theme.palette.error.main}, 0.08)`,
                            color: theme.palette.error.main,
                            '& .MuiListItemIcon-root': {
                                color: theme.palette.error.main,
                            },
                        },
                        transition: 'all 0.2s',
                    }}
                    onClick={handleLogout}
                >
                    <ListItemIcon>
                        <LogOut size={20} color="action"/>
                    </ListItemIcon>
                    <ListItemText
                        primary="退出登录"
                        primaryTypographyProps={{
                            fontSize: '0.95rem',
                        }}
                    />
                </ListItemButton>
            </Box>
        </Box>
    );

    return (
        <Box sx={{display: 'flex'}}>
            {/* 移动设备菜单按钮 */}
            {isMobile && (
                <Box sx={{position: 'fixed', top: 10, left: 10, zIndex: 1200}}>
                    <IconButton
                        color="primary"
                        aria-label="打开菜单"
                        edge="start"
                        onClick={handleDrawerToggle}
                        sx={{
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': {bgcolor: 'background.paper'}
                        }}
                    >
                        <Menu/>
                    </IconButton>
                </Box>
            )}

            <Box
                component="nav"
                sx={{width: {md: drawerWidth}, flexShrink: {md: 0}}}
            >
                {/* 移动设备抽屉 */}
                <Drawer
                    variant="temporary"
                    open={mobileOpen}
                    onClose={handleDrawerToggle}
                    ModalProps={{
                        keepMounted: true, // 提高移动设备上的性能
                    }}
                    sx={{
                        display: {xs: 'block', md: 'none'},
                        '& .MuiDrawer-paper': {boxSizing: 'border-box', width: drawerWidth},
                    }}
                >
                    {drawer}
                </Drawer>

                {/* 桌面端抽屉 */}
                <Drawer
                    variant="permanent"
                    sx={{
                        display: {xs: 'none', md: 'block'},
                        '& .MuiDrawer-paper': {boxSizing: 'border-box', width: drawerWidth},
                    }}
                    open
                >
                    {drawer}
                </Drawer>
            </Box>

            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: 3,
                    width: {md: `calc(100% - ${drawerWidth}px)`},
                    height: '100vh',
                    display: 'flex',
                    flexDirection: 'column'
                }}
            >
                <Box sx={{
                    flexGrow: 1,
                    maxWidth: '1200px',
                    mx: 'auto',
                    width: '100%'
                }}>
                    {children}
                </Box>
            </Box>
        </Box>
    );
} 