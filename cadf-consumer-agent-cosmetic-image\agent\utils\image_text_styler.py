import asyncio
import io
import os

from PIL import Image, ImageDraw, ImageFont
from pilmoji import <PERSON>lmoji
from requests.exceptions import ReadTimeout

from omni.log.log import olog


class ImageTextStyler:
    def __init__(self):
        # 定义字体路径
        self.text_font_path = os.path.join(
            os.path.dirname(__file__), "AlimamaShuHeiTi-Bold.ttf"
        )  # 文字字体
        self.body_font_size = 40
        self.title_font_size = self.body_font_size * 1.3
        self.edge_padding_percent = 0.05
        self.border_width = 4

        self.text_font = ImageFont.truetype(self.text_font_path, self.body_font_size)
        self.title_text_font = ImageFont.truetype(
            self.text_font_path, int(self.title_font_size)
        )  # 新增标题字体

        self.title_color = (255, 220, 100, 255)
        self.body_color = (255, 255, 255, 255)
        self.text_padding = 20
        self.line_spacing = 10
        self.border_color = (0, 0, 0, 255)
        
        self.position_map = {
            "左上": (0, 0),
            "左下": (0, 1),
            "右上": (1, 0),
            "右下": (1, 1),
            "左中": (0, 0.5),
            "右中": (1, 0.5)
        }

    def _calculate_text_dimensions(self, draw, title: str, bodies: list[str]) -> tuple[int, int]:
        """计算文本区域的最大宽度和总高度"""
        max_width = 0
        total_height = 0

        if title:
            title_bbox = draw.textbbox((0, 0), title, font=self.title_text_font)
            max_width = max(max_width, title_bbox[2] - title_bbox[0])
            total_height += (title_bbox[3] - title_bbox[1]) + self.line_spacing * 2

        for line in bodies:
            line_bbox = draw.textbbox((0, 0), line, font=self.text_font)
            max_width = max(max_width, line_bbox[2] - line_bbox[0])
            total_height += (line_bbox[3] - line_bbox[1]) + self.line_spacing

        return max_width, total_height

    def _calculate_position(self, position: str, img_width: int, img_height: int, 
                          text_width: int, text_height: int, padding_x: int, padding_y: int) -> tuple[int, int]:
        """根据位置参数计算文本区域的起始坐标"""
        if position not in self.position_map:
            raise ValueError(f"无效的位置参数，可选值: {list(self.position_map.keys())}")
        
        x_factor, y_factor = self.position_map[position]
        
        if x_factor == 0:
            x = padding_x
        else:
            x = img_width - text_width - padding_x
            
        if y_factor == 0:
            y = padding_y
        elif y_factor == 1:
            y = img_height - text_height - padding_y
        else:
            y = int((img_height - text_height) / 2)
            
        return max(x, padding_x), max(y, padding_y)

    async def _draw_title(self, renderer, draw, title: str, region_x: int, current_y: int, max_width: int) -> int:
        """渲染标题并返回下一行y坐标"""
        title_bbox = draw.textbbox((0, 0), title, font=self.title_text_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_height = title_bbox[3] - title_bbox[1]
        
        title_x = int(region_x + (max_width - title_width) / 2)
        await self._draw_text_with_retry(
            renderer, (title_x, current_y), title,
            font=self.title_text_font, fill=self.title_color,
            stroke_width=self.border_width, stroke_fill=self.border_color
        )
        return current_y + title_height + self.line_spacing * 2

    async def _draw_bodies(self, renderer, draw, bodies: list[str], region_x: int, current_y: int, max_width: int) -> None:
        """渲染正文内容"""
        for line in bodies:
            line_bbox = draw.textbbox((0, 0), line, font=self.text_font)
            line_width = line_bbox[2] - line_bbox[0]
            line_height = line_bbox[3] - line_bbox[1]
            
            line_x = int(region_x + (max_width - line_width) / 2)
            await self._draw_text_with_retry(
                renderer, (line_x, current_y), line,
                font=self.text_font, fill=self.body_color,
                stroke_width=self.border_width, stroke_fill=self.border_color
            )
            current_y += line_height + self.line_spacing

    async def _draw_text_with_retry(self, renderer, *args, max_retries: int = 3, retry_interval: float = 0.5, **kwargs):
        """带重试机制的文本渲染"""
        for attempt in range(1, max_retries + 1):
            try:
                return await asyncio.to_thread(renderer.text, *args, **kwargs)
            except ReadTimeout as e:
                olog.warning(f"第{attempt}次渲染emoji超时，准备重试")
                if attempt == max_retries:
                    olog.exception("多次重试后仍然失败，放弃渲染emoji")
                    raise
                await asyncio.sleep(retry_interval)
            except Exception as e:
                olog.exception(f"渲染文本时发生异常: {e}")
                raise

    async def apply_style(
            self,
            image_bytes: bytes,
            title: str,
            bodies: list[str] = None,
            position: str = "左上",
    ) -> bytes | None:
        olog.info(f"开始应用文字样式，标题: {title}，位置: {position}")
        
        try:
            if bodies is None:
                bodies = []
            
            img = Image.open(io.BytesIO(image_bytes)).convert("RGBA")
            img_width, img_height = img.size
            olog.debug(f"图像尺寸: {img_width}x{img_height}")

            draw_for_border_and_measure = ImageDraw.Draw(img)
            pilmoji_renderer = Pilmoji(img)

            padding_x = int(img_width * self.edge_padding_percent)
            padding_y = int(img_height * self.edge_padding_percent)

            # 计算文本区域尺寸
            max_width, total_height = self._calculate_text_dimensions(
                draw_for_border_and_measure, title, bodies
            )
            olog.debug(f"文本区域尺寸: {max_width}x{total_height}")

            # 计算文本区域起始位置
            region_x, region_y = self._calculate_position(
                position, img_width, img_height, max_width, total_height, padding_x, padding_y
            )
            olog.debug(f"文本区域起始位置: ({region_x}, {region_y})")

            current_y = int(region_y)

            # 渲染标题
            if title:
                current_y = await self._draw_title(
                    pilmoji_renderer, draw_for_border_and_measure, title, 
                    region_x, current_y, max_width
                )

            # 渲染正文
            if bodies:
                await self._draw_bodies(
                    pilmoji_renderer, draw_for_border_and_measure, bodies,
                    region_x, current_y, max_width
                )

            output_buffer = io.BytesIO()
            img.save(output_buffer, format="PNG")
            
            olog.info("文字样式应用完成")
            return output_buffer.getvalue()
        except Exception as e:
            olog.exception("应用文字样式时发生异常")
            raise
