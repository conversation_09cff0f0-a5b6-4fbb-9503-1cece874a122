import React from 'react';
import {Box, Card, CardActionArea, CardMedia} from '@mui/material';

const ProductCard = ({
                         images = [],  // 接收图片数组
                         onClick,
                         children
                     }) => {
    // 默认值
    const defaultImage = 'https://placehold.co/300x400/e0f2fe/0c4a6e?text=Product';

    // 确定要显示的图片
    const imageToShow = images && images.length > 0 ? images[0] : defaultImage;

    // 图片数量
    const imageCount = images ? images.length : 0;

    return (
        <Card sx={{height: '100%', boxShadow: 2, borderRadius: 2, display: 'flex', flexDirection: 'column'}}>
            {/* 图片部分使用CardActionArea */}
            <CardActionArea onClick={onClick}>
                <Box sx={{
                    width: '100%',
                    paddingTop: '133.33%', // 默认3:4的宽高比
                    position: 'relative',
                    overflow: 'hidden'
                }}>
                    <CardMedia
                        component="img"
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                        }}
                        image={imageToShow}
                        alt="Product"
                    />

                    {/* 图片数量标记 */}
                    {imageCount > 1 && (
                        <Box
                            sx={{
                                position: 'absolute',
                                bottom: 8,
                                right: 8,
                                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                color: 'white',
                                borderRadius: '12px',
                                padding: '2px 8px',
                                fontSize: '0.75rem'
                            }}
                        >
                            {imageCount} 张图片
                        </Box>
                    )}
                </Box>
            </CardActionArea>

            {/* 内容部分不使用CardActionArea，避免button嵌套 */}
            <Box
                sx={{
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer'
                }}
                onClick={onClick}
            >
                {children}
            </Box>
        </Card>
    );
};

export default ProductCard; 