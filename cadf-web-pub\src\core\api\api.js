"use client";

import {LOGIN_PATH} from "@/config";
import axios from "axios";
import Cookies from "js-cookie";
import {BASE_URL} from "../../../env";
import { enqueueSnackbar } from 'notistack';

const api = async (params = {}) => {
    const access_token = Cookies.get("access_token");

    const headers = {
        "Content-Type": "application/json",
        ...(access_token && {Authorization: access_token}),
    };

    const url = `${BASE_URL}/api`;

    try {
        const response = await axios({
            method: "post",
            url,
            headers,
            data: params,
        });

        const data = response.data;

        switch (data.code) {
            case 401:
                throw new Error(data.message);
            case 403:
                enqueueSnackbar("请先登录！", { variant: 'error' });
                Cookies.remove("access_token");
                window.location.href = LOGIN_PATH;
                return {};
            case 500:
                throw new Error(data.message);
            default:
                return data.data;
        }
    } catch (error) {
        throw new Error(`请求错误: ${error.message}`);
    }
};

export default api;
