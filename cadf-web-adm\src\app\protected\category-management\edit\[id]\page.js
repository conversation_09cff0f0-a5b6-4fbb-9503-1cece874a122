"use client";

import { useState, useEffect } from 'react';
import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  Box, 
  Typography, 
  Paper, 
  Button, 
  TextField,
  Stack,
  CircularProgress,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ArrowLeft, Save } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { dataDictionaryApi } from "@/api/data-dictionary-api";

export default function EditDataDictionary({ params }) {
  const resolvedParams = React.use(params);
  const theme = useTheme();
  const router = useRouter();
  const dispatch = useDispatch();
  
  const [formData, setFormData] = useState({
    category: '',
    key: '',
    value: ''
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // 加载数据字典详情
  const loadDictionary = async () => {
    try {
      setInitialLoading(true);
      const response = await dataDictionaryApi.getById(resolvedParams.id);
      if (response.dictionary) {
        setFormData({
          category: response.dictionary.category || '',
          key: response.dictionary.key || '',
          value: response.dictionary.value || ''
        });
      }
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: '加载数据失败' 
      }));
      router.push('/protected/category-management');
    } finally {
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    if (resolvedParams.id) {
      loadDictionary();
    }
  }, [resolvedParams.id]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      await dataDictionaryApi.update(
        resolvedParams.id,
        formData.category,
        formData.key,
        formData.value
      );
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.MODIFY }));
      router.push('/protected/category-management');
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '修改失败' 
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/protected/category-management');
  };

  const isFormValid = formData.category && formData.key && formData.value;

  if (initialLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minHeight: '100vh', py: 4 }}>
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', gap: 2, width: '100%', maxWidth: 600 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowLeft size={18} />}
          onClick={handleCancel}
          color="inherit"
        >
          返回
        </Button>
        <Typography variant="h5" component="h1" fontWeight="bold">
          编辑数据字典
        </Typography>
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          p: 4,
          width: '100%',
          maxWidth: 600
        }}
      >
        <Stack spacing={3}>
          <TextField
            name="category"
            label="类别"
            type="text"
            fullWidth
            value={formData.category}
            onChange={handleInputChange}
            variant="outlined"
            required
            helperText="请输入数据字典的类别"
          />
          
          <TextField
            name="key"
            label="键"
            type="text"
            fullWidth
            value={formData.key}
            onChange={handleInputChange}
            variant="outlined"
            required
            helperText="请输入数据字典的键名"
          />
          
          <TextField
            name="value"
            label="值"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={formData.value}
            onChange={handleInputChange}
            variant="outlined"
            required
            helperText="支持文本、数字或JSON格式"
          />

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 4 }}>
            <Button 
              onClick={handleCancel} 
              color="inherit"
              variant="outlined"
            >
              取消
            </Button>
            <Button 
              onClick={handleSave} 
              variant="contained"
              startIcon={<Save size={18} />}
              disabled={!isFormValid || loading}
            >
              {loading ? '保存中...' : '保存'}
            </Button>
          </Box>
        </Stack>
      </Paper>
    </Box>
  );
} 