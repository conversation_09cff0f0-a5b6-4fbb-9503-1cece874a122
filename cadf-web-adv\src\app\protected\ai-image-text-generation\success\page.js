"use client";

import {Box, Button, Container, Paper, Typography, Stack, useMediaQuery} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {CheckCircle, Megaphone, Plus, List} from "lucide-react";

export default function TaskSuccessPage() {
    const theme = useTheme();
    const router = useRouter();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    return (
        <Container maxWidth="md" sx={{ py: { xs: 4, sm: 8 }, px: { xs: 1, sm: 2 } }}>
            <Paper sx={{
                p: { xs: 4, sm: 8 },
                textAlign: 'center',
                borderRadius: 4,
                boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.04), 0px 8px 40px rgba(0, 0, 0, 0.06)",
                border: `1px solid ${theme.palette.grey[50]}`,
                bgcolor: 'background.paper'
            }}>
                {/* 成功图标和标题 */}
                <Box sx={{ mb: { xs: 3, sm: 4 } }}>
                    <CheckCircle 
                        size={isMobile ? 60 : 80}
                        color={theme.palette.success.main} 
                        style={{ marginBottom: isMobile ? 16 : 24 }}
                    />
                    <Typography 
                        variant={isMobile ? "h4" : "h3"} 
                        fontWeight={600} 
                        color="success.main" 
                        gutterBottom
                        sx={{ 
                            fontSize: { xs: '1.75rem', sm: '2.5rem' },
                            letterSpacing: '-0.02em'
                        }}
                    >
                        AI生成任务创建成功！
                    </Typography>
                    <Typography 
                        variant={isMobile ? "body1" : "h6"} 
                        color="text.secondary" 
                        sx={{ 
                            maxWidth: 500, 
                            mx: 'auto',
                            px: { xs: 1, sm: 0 },
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                        }}
                    >
                        AI图文生成任务已成功创建，系统正在为您生成高质量的推广内容
                    </Typography>
                </Box>

                {/* 提示信息 */}
                <Box sx={{ 
                    mb: { xs: 4, sm: 6 }, 
                    p: { xs: 3, sm: 4 }, 
                    bgcolor: 'grey.25', 
                    borderRadius: 3,
                    textAlign: 'left',
                    border: `1px solid ${theme.palette.grey[100]}`
                }}>
                    <Typography 
                        variant={isMobile ? "subtitle1" : "h6"} 
                        fontWeight="medium" 
                        gutterBottom
                        sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}
                    >
                        接下来您可以：
                    </Typography>
                    <Stack spacing={{ xs: 1.5, sm: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                            <Typography variant="body1" color="primary.main" sx={{ mr: 1, fontWeight: 'bold', fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                                •
                            </Typography>
                            <Typography variant="body1" sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                                在任务列表中查看生成进度和结果
                            </Typography>
                        </Box>
                        <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'flex-start',
                            p: { xs: 2, sm: 2.5 },
                            bgcolor: 'primary.50',
                            borderRadius: 2,
                            border: `2px solid ${theme.palette.primary.main}`,
                            boxShadow: '0px 2px 8px rgba(25, 118, 210, 0.12)'
                        }}>
                            <Typography variant="body1" color="primary.main" sx={{ mr: 1, fontWeight: 'bold', fontSize: { xs: '1rem', sm: '1.1rem' } }}>
                                ⭐
                            </Typography>
                            <Typography variant="body1" fontWeight="600" color="primary.main" sx={{ 
                                fontSize: { xs: '1rem', sm: '1.1rem' },
                                lineHeight: 1.4
                            }}>
                                生成完成后，立即创建推广任务来推广您的图文内容
                            </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                            <Typography variant="body1" color="primary.main" sx={{ mr: 1, fontWeight: 'bold', fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                                •
                            </Typography>
                            <Typography variant="body1" sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                                继续创建更多AI图文生成任务
                            </Typography>
                        </Box>
                    </Stack>
                </Box>

                {/* 操作按钮 */}
                <Stack 
                    direction={{ xs: 'column', sm: 'row' }} 
                    spacing={{ xs: 1.5, sm: 2 }}
                    justifyContent="center"
                    alignItems="center"
                >
                    <Button
                        variant="outlined"
                        size={isMobile ? "medium" : "large"}
                        onClick={() => router.push("/protected/ai-image-text-generation")}
                        startIcon={<List size={16} />}
                        sx={{ 
                            minWidth: { xs: '100%', sm: 200 },
                            maxWidth: { xs: '100%', sm: 'none' },
                            height: { xs: 44, sm: 48 },
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            fontWeight: 500,
                            borderRadius: 2.5,
                            textTransform: 'none',
                            borderColor: 'grey.300',
                            '&:hover': {
                                borderColor: 'primary.main',
                                bgcolor: 'primary.50',
                                transform: 'translateY(-1px)'
                            },
                            transition: 'all 0.2s ease-in-out'
                        }}
                    >
                        查看任务列表
                    </Button>
                    
                    <Button
                        variant="contained"
                        size={isMobile ? "medium" : "large"}
                        color="primary"
                        onClick={() => router.push("/protected/promotion-task/create")}
                        startIcon={<Megaphone size={16} />}
                        sx={{ 
                            minWidth: { xs: '100%', sm: 200 },
                            maxWidth: { xs: '100%', sm: 'none' },
                            height: { xs: 44, sm: 48 },
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            fontWeight: 500,
                            borderRadius: 2.5,
                            textTransform: 'none',
                            boxShadow: '0px 2px 8px rgba(25, 118, 210, 0.24), 0px 1px 3px rgba(25, 118, 210, 0.12)',
                            '&:hover': {
                                boxShadow: '0px 4px 16px rgba(25, 118, 210, 0.32), 0px 2px 6px rgba(25, 118, 210, 0.16)',
                                transform: 'translateY(-1px)'
                            },
                            transition: 'all 0.2s ease-in-out'
                        }}
                    >
                        创建推广任务
                    </Button>
                    
                    <Button
                        variant="text"
                        size={isMobile ? "medium" : "large"}
                        onClick={() => router.push("/protected/ai-image-text-generation/create")}
                        startIcon={<Plus size={16} />}
                        sx={{ 
                            minWidth: { xs: '100%', sm: 200 },
                            maxWidth: { xs: '100%', sm: 'none' },
                            height: { xs: 44, sm: 48 },
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            fontWeight: 500,
                            borderRadius: 2.5,
                            textTransform: 'none',
                            color: 'text.secondary',
                            '&:hover': {
                                bgcolor: 'grey.100',
                                color: 'primary.main',
                                transform: 'translateY(-1px)'
                            },
                            transition: 'all 0.2s ease-in-out'
                        }}
                    >
                        继续创建任务
                    </Button>
                </Stack>

                {/* 底部提示 */}
                <Box sx={{ mt: { xs: 3, sm: 4 }, pt: { xs: 2, sm: 3 }, borderTop: 1, borderColor: 'divider' }}>
                    <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{ 
                            fontSize: { xs: '0.85rem', sm: '0.875rem' },
                            px: { xs: 1, sm: 0 }
                        }}
                    >
                        生成过程通常需要几分钟时间，请耐心等待。您可以随时在任务列表中查看进度。
                    </Typography>
                </Box>
            </Paper>
        </Container>
    );
}