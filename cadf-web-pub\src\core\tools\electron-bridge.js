export const isElectron = () => {
    return window.electronAPI !== undefined;
};

export const invokeElectron = async (channel, ...args) => {
    if (!isElectron()) {
        throw new Error('Electron API 不可用');
    }

    try {
        return await window.electronAPI.invoke(channel, ...args);
    } catch (error) {
        console.error(`在通道 '${channel}' 上调用 Electron API 时发生错误:`, error);
        throw error;
    }
};
