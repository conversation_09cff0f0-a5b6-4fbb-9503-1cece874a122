from typing import Dict, Any
import asyncio

from agent.utils.openai_image_edit import OpenaiImageEditor
from agent.state import OverallState
from agent.utils.temp_file_manager import save_temp_pic
from omni.log.log import olog


async def clone_compliant_character(state: OverallState) -> Dict[str, Any]:
    """克隆合规角色节点，移除图片中所有非自然元素"""
    olog.info("开始执行合规角色克隆节点")
    
    image_path = state.scene_image_path
    prompt_clean_scene = "移除图片中所有艺术字体，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素，确保场景干净。"
    
    olog.debug("开始执行图片清理操作")
    
    async def read_image_file(path: str) -> bytes:
        def _read():
            with open(path, "rb") as f:
                return f.read()
        return await asyncio.to_thread(_read)
    
    image_bytes = await read_image_file(image_path)
    
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    edited_image_data, error_type = await editor.generate_image_from_image(
        image_bytes_list=[image_bytes],
        prompt=prompt_clean_scene,
    )
    
    if error_type == "success" and edited_image_data:
        temp_path = await save_temp_pic(edited_image_data)
        olog.info("图片克隆成功")
        return {"processed_image_path": temp_path}
    elif error_type == "security_blocked":
        olog.warning("图片克隆因安全机制失败，需要识别元素并生成新图片")
        return {
            "processed_image_path": None,
            "security_blocked": True
        }
    else:
        olog.error(f"图片克隆失败，错误类型: {error_type}")
        raise RuntimeError(f"图片克隆失败: {error_type}")