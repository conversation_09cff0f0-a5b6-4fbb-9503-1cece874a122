import os
import tempfile

LOCAL_TEMP_DIR = os.path.join(tempfile.gettempdir(), 'cadf')
os.makedirs(LOCAL_TEMP_DIR, exist_ok=True)

OSS_PIC_DIR = '/cadf/pic'


# Redis 键配置
class RedisKeyConfig:
    XHS_IMAGE_GEN_SET = "cadf:agent:xhs:image_gen_set"
    XHS_TEXT_GEN_SET = "cadf:agent:xhs:text_gen_set"
    XHS_NOTE_CONTENT_SPIDER_SET = "cadf:spider:xhs:note_content_spider_set"
    XHS_NOTE_COMMENT_SPIDER_SET = "cadf:spider:xhs:note_comment_spider_set"
    XHS_ACCOUNT_TRAFFIC_METRICS_SPIDER_SET = "cadf:spider:xhs:account_traffic_metrics_spider_set"
    XHS_NOTE_URL_VERIFY_SET = "cadf:spider:xhs:note_url_verify_set"


# 消费相关配置
class CostConfig:
    IMAGE_GENERATION_COST = 5
    TEXT_GENERATION_COST = 5
