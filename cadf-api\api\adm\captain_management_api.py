import random
import string
from typing import Any, Dict, List

from beanie import PydanticObjectId

from models.models import User
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog


@register_handler('captain_management')
class CaptainManagementApi:

    @auth_required(['admin'])
    async def query_all_users(self, data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """查询所有用户，用于舰长管理"""
        # 查询所有用户
        users = await User.find_all().sort(-User.create_at).to_list()

        # 为每个用户添加角色信息并转换为字典
        user_dicts = []
        for user in users:
            user_dict = user.to_dict()
            if not user_dict.get('roles'):
                user_dict['roles'] = []
            # 判断是否为舰长
            user_dict['is_captain'] = 'captain' in user_dict['roles']
            # 判断是否可以成为舰长（具有user或creator权限）
            user_dict['can_be_captain'] = any(role in user_dict['roles'] for role in ['user', 'creator'])
            user_dicts.append(user_dict)

        olog.info(f"查询到 {len(user_dicts)} 个用户")
        return {'users': user_dicts}

    @auth_required(['admin'])
    async def promote_to_captain(self, data: Dict[str, Any]):
        """将用户提升为舰长"""
        target_user_id = data.get('target_user_id')

        # 输入验证
        if not target_user_id:
            raise MException("target_user_id 不能为空")

        # 查询用户
        user = await User.find_one(User.id == PydanticObjectId(target_user_id))
        if not user:
            raise MException("用户不存在")

        # 检查用户是否已经是舰长
        if 'captain' in user.roles:
            raise MException("该用户已经是舰长")

        # 检查用户是否具有user或creator权限
        if not any(role in user.roles for role in ['user', 'creator']):
            raise MException("只有具有user或creator权限的用户才能成为舰长")

        # 生成唯一的10位大写英文邀请码
        code_length = 10
        characters = string.ascii_uppercase
        while True:
            invite_code = ''.join(random.choice(characters) for _ in range(code_length))
            # 检查邀请码是否已存在
            if not await User.find_one(User.crew_invite_code == invite_code):
                break

        # 添加舰长权限并设置邀请码
        user.roles.append('captain')
        user.crew_invite_code = invite_code
        await user.save()

        olog.info(f"用户 {user.username} 已被提升为舰长，邀请码为: {invite_code}")

    @auth_required(['admin'])
    async def revoke_captain(self, data: Dict[str, Any]):
        """撤销用户的舰长权限"""
        target_user_id = data.get('target_user_id')

        # 输入验证
        if not target_user_id:
            raise MException("target_user_id 不能为空")

        # 查询用户
        user = await User.find_one(User.id == PydanticObjectId(target_user_id))
        if not user:
            raise MException("用户不存在")

        # 检查用户是否是舰长
        if 'captain' not in user.roles:
            raise MException("该用户不是舰长")

        # 移除舰长权限
        user.roles = [role for role in user.roles if role != 'captain']
        await user.save()

        olog.info(f"用户 {user.username} 的舰长权限已被撤销")

    @auth_required(['admin'])
    async def query_captain_list(self, data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """查询所有舰长列表"""
        # 查询所有包含captain角色的用户
        captains = await User.find(
            User.roles == "captain"
        ).sort(-User.create_at).to_list()

        captain_dicts = [captain.to_dict() for captain in captains]
        olog.info(f"查询到 {len(captain_dicts)} 个舰长")
        return {'captains': captain_dicts}

    @auth_required(['admin'])
    async def query_promotable_users(self, data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """查询可以提升为舰长的用户"""
        # 查询具有user或creator权限，但不是舰长的用户
        users = await User.find(
            User.roles.in_(['user', 'creator']),
            User.roles.nin_(['captain', 'admin'])
        ).sort(-User.create_at).to_list()

        user_dicts = []
        for user in users:
            user_dict = user.to_dict()
            user_dict['can_be_captain'] = True
            user_dicts.append(user_dict)

        olog.info(f"查询到 {len(user_dicts)} 个可提升为舰长的用户")
        return {'users': user_dicts}
