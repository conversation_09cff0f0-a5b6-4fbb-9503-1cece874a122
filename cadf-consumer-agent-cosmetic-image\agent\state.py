from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class InputState(BaseModel):
    """输入状态定义"""
    product_image_path: str
    scene_image_path: str
    product_intro: str


class OutputState(BaseModel):
    """输出状态定义"""
    final_image_path: Optional[str] = None


class OverallState(InputState,OutputState):
    """整体状态定义，包含所有中间处理结果"""

    # 图片分类结果
    image_category: Optional[str] = None
    # AI生成的图片
    processed_image_path: Optional[str] = None

    # 图片-产品处理
    scene_product_info: Optional[Dict[str, Any]] = None
    product_info: Optional[Dict[str, Any]] = None

    # 图片-人物处理
    security_blocked: Optional[bool] = None
    image_elements: Optional[str] = None
    generated_prompt: Optional[str] = None

    # 文字-处理
    has_text: Optional[bool] = None
    ocr_text: Optional[str] = None
    caption_items: Optional[List[Dict[str, Any]]] = None
    
    
    