"use client";

import {<PERSON>, <PERSON><PERSON>, Card, CardContent, CardActions, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, IconButton, InputAdornment, LinearProgress, Paper, Stack, Grid, TextField, Typography} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {useCallback, useEffect, useState} from "react";
import {useSnackbar} from "notistack";
import {advAiGenerationTaskApi} from "@/api/adv-ai-generation-task-api";
import {Eye, MoreHorizontal, RefreshCw, Search, Trash2} from "lucide-react";
import InfiniteScrollList from "@/core/components/InfiniteScrollList";
import dayjs from "dayjs";

export default function TaskList() {
    const theme = useTheme();
    const { enqueueSnackbar } = useSnackbar();
    const [searchQuery, setSearchQuery] = useState("");
    const [tasks, setTasks] = useState([]);
    const [hasMore, setHasMore] = useState(true);
    const [currentPage, setCurrentPage] = useState(0);
    const rowsPerPage = 6;
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [taskToDeleteId, setTaskToDeleteId] = useState(null);

    const router = useRouter();

    const fetchTasks = useCallback(async (searchQuery = "", page = 0, isAppend = false) => {
        const status = "all";

        try {
            const response = await advAiGenerationTaskApi.query_all(
                searchQuery,
                page + 1,
                rowsPerPage
            );

            if (response && Array.isArray(response.results)) {
                const mappedTasks = response.results.map(item => ({
                    id: item._id,
                    name: item.task_name,
                    image_url: item.image_url,
                    create_time: item.create_at ? dayjs.unix(item.create_at) : null,
                    total_materials: item.total_materials,
                    image_completed_count: item.image_completed_count,
                    text_completed_count: item.text_completed_count,
                    image_processing_count: item.image_processing_count,
                    text_processing_count: item.text_processing_count,
                    image_failed_count: item.image_failed_count,
                    text_failed_count: item.text_failed_count,
                    image_pending_count: item.image_pending_count,
                    text_pending_count: item.text_pending_count,
                    completed_materials_count: item.completed_materials_count,
                    processing_materials_count: item.processing_materials_count,
                    pending_materials_count: item.pending_materials_count,
                    failed_materials_count: item.failed_materials_count,
                    status: item.status,
                }));
                
                if (isAppend) {
                    setTasks(prev => [...prev, ...mappedTasks]);
                } else {
                    setTasks(mappedTasks);
                }
                
                setHasMore(response.results.length === rowsPerPage);
            } else {
                if (!isAppend) {
                    setTasks([]);
                }
                setHasMore(false);
                if (!isAppend) {
                    enqueueSnackbar("获取任务列表失败或数据格式不正确", { variant: 'error' });
                }
            }
        } catch (err) {
            if (!isAppend) {
                setTasks([]);
                enqueueSnackbar("获取任务列表时发生错误", { variant: 'error' });
            }
            setHasMore(false);
            console.error("Error fetching tasks:", err);
        }
    }, [rowsPerPage]);

    useEffect(() => {
        setTasks([]);
        setCurrentPage(0);
        setHasMore(true);
        fetchTasks(searchQuery, 0, false);
    }, [searchQuery, fetchTasks]);

    const handleDelete = useCallback(async (taskId) => {
        setTaskToDeleteId(taskId);
        setIsDeleteDialogOpen(true);
    }, []);

    const confirmDeleteHandler = useCallback(async () => {
        if (!taskToDeleteId) return;

        try {
            const response = await advAiGenerationTaskApi.delete(taskToDeleteId);

            enqueueSnackbar("任务删除成功", { variant: 'success' });
            setTasks([]);
            setCurrentPage(0);
            setHasMore(true);
            fetchTasks(searchQuery, 0, false);

            handleCloseDeleteDialog();
        } catch (err) {
            setError(`删除任务时发生错误: ${err.message || '未知错误'}`);
            enqueueSnackbar(`删除任务时发生错误: ${err.message || '未知错误'}`, { variant: 'error' });
            console.error("Error deleting task:", err);

            handleCloseDeleteDialog();
        }
    }, [taskToDeleteId, fetchTasks, searchQuery]);

    const handleCloseDeleteDialog = () => {
        setIsDeleteDialogOpen(false);
        setTaskToDeleteId(null);
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };


    const handleSearchSubmit = useCallback(() => {
        setTasks([]);
        setCurrentPage(0);
        setHasMore(true);
        fetchTasks(searchQuery, 0, false);
    }, [searchQuery, fetchTasks]);

    const handleRefresh = useCallback(() => {
        setTasks([]);
        setCurrentPage(0);
        setHasMore(true);
        fetchTasks(searchQuery, 0, false);
    }, [fetchTasks, searchQuery]);

    const handleLoadMore = useCallback(async () => {
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);
        await fetchTasks(searchQuery, nextPage, true);
    }, [currentPage, searchQuery, fetchTasks]);


    // 获取分离的图片和文本状态统计
    const getStatusStatistics = (task) => {
        const totalCount = task.total_materials ?? 0;
        
        // 图片状态统计 - 使用后端返回的数据
        const imageCompleted = task.image_completed_count ?? 0;
        const imageProcessing = task.image_processing_count ?? 0;
        const imageFailed = task.image_failed_count ?? 0;
        const imagePending = task.image_pending_count ?? 0;

        // 文本状态统计 - 使用后端返回的数据
        const textCompleted = task.text_completed_count ?? 0;
        const textProcessing = task.text_processing_count ?? 0;
        const textFailed = task.text_failed_count ?? 0;
        const textPending = task.text_pending_count ?? 0;

        return {
            total: totalCount,
            image: {
                pending: imagePending,
                processing: imageProcessing,
                completed: imageCompleted,
                failed: imageFailed
            },
            text: {
                pending: textPending,
                processing: textProcessing,
                completed: textCompleted,
                failed: textFailed
            }
        };
    };

    const getTaskOverallStatus = (task) => {
        if (!task) return {label: "-", color: "default"};

        const totalCount = task.total_materials ?? 0;
        const completed = task.completed_materials_count ?? 0;
        const processing = task.processing_materials_count ?? 0;
        const failed = task.failed_materials_count ?? 0;

        // 如果总数为0，任务还未开始
        if (totalCount === 0) return {label: "待生成", color: "default"};

        // 如果所有素材都完成了，任务完成
        if (completed === totalCount) {
            return {label: "已完成", color: "success"};
        }

        // 如果有失败的素材
        if (failed > 0) {
            return {label: "部分失败", color: "error"};
        }

        // 如果有任何进度，任务进行中
        if (processing > 0 || completed > 0) {
            return {label: "生成中", color: "warning"};
        }

        // 否则任务还在等待
        return {label: "待生成", color: "default"};
    };

    const renderTaskCard = (task) => {
        const status = getTaskOverallStatus(task);
        const stats = getStatusStatistics(task);
        
        return (
            <Card 
                sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    borderRadius: 3,
                    border: `1px solid ${theme.palette.grey[200]}`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                    overflow: 'hidden',
                    position: 'relative',
                    backgroundColor: theme.palette.background.paper,
                    '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                        borderColor: theme.palette.primary.main,
                    }
                }}
            >
                {/* 渐变状态指示条 */}
                <Box
                    sx={{
                        height: 3,
                        width: '100%',
                        backgroundColor: status.color === 'success'
                            ? theme.palette.success.main
                            : status.color === 'warning'
                                ? theme.palette.warning.main
                                : status.color === 'error'
                                    ? theme.palette.error.main
                                    : theme.palette.grey[400],
                    }}
                />
                
                <CardContent sx={{ flexGrow: 1, p: { xs: 2.5, md: 3 } }}>
                    {/* 产品名称与图片区域 */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2.5 }}>
                        {/* 产品图片 */}
                        {task.image_url && (
                            <Box
                                sx={{
                                    width: { xs: 60, md: 72 },
                                    height: { xs: 60, md: 72 },
                                    borderRadius: 2.5,
                                    overflow: 'hidden',
                                    border: `1px solid ${theme.palette.grey[200]}`,
                                    flexShrink: 0,
                                    bgcolor: theme.palette.grey[50],
                                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08)'
                                }}
                            >
                                <img
                                    src={task.image_url}
                                    alt="产品图片"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover'
                                    }}
                                    onError={(e) => {
                                        e.target.style.display = 'none';
                                    }}
                                />
                            </Box>
                        )}
                        
                        {/* 产品名称和时间 */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Typography 
                                variant="h6" 
                                component="h3"
                                sx={{ 
                                    fontWeight: 600,
                                    fontSize: { xs: '1.05rem', md: '1.15rem' },
                                    lineHeight: 1.4,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    color: theme.palette.text.primary,
                                    mb: 1,
                                    letterSpacing: '-0.01em'
                                }}
                                title={task.name}
                            >
                                {task.name}
                            </Typography>
                            
                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem', opacity: 0.8 }}>
                                {task.create_time ? task.create_time.format('YYYY-MM-DD HH:mm') : "-"}
                            </Typography>
                        </Box>
                    </Box>

                    {/* 详细统计区域 */}
                    <Box sx={{ mb: 2.5 }}>
                        <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'space-between',
                            mb: 2,
                            p: { xs: 1.5, md: 2 },
                            borderRadius: 2.5,
                            bgcolor: 'rgba(25, 118, 210, 0.04)',
                            border: `1px solid rgba(25, 118, 210, 0.08)`
                        }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: theme.palette.text.primary, fontSize: '0.9rem' }}>
                                素材状态统计
                            </Typography>
                            <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.primary.main, fontSize: { xs: '1.05rem', md: '1.15rem' } }}>
                                总计: {stats.total}
                            </Typography>
                        </Box>
                        
                        {/* 图片状态统计 */}
                        <Box sx={{ mb: 2.5 }}>
                            <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                mb: 1.5,
                                p: 1.5,
                                borderRadius: 2,
                                bgcolor: 'rgba(103, 58, 183, 0.05)',
                                border: `1px solid rgba(103, 58, 183, 0.1)`
                            }}>
                                <Box
                                    sx={{
                                        width: 12,
                                        height: 12,
                                        borderRadius: '50%',
                                        bgcolor: '#673ab7',
                                        mr: 1.5,
                                        boxShadow: '0 2px 4px rgba(103, 58, 183, 0.3)'
                                    }}
                                />
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#673ab7' }}>
                                    图片生成状态
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'nowrap' }}>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: 1.5, 
                                    borderRadius: 3,
                                    background: 'linear-gradient(135deg, rgba(158, 158, 158, 0.08) 0%, rgba(189, 189, 189, 0.05) 100%)',
                                    border: `1px solid rgba(158, 158, 158, 0.15)`,
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 4px 12px rgba(158, 158, 158, 0.15)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 800, color: theme.palette.grey[700], fontSize: '1.1rem', mb: 0.5 }}>
                                        {stats.image.pending}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        待生成
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(255, 152, 0, 0.05)',
                                    border: `1px solid rgba(255, 152, 0, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(255, 152, 0, 0.08)',
                                        borderColor: 'rgba(255, 152, 0, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.warning.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.image.processing}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        生成中
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(76, 175, 80, 0.05)',
                                    border: `1px solid rgba(76, 175, 80, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(76, 175, 80, 0.08)',
                                        borderColor: 'rgba(76, 175, 80, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.success.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.image.completed}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        已完成
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(244, 67, 54, 0.05)',
                                    border: `1px solid rgba(244, 67, 54, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(244, 67, 54, 0.08)',
                                        borderColor: 'rgba(244, 67, 54, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.error.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.image.failed}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        失败
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>

                        {/* 文本状态统计 */}
                        <Box sx={{ mb: 0 }}>
                            <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                mb: 1.5,
                                p: { xs: 1.25, md: 1.5 },
                                borderRadius: 2,
                                bgcolor: 'rgba(0, 150, 136, 0.04)',
                                border: `1px solid rgba(0, 150, 136, 0.08)`
                            }}>
                                <Box
                                    sx={{
                                        width: 12,
                                        height: 12,
                                        borderRadius: '50%',
                                        bgcolor: '#009688',
                                        mr: 1.5,
                                        boxShadow: '0 2px 4px rgba(0, 150, 136, 0.3)'
                                    }}
                                />
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#009688', fontSize: '0.85rem' }}>
                                    文本生成状态
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', gap: { xs: 0.75, md: 1 }, flexWrap: 'nowrap' }}>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: 1.5, 
                                    borderRadius: 3,
                                    background: 'linear-gradient(135deg, rgba(158, 158, 158, 0.08) 0%, rgba(189, 189, 189, 0.05) 100%)',
                                    border: `1px solid rgba(158, 158, 158, 0.15)`,
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 4px 12px rgba(158, 158, 158, 0.15)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 800, color: theme.palette.grey[700], fontSize: '1.1rem', mb: 0.5 }}>
                                        {stats.text.pending}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        待生成
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(255, 152, 0, 0.05)',
                                    border: `1px solid rgba(255, 152, 0, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(255, 152, 0, 0.08)',
                                        borderColor: 'rgba(255, 152, 0, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.warning.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.text.processing}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        生成中
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(76, 175, 80, 0.05)',
                                    border: `1px solid rgba(76, 175, 80, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(76, 175, 80, 0.08)',
                                        borderColor: 'rgba(76, 175, 80, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.success.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.text.completed}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        已完成
                                    </Typography>
                                </Box>
                                <Box sx={{ 
                                    flex: 1,
                                    minWidth: 0,
                                    textAlign: 'center', 
                                    p: { xs: 1, md: 1.25 }, 
                                    borderRadius: 2,
                                    backgroundColor: 'rgba(244, 67, 54, 0.05)',
                                    border: `1px solid rgba(244, 67, 54, 0.12)`,
                                    transition: 'all 0.15s ease-in-out',
                                    '&:hover': {
                                        backgroundColor: 'rgba(244, 67, 54, 0.08)',
                                        borderColor: 'rgba(244, 67, 54, 0.2)'
                                    }
                                }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.error.dark, fontSize: { xs: '0.95rem', md: '1.05rem' }, mb: 0.25 }}>
                                        {stats.text.failed}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                                        失败
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </CardContent>
                
                {/* 操作按钮区域 */}
                <Box sx={{ 
                    p: { xs: 2.5, md: 3 }, 
                    pt: 0, 
                    display: 'flex', 
                    justifyContent: 'flex-end', 
                    gap: { xs: 1.5, md: 2 },
                    borderTop: `1px solid ${theme.palette.grey[200]}`,
                    flexDirection: { xs: 'column', sm: 'row' }
                }}>
                    <Button
                        size="medium"
                        variant="contained"
                        color="primary"
                        onClick={() =>
                            router.push(`/protected/ai-image-text-generation/${task.id}`)
                        }
                        startIcon={<Eye size={18}/>}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1.2,
                            fontSize: '0.9rem',
                            boxShadow: 'none',
                            backgroundColor: theme.palette.primary.main,
                            '&:hover': {
                                backgroundColor: theme.palette.primary.dark,
                                boxShadow: 'none',
                                transform: 'translateY(-1px)'
                            },
                            '&:active': {
                                transform: 'translateY(0px)'
                            },
                            transition: 'all 0.15s ease-in-out'
                        }}
                    >
                        查看详情
                    </Button>
                    <Button
                        size="medium"
                        variant="outlined"
                        color="error"
                        onClick={() => handleDelete(task.id)}
                        startIcon={<Trash2 size={18}/>}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1.2,
                            fontSize: '0.9rem',
                            borderWidth: '1px',
                            '&:hover': {
                                borderWidth: '1px',
                                backgroundColor: 'rgba(244, 67, 54, 0.04)',
                                transform: 'translateY(-1px)'
                            },
                            '&:active': {
                                transform: 'translateY(0px)'
                            },
                            transition: 'all 0.15s ease-in-out'
                        }}
                    >
                        删除
                    </Button>
                </Box>
            </Card>
        );
    };

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{mb: 4}}
            >
                <Typography 
                    variant="h4" 
                    sx={{ 
                        fontWeight: 600, 
                        color: theme.palette.text.primary,
                        fontSize: { xs: '1.75rem', md: '2.125rem' },
                        letterSpacing: '-0.02em'
                    }}
                >
                    任务列表
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    onClick={() =>
                        router.push("/protected/ai-image-text-generation/create")
                    }
                    sx={{
                        px: 3.5,
                        py: 1.5,
                        borderRadius: 2.5,
                        textTransform: "none",
                        fontWeight: 600,
                        fontSize: '0.95rem',
                        boxShadow: 'none',
                        backgroundColor: theme.palette.primary.main,
                        '&:hover': {
                            backgroundColor: theme.palette.primary.dark,
                            boxShadow: 'none',
                            transform: 'translateY(-1px)'
                        },
                        '&:active': {
                            transform: 'translateY(0px)'
                        },
                        transition: 'all 0.15s ease-in-out',
                    }}
                >
                    生成AI图文
                </Button>
            </Stack>

            <Paper
                elevation={0}
                sx={{
                    p: 0,
                    mb: 4,
                    overflow: "hidden",
                    borderRadius: 2.5,
                    border: `1px solid ${theme.palette.grey[200]}`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                    backgroundColor: theme.palette.background.paper,
                }}
            >
                <Box
                    sx={{
                        p: { xs: 2.5, md: 3 },
                        bgcolor: 'rgba(248, 249, 250, 0.8)',
                        color: theme.palette.text.primary,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        flexDirection: { xs: 'column', sm: 'row' },
                        gap: { xs: 2, sm: 0 }
                    }}
                >
                    <Typography 
                        variant="subtitle1" 
                        sx={{ 
                            fontWeight: 600, 
                            fontSize: '1.1rem',
                            color: theme.palette.text.primary,
                            letterSpacing: '-0.01em'
                        }}
                    >
                        任务列表
                    </Typography>
                    <Stack direction="row" spacing={2} alignItems="center">
                        <TextField
                            placeholder="搜索任务名称"
                            variant="outlined"
                            size="small"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            sx={{
                                width: { xs: '100%', sm: 280 },
                                "& .MuiOutlinedInput-root": {
                                    borderRadius: 2,
                                    bgcolor: theme.palette.background.paper,
                                    fontSize: '0.9rem',
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        borderColor: theme.palette.grey[300],
                                    },
                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                        borderColor: theme.palette.primary.main,
                                    },
                                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                        borderColor: theme.palette.primary.main,
                                        borderWidth: '1px',
                                    },
                                },
                            }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <Search size={18} color={theme.palette.text.secondary}/>
                                    </InputAdornment>
                                ),
                            }}
                            onKeyDown={(event) => {
                                if (event.key === 'Enter') {
                                    handleSearchSubmit();
                                }
                            }}
                        />
                        <IconButton
                            onClick={handleSearchSubmit}
                            size="small"
                            sx={{
                                bgcolor: theme.palette.background.paper, 
                                borderRadius: 1.5,
                                border: `1px solid ${theme.palette.grey[300]}`,
                                color: theme.palette.text.secondary,
                                '&:hover': {
                                    bgcolor: theme.palette.action.hover,
                                    borderColor: theme.palette.grey[400],
                                    color: theme.palette.text.primary,
                                },
                                '&:active': {
                                    transform: 'scale(0.95)'
                                },
                                transition: 'all 0.15s ease-in-out',
                            }}
                        >
                            <Search size={18}/>
                        </IconButton>
                        <IconButton
                            onClick={handleRefresh}
                            size="small"
                            sx={{
                                bgcolor: theme.palette.background.paper, 
                                borderRadius: 1.5,
                                border: `1px solid ${theme.palette.grey[300]}`,
                                color: theme.palette.text.secondary,
                                '&:hover': {
                                    bgcolor: theme.palette.action.hover,
                                    borderColor: theme.palette.grey[400],
                                    color: theme.palette.text.primary,
                                },
                                '&:active': {
                                    transform: 'scale(0.95)'
                                },
                                transition: 'all 0.15s ease-in-out',
                            }}
                        >
                            <RefreshCw size={18}/>
                        </IconButton>
                    </Stack>
                </Box>

                <Box sx={{p: { xs: 2, md: 3 }}}>
                    <InfiniteScrollList
                        items={tasks}
                        renderItem={(task) => renderTaskCard(task)}
                        loadMore={handleLoadMore}
                        hasMore={hasMore}
                        gridColumns={{ xs: 12, lg: 6, xl: 4 }}
                    />
                </Box>
            </Paper>


            <Dialog
                open={isDeleteDialogOpen}
                onClose={handleCloseDeleteDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                sx={{
                    '& .MuiDialog-paper': {
                        borderRadius: 3,
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                        border: `1px solid ${theme.palette.grey[200]}`,
                        minWidth: { xs: '90%', sm: 400 }
                    }
                }}
            >
                <DialogTitle 
                    id="alert-dialog-title" 
                    sx={{ 
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        pb: 1
                    }}
                >
                    {"确认删除"}
                </DialogTitle>
                <DialogContent sx={{ pb: 2 }}>
                    <DialogContentText 
                        id="alert-dialog-description"
                        sx={{
                            fontSize: '0.95rem',
                            color: theme.palette.text.secondary
                        }}
                    >
                        确定要删除这个任务吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 1, gap: 1.5 }}>
                    <Button 
                        onClick={handleCloseDeleteDialog}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1,
                            color: theme.palette.text.secondary,
                            '&:hover': {
                                backgroundColor: theme.palette.action.hover
                            }
                        }}
                    >
                        取消
                    </Button>
                    <Button 
                        onClick={confirmDeleteHandler} 
                        color="error" 
                        variant="contained"
                        autoFocus
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            px: 3,
                            py: 1,
                            boxShadow: 'none',
                            '&:hover': {
                                boxShadow: 'none'
                            }
                        }}
                    >
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>

        </Container>
    );
}
