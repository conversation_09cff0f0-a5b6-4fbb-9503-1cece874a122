"use client";

import {Box, Button, Grid, Paper, Stack, Typography, useTheme} from '@mui/material';
import {useRouter} from 'next/navigation';
import {ArrowRight, Check, Sparkles} from 'lucide-react';

export default function ProductGuidePage() {
    const theme = useTheme();
    const router = useRouter();

    const handleGoToAIGeneration = () => {
        router.push('/protected/ai-image-text-generation/create');
    };

    const handleBackToProducts = () => {
        router.push('/protected/product-management');
    };

    const features = [
        '精美的产品宣传图片',
        '吸引人的营销文案',
        '多平台适配的内容格式',
        '个性化的品牌风格'
    ];

    return (
        <Box sx={{
            minHeight: '100vh',
            bgcolor: theme.palette.grey[50],
            py: {xs: 3, md: 6}
        }}>
            <Grid container sx={{width: '100%'}} justifyContent="center">
                <Grid size={{xs: 12, sm: 10, md: 8, lg: 6}}>
                    <Stack spacing={4} alignItems="center">
                        
                        {/* 成功图标区 */}
                        <Box sx={{
                            width: 72,
                            height: 72,
                            borderRadius: '50%',
                            bgcolor: theme.palette.success.main,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: `0 8px 32px ${theme.palette.success.main}25`
                        }}>
                            <Check size={36} color="white" />
                        </Box>

                        {/* 标题区 */}
                        <Stack spacing={2} textAlign="center" sx={{maxWidth: 480}}>
                            <Typography 
                                variant="h4" 
                                component="h1" 
                                sx={{
                                    fontWeight: 600,
                                    color: theme.palette.text.primary,
                                    fontSize: {xs: '1.75rem', md: '2.125rem'}
                                }}
                            >
                                产品保存成功
                            </Typography>
                            <Typography 
                                variant="body1" 
                                sx={{
                                    color: theme.palette.text.secondary,
                                    lineHeight: 1.5,
                                    fontSize: '1rem'
                                }}
                            >
                                您的产品信息已成功保存。现在可以使用AI功能为您的产品创建营销内容。
                            </Typography>
                        </Stack>

                        {/* AI功能卡片 */}
                        <Paper 
                            elevation={0}
                            sx={{
                                width: '100%',
                                maxWidth: 480,
                                borderRadius: 3,
                                bgcolor: 'white',
                                border: `1px solid ${theme.palette.grey[200]}`,
                                overflow: 'hidden'
                            }}
                        >
                            <Box sx={{p: {xs: 3, md: 4}}}>
                                <Stack spacing={3} alignItems="center">
                                    
                                    {/* AI图标 */}
                                    <Box sx={{
                                        width: 56,
                                        height: 56,
                                        borderRadius: 2,
                                        bgcolor: theme.palette.primary.main,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        <Sparkles size={28} color="white" />
                                    </Box>

                                    {/* 功能标题 */}
                                    <Typography 
                                        variant="h6" 
                                        sx={{
                                            fontWeight: 600,
                                            color: theme.palette.text.primary,
                                            textAlign: 'center'
                                        }}
                                    >
                                        AI图文生成
                                    </Typography>

                                    {/* 功能列表 */}
                                    <Stack spacing={1.5} sx={{width: '100%'}}>
                                        {features.map((feature, index) => (
                                            <Box 
                                                key={index}
                                                sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 1.5
                                                }}
                                            >
                                                <Box sx={{
                                                    width: 6,
                                                    height: 6,
                                                    borderRadius: '50%',
                                                    bgcolor: theme.palette.primary.main,
                                                    flexShrink: 0
                                                }} />
                                                <Typography 
                                                    variant="body2" 
                                                    sx={{
                                                        color: theme.palette.text.secondary,
                                                        lineHeight: 1.4
                                                    }}
                                                >
                                                    {feature}
                                                </Typography>
                                            </Box>
                                        ))}
                                    </Stack>

                                    {/* 主要按钮 */}
                                    <Button
                                        variant="contained"
                                        size="large"
                                        endIcon={<ArrowRight size={18} />}
                                        onClick={handleGoToAIGeneration}
                                        sx={{
                                            borderRadius: 2,
                                            px: 4,
                                            py: 1.5,
                                            fontSize: '1rem',
                                            fontWeight: 500,
                                            textTransform: 'none',
                                            width: '100%',
                                            boxShadow: 'none',
                                            '&:hover': {
                                                boxShadow: `0 4px 12px ${theme.palette.primary.main}25`
                                            }
                                        }}
                                    >
                                        开始AI创作
                                    </Button>
                                </Stack>
                            </Box>
                        </Paper>

                        {/* 次要按钮 */}
                        <Button
                            variant="text"
                            onClick={handleBackToProducts}
                            sx={{
                                color: theme.palette.text.secondary,
                                textTransform: 'none',
                                fontSize: '0.875rem',
                                fontWeight: 400
                            }}
                        >
                            稍后再说，返回产品管理
                        </Button>
                    </Stack>
                </Grid>
            </Grid>
        </Box>
    );
}