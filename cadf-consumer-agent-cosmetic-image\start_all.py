import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../common-module')))
import asyncio

from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import start_consumer_server

if __name__ == '__main__':
    try:
        asyncio.run(start_consumer_server())
    except KeyboardInterrupt:
        olog.info("主线程收到退出信号，消费者结束。")
    finally:
        olog.info("程序退出。")
