{"name": "omni-js", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "^6.1.10", "@mui/material": "^6.1.10", "@mui/material-nextjs": "^6.1.9", "@mui/x-charts": "^7.23.2", "@mui/x-date-pickers": "^7.23.0", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.8.4", "dayjs": "^1.11.13", "howler": "^2.2.4", "js-cookie": "^3.0.5", "lucide-react": "^0.483.0", "moment": "^2.30.1", "next": "^15.0.3", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-syntax-highlighter": "^15.5.0", "recorder-core": "^1.3.24102001", "remark-external-links": "^9.0.1", "remark-gfm": "^4.0.0"}}