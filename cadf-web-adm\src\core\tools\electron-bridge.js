/*
// 示例：如何在你的 React 或 Vue 组件中使用 invokeElectron
import { invokeElectron } from '@/core/tools/electron-bridge'; // 确保路径正确

async function handleOpenLoginWindow() {
  try {
    const result = await invokeElectron('open-xhs-login');
    console.log('打开小红书登录窗口结果:', result); // { success: true, message: 'Window opened.' } 或 { success: true, message: 'Window already open.' }
    if (result.success) {
      // 可以在这里处理成功打开窗口后的逻辑
    } else {
      console.error('无法打开小红书登录窗口:', result.message);
    }
  } catch (error) {
    console.error('调用 open-xhs-login 时出错:', error);
    // 处理调用错误，例如 Electron API 不可用或 IPC 通信失败
  }
}

async function handleGetCookies() {
  try {
    const result = await invokeElectron('get-xhs-cookies-and-close');
    console.log('获取 Cookie 结果:', result);
    if (result.success) {
      console.log('获取到的 Cookie:', result.cookies);
      // 在这里处理获取到的 Cookie
    } else {
      console.error('无法获取 Cookie:', result.message);
    }
  } catch (error) {
    console.error('调用 get-xhs-cookies-and-close 时出错:', error);
  }
}

// 在你的组件中调用这些函数，例如绑定到按钮点击事件
// <button onClick={handleOpenLoginWindow}>打开小红书登录</button>
// <button onClick={handleGetCookies}>获取并关闭</button>
*/

export const isElectron = () => {
    return window.electronAPI !== undefined;
};

export const invokeElectron = async (channel, ...args) => {
    if (!isElectron()) {
        throw new Error('Electron API 不可用');
    }

    try {
        return await window.electronAPI.invoke(channel, ...args);
    } catch (error) {
        console.error(`在通道 '${channel}' 上调用 Electron API 时发生错误:`, error);
        throw error;
    }
};


