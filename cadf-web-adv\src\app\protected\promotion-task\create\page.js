"use client";

import {useEffect, useState} from "react";
import {Box, Button, Container, Grid, IconButton, Paper, Stack, TextField, Typography, useMediaQuery, Drawer, Chip, Avatar, Badge, FormControl, InputLabel, Select, MenuItem, Slider} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {ChevronLeft, Plus, RefreshCcw, Search, Package, X, Send} from "lucide-react";
import InfiniteScrollList from "@/core/components/InfiniteScrollList";
import {advPromotionTaskApi} from "@/api/adv-promotion-task-api";
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DatePicker} from '@mui/x-date-pickers/DatePicker';
import {useSnackbar} from 'notistack';

// 添加全局样式
const globalStyles = `
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// 在组件头部添加样式
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = globalStyles;
  document.head.appendChild(styleSheet);
}

dayjs.locale('zh-cn');

// 通用数据处理函数
const transformProductItem = (item) => {
    const imageUrl = item.image_url || `https://placehold.co/300x400/757de8/ffffff?text=${item.title}`;
    
    return {
        ...item,
        id: item._id,
        name: item.title,
        description: item.description || '',
        image: imageUrl,
        material_count: item.material_count || 0,
        selected: false
    };
};

// 产品数据加载钩子
const useProductLoader = () => {
    const [items, setItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [hasMore, setHasMore] = useState(true);
    const [searchInput, setSearchInput] = useState("");
    const itemsPerPage = 5;

    const loadData = async (searchTerm = "", pageNum = 1, append = false) => {
        if (pageNum === 1) {
            setLoading(true);
            setError(null);
        }
        
        try {
            const response = await advPromotionTaskApi.queryProductOverview(
                searchTerm, pageNum, itemsPerPage
            );
            
            const { results = [], total = 0 } = response || {};
            
            const transformedItems = results.map(item => transformProductItem(item));
            setItems(prev => append ? [...prev, ...transformedItems] : transformedItems);
            setTotalCount(total);
            setHasMore(pageNum < Math.ceil(total / itemsPerPage));
            setPage(pageNum + 1);
            
        } catch (err) {
            console.error('获取产品失败:', err);
            setError('获取产品数据失败，请稍后重试');
            if (!append) {
                setItems([]);
                setTotalCount(0);
                setHasMore(false);
            }
        } finally {
            setLoading(false);
        }
    };

    const loadMore = () => loadData(searchInput, page, true);
    const search = () => {
        setPage(1);
        loadData(searchInput, 1, false);
    };
    const refresh = () => {
        setSearchInput("");
        setPage(1);
        loadData("", 1, false);
    };

    return {
        items, setItems, loading, error, totalCount, hasMore,
        searchInput, setSearchInput, loadData, loadMore, search, refresh
    };
};

// 产品选择抽屉组件
const ProductSelectionDrawer = ({ 
    open, onClose, isMobile, 
    data, onToggle, 
    theme, router 
}) => {
    const { items, loading, error, searchInput, setSearchInput, search, refresh, loadMore, hasMore } = data;

    return (
        <Box sx={{ width: isMobile ? '100vw' : 600, height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="medium">选择产品</Typography>
                    <IconButton 
                        onClick={onClose}
                        sx={{
                            borderRadius: 2,
                            '&:hover': {
                                bgcolor: 'action.hover'
                            }
                        }}
                    >
                        <X size={20} />
                    </IconButton>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                    请选择要进行推广的产品
                </Typography>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 2 }}>
                    <TextField
                        fullWidth
                        placeholder="搜索产品名称或描述"
                        variant="outlined"
                        size="small"
                        value={searchInput}
                        onChange={(e) => setSearchInput(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && search()}
                    />
                    <IconButton 
                        onClick={search} 
                        color="primary"
                        sx={{ borderRadius: 2 }}
                    >
                        <Search size={20}/>
                    </IconButton>
                    <IconButton 
                        onClick={refresh}
                        sx={{ borderRadius: 2 }}
                    >
                        <RefreshCcw size={20}/>
                    </IconButton>
                </Stack>
            </Box>
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
                {loading ? (
                    <Box sx={{ p: 4, textAlign: "center" }}>
                        <Typography color="text.secondary">正在加载产品...</Typography>
                    </Box>
                ) : error ? (
                    <Box sx={{ p: 4, textAlign: "center" }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : (
                    <InfiniteScrollList
                        items={[
                            { id: 'add-new', isAddCard: true },
                            ...items
                        ]}
                        renderItem={(item) => {
                            if (item.isAddCard) {
                                return (
                                    <Paper
                                        onClick={() => router.push('/protected/product-management/new')}
                                        sx={{
                                            p: 3,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderStyle: 'dashed',
                                            borderWidth: 2,
                                            borderColor: 'primary.main',
                                            backgroundColor: 'primary.50',
                                            cursor: 'pointer',
                                            minHeight: 100,
                                            borderRadius: 3,
                                            transition: 'all 0.2s ease-in-out',
                                            '&:hover': { 
                                                bgcolor: 'primary.100',
                                                borderColor: 'primary.dark',
                                                transform: 'translateY(-2px)',
                                                boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.1)'
                                            }
                                        }}
                                    >
                                        <Plus size={20} color={theme.palette.primary.main} />
                                        <Typography variant="body2" color="primary.main" sx={{ mt: 0.5 }}>
                                            添加新产品
                                        </Typography>
                                    </Paper>
                                );
                            }
                            
                            const isDisabled = !item.material_count || item.material_count <= 0;
                            
                            return (
                                <Paper
                                    onClick={() => {
                                        if (!isDisabled) {
                                            onToggle(item.id);
                                            onClose();
                                        }
                                    }}
                                    sx={{
                                        p: 2.5,
                                        display: 'flex',
                                        alignItems: 'center',
                                        cursor: isDisabled ? 'not-allowed' : 'pointer',
                                        border: item.selected ? 2 : 1,
                                        borderColor: item.selected ? 'primary.main' : 'divider',
                                        bgcolor: isDisabled ? 'grey.100' : (item.selected ? 'primary.50' : 'background.paper'),
                                        opacity: isDisabled ? 0.6 : 1,
                                        borderRadius: 2,
                                        transition: 'all 0.15s ease-in-out',
                                        '&:hover': {
                                            bgcolor: isDisabled ? 'grey.100' : (item.selected ? 'primary.100' : 'action.hover'),
                                            transform: isDisabled ? 'none' : 'translateY(-1px)',
                                            boxShadow: isDisabled ? 'none' : '0px 2px 8px rgba(0, 0, 0, 0.08)'
                                        }
                                    }}
                                >
                                    <Avatar src={item.image} sx={{ width: 48, height: 48, mr: 2 }}>
                                        <Package size={20} />
                                    </Avatar>
                                    <Box sx={{ flex: 1 }}>
                                        <Typography variant="subtitle1" fontWeight="medium">
                                            {item.name}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 1,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden'
                                        }}>
                                            可用素材数: {item.material_count}
                                        </Typography>
                                        {isDisabled && (
                                            <Typography variant="caption" color="error">
                                                无可用素材
                                            </Typography>
                                        )}
                                    </Box>
                                    {item.selected && (
                                        <Chip size="small" label="已选择" color="primary" />
                                    )}
                                </Paper>
                            );
                        }}
                        loadMore={loadMore}
                        hasMore={hasMore}
                        gridColumns={{ xs: 12 }}
                    />
                )}
            </Box>
        </Box>
    );
};

export default function CreateTask() {
    const theme = useTheme();
    const router = useRouter();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const {enqueueSnackbar} = useSnackbar();
    
    // 使用自定义钩子管理产品数据
    const products = useProductLoader();
    
    // 简化后的状态管理
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [promotionCount, setPromotionCount] = useState(1);
    const [platform, setPlatform] = useState('小红书');
    const [endDate, setEndDate] = useState(dayjs().add(7, 'day'));
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 初始化数据加载
    useEffect(() => {
        products.loadData();
    }, []);

    // 简化的任务提交
    const submitTask = async () => {
        const selectedProduct = products.items.find(p => p.selected);
        
        if (!selectedProduct) {
            enqueueSnackbar('请选择一个产品进行推广', {variant: 'error'});
            return;
        }
        
        if (!platform) {
            enqueueSnackbar('请选择推广平台', {variant: 'error'});
            return;
        }
        
        if (!endDate || !endDate.isValid()) {
            enqueueSnackbar('请选择有效的结束日期', {variant: 'error'});
            return;
        }
        
        if (endDate.isBefore(dayjs())) {
            enqueueSnackbar('结束日期不能早于今天', {variant: 'error'});
            return;
        }
        
        if (!promotionCount || promotionCount <= 0 || promotionCount > selectedProduct?.material_count) {
            enqueueSnackbar(`推广数量必须在 1 到 ${selectedProduct?.material_count || 0} 之间`, {variant: 'error'});
            return;
        }

        setIsSubmitting(true);
        try {
            const product_id = selectedProduct._id || selectedProduct.id;
            const response = await advPromotionTaskApi.createPromotionTask(
                product_id,
                promotionCount,
                platform,
                endDate.format('YYYY-MM-DD')
            );
            enqueueSnackbar(response?.message || '创建成功', {variant: 'success'});
            router.push('/protected/promotion-task/success');
        } catch (err) {
            console.error("创建任务失败:", err);
            const errorMsg = err?.response?.data?.message || err.message || '创建推广任务时出错';
            enqueueSnackbar(errorMsg, {variant: 'error'});
        } finally {
            setIsSubmitting(false);
        }
    };

    // 简化的选择处理
    const handleProductToggle = (id) => {
        products.setItems(prev => prev.map(p => ({ 
            ...p, selected: p.id === id 
        })));
        const selectedProduct = products.items.find(p => p.id === id);
        if (selectedProduct) {
            setPromotionCount(Math.min(1, selectedProduct.material_count || 1));
        }
    };

    const handlePromotionCountChange = (event) => {
        const selectedProduct = products.items.find(p => p.selected);
        const value = parseInt(event.target.value, 10);
        const maxCount = selectedProduct?.material_count || 1;

        if (!isNaN(value)) {
            setPromotionCount(Math.max(1, Math.min(value, maxCount)));
        } else {
            setPromotionCount(1);
        }
    };

    const handlePlatformChange = (event) => setPlatform(event.target.value);
    const handleEndDateChange = (newValue) => setEndDate(newValue);

    // 简化的工具函数
    const canSubmit = () => {
        const selectedProduct = products.items.find(p => p.selected);
        return selectedProduct && platform && endDate && 
               endDate.isValid() && !endDate.isBefore(dayjs()) &&
               promotionCount > 0 && promotionCount <= (selectedProduct?.material_count || 0);
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
            <Container maxWidth={false} sx={{ py: {xs: 2, md: 4}, px: {xs: 1, sm: 2, md: 4} }}>
                <Paper sx={{
                    p: {xs: 3, sm: 4, md: 5},
                    mb: {xs: 2, md: 3},
                    borderRadius: 3,
                    boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
                    bgcolor: 'background.paper'
                }}>
                    {/* 页面标题和返回按钮 */}
                    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 4 }}>
                        <Typography variant={{xs: "h4", md: "h3"}} fontWeight="bold">
                            创建推广任务
                        </Typography>
                        <Button
                            variant="outlined"
                            onClick={() => router.push("/protected/promotion-task")}
                            startIcon={<ChevronLeft size={18}/>}
                            sx={{ 
                                display: { xs: 'none', sm: 'flex' },
                                borderRadius: 2,
                                textTransform: 'none',
                                fontWeight: 500
                            }}
                        >
                            返回列表
                        </Button>
                        <IconButton
                            onClick={() => router.push("/protected/promotion-task")}
                            sx={{ 
                                display: { xs: 'flex', sm: 'none' },
                                borderRadius: 2
                            }}
                        >
                            <ChevronLeft size={20} />
                        </IconButton>
                    </Box>

                    {/* 任务引导 */}
                    <Box sx={{ 
                        mb: 4, 
                        p: 3, 
                        borderRadius: 2,
                        bgcolor: 'grey.50',
                        border: '1px solid',
                        borderColor: 'grey.200'
                    }}>
                        <Typography variant="h6" fontWeight="600" color="text.primary" sx={{ mb: 2 }}>
                            创建步骤
                        </Typography>
                        <Stack spacing={1.5} sx={{ mb: 2 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Box sx={{ 
                                    width: 24, height: 24, 
                                    borderRadius: '50%', 
                                    bgcolor: 'primary.main', 
                                    color: 'white', 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    justifyContent: 'center',
                                    mr: 2,
                                    fontSize: '0.75rem',
                                    fontWeight: '600'
                                }}>1</Box>
                                <Typography variant="body1" fontWeight="500">
                                    选择推广产品
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Box sx={{ 
                                    width: 24, height: 24, 
                                    borderRadius: '50%', 
                                    bgcolor: 'primary.main', 
                                    color: 'white', 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    justifyContent: 'center',
                                    mr: 2,
                                    fontSize: '0.75rem',
                                    fontWeight: '600'
                                }}>2</Box>
                                <Typography variant="body1" fontWeight="500">
                                    配置推广参数
                                </Typography>
                            </Box>
                        </Stack>
                        <Typography variant="body2" color="text.secondary">
                            完成所有步骤后即可创建推广任务
                        </Typography>
                    </Box>

                    {/* 选择区域 */}
                    <Stack spacing={3} sx={{ mb: 4 }}>
                        {/* 产品选择区域 */}
                        <Paper sx={{ 
                            borderRadius: 2,
                            border: '1px solid',
                            borderColor: 'divider',
                            overflow: 'hidden',
                            boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)'
                        }}>
                            {/* 标题区域 */}
                            <Box sx={{ 
                                p: 3, 
                                bgcolor: 'background.paper',
                                borderBottom: '1px solid',
                                borderColor: 'divider'
                            }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Box sx={{ 
                                            width: 28, 
                                            height: 28, 
                                            borderRadius: '50%',
                                            bgcolor: 'primary.main',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mr: 2
                                        }}>
                                            <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold', fontSize: '0.75rem' }}>
                                                1
                                            </Typography>
                                        </Box>
                                        <Box>
                                            <Typography variant="subtitle1" sx={{ fontWeight: '600', color: 'text.primary' }}>
                                                选择推广产品
                                            </Typography>
                                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                                选择要进行推广的产品
                                            </Typography>
                                        </Box>
                                    </Box>
                                    <Button
                                        variant="contained"
                                        onClick={() => setDrawerOpen(true)}
                                        startIcon={<Plus size={16} />}
                                        sx={{
                                            borderRadius: 2,
                                            textTransform: 'none',
                                            fontWeight: 500,
                                            px: 3,
                                            py: 1
                                        }}
                                    >
                                        选择产品
                                    </Button>
                                </Box>
                            </Box>

                            {/* 内容区域 */}
                            <Box sx={{ p: 3, bgcolor: 'background.paper' }}>
                                {(() => {
                                    const selectedProduct = products.items.find(p => p.selected);
                                    if (selectedProduct) {
                                        return (
                                            <Paper sx={{ 
                                                p: 3, 
                                                display: 'flex', 
                                                alignItems: 'center',
                                                bgcolor: 'success.50',
                                                border: '1px solid',
                                                borderColor: 'success.main',
                                                borderRadius: 2,
                                                transition: 'all 0.15s ease-in-out'
                                            }}>
                                                <Avatar 
                                                    src={selectedProduct.image} 
                                                    sx={{ width: 48, height: 48, mr: 2 }}
                                                >
                                                    <Package size={20} />
                                                </Avatar>
                                                <Box sx={{ flex: 1 }}>
                                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                                                        {selectedProduct.name}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        可用素材数: {selectedProduct.material_count} 个
                                                    </Typography>
                                                </Box>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Chip size="small" label="已选择" color="success" />
                                                    <IconButton 
                                                        size="small" 
                                                        onClick={() => setDrawerOpen(true)}
                                                        sx={{ borderRadius: 1 }}
                                                    >
                                                        <Plus size={16} />
                                                    </IconButton>
                                                </Box>
                                            </Paper>
                                        );
                                    } else {
                                        return (
                                            <Paper 
                                                onClick={() => setDrawerOpen(true)}
                                                sx={{ 
                                                    p: 4, 
                                                    textAlign: 'center',
                                                    borderStyle: 'dashed',
                                                    borderWidth: 2,
                                                    borderColor: 'success.main',
                                                    bgcolor: 'success.50',
                                                    borderRadius: 2,
                                                    cursor: 'pointer',
                                                    transition: 'all 0.2s ease-in-out',
                                                    '&:hover': {
                                                        borderColor: 'success.dark',
                                                        bgcolor: 'success.100',
                                                        transform: 'translateY(-1px)'
                                                    }
                                                }}
                                            >
                                                <Package size={32} color={theme.palette.success.main} style={{ marginBottom: 8 }} />
                                                <Typography variant="body1" color="success.main" sx={{ fontWeight: 'medium' }}>
                                                    点击选择要进行推广的产品
                                                </Typography>
                                            </Paper>
                                        );
                                    }
                                })()}
                            </Box>
                        </Paper>

                        {/* 推广设置区域 */}
                        <Paper sx={{ 
                            borderRadius: 2,
                            border: '1px solid',
                            borderColor: 'divider',
                            overflow: 'hidden',
                            boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)'
                        }}>
                            {/* 标题区域 */}
                            <Box sx={{ 
                                p: 3, 
                                bgcolor: 'background.paper',
                                borderBottom: '1px solid',
                                borderColor: 'divider'
                            }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <Box sx={{ 
                                        width: 28, 
                                        height: 28, 
                                        borderRadius: '50%',
                                        bgcolor: 'primary.main',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mr: 2
                                    }}>
                                        <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold', fontSize: '0.75rem' }}>
                                            2
                                        </Typography>
                                    </Box>
                                    <Box>
                                        <Typography variant="subtitle1" sx={{ fontWeight: '600', color: 'text.primary' }}>
                                            设置推广详情
                                        </Typography>
                                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                            配置推广平台、数量和时间范围
                                        </Typography>
                                    </Box>
                                </Box>
                            </Box>

                            {/* 表单区域 */}
                            <Box sx={{ p: 3, bgcolor: 'background.paper' }}>
                                <Grid container spacing={3}>
                                    {/* 推广平台选择 */}
                                    <Grid size={{ xs: 12, md: 4 }}>
                                        <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 'medium', color: 'text.primary' }}>
                                            推广平台
                                        </Typography>
                                        <Paper sx={{ 
                                            p: 2.5, 
                                            borderRadius: 2,
                                            border: '1px solid',
                                            borderColor: platform === '小红书' ? 'primary.main' : 'divider',
                                            bgcolor: platform === '小红书' ? 'primary.50' : 'background.paper',
                                            minHeight: 120,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            cursor: 'pointer',
                                            transition: 'all 0.2s ease-in-out',
                                            '&:hover': {
                                                bgcolor: platform === '小红书' ? 'primary.100' : 'grey.50'
                                            }
                                        }}
                                        onClick={() => setPlatform('小红书')}
                                        >
                                            <Box sx={{ 
                                                display: 'flex', 
                                                alignItems: 'center',
                                                p: 1.5
                                            }}>
                                                <Box sx={{ 
                                                    width: 32, 
                                                    height: 32, 
                                                    borderRadius: '50%',
                                                    bgcolor: '#FF2442',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    mr: 2,
                                                    flexShrink: 0
                                                }}>
                                                    <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold' }}>
                                                        小
                                                    </Typography>
                                                </Box>
                                                <Box sx={{ flex: 1, minWidth: 0 }}>
                                                    <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                                                        小红书
                                                    </Typography>
                                                    {platform === '小红书' ? (
                                                        <Chip size="small" label="已选择" color="primary" />
                                                    ) : (
                                                        <Typography variant="caption" color="text.secondary">
                                                            点击选择
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </Box>
                                        </Paper>
                                    </Grid>

                                    {/* 推广数量设置 */}
                                    <Grid size={{ xs: 12, md: 4 }}>
                                        {(() => {
                                            const selectedProduct = products.items.find(p => p.selected);
                                            return (
                                                <Box>
                                                    <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 'medium', color: 'text.primary' }}>
                                                        推广数量
                                                    </Typography>
                                                    <Paper sx={{ 
                                                        p: 2.5, 
                                                        borderRadius: 2,
                                                        border: '1px solid',
                                                        borderColor: selectedProduct ? 'primary.main' : 'divider',
                                                        bgcolor: selectedProduct ? 'primary.50' : 'grey.50',
                                                        minHeight: 120,
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        justifyContent: 'center',
                                                        transition: 'all 0.2s ease-in-out'
                                                    }}>
                                                        <Box sx={{ textAlign: 'center' }}>
                                                            <Typography variant="caption" color="text.secondary" sx={{ mb: 1.5, display: 'block' }}>
                                                                最多 {selectedProduct?.material_count || 0} 个可用
                                                            </Typography>
                                                            <TextField
                                                                type="number"
                                                                value={promotionCount}
                                                                onChange={handlePromotionCountChange}
                                                                InputProps={{
                                                                    inputProps: {
                                                                        min: 1, 
                                                                        max: selectedProduct?.material_count || 1,
                                                                        style: { textAlign: 'center', fontSize: '1.1rem', fontWeight: 'bold' }
                                                                    },
                                                                    endAdornment: (
                                                                        <Typography variant="body2" color="text.secondary">
                                                                            个
                                                                        </Typography>
                                                                    )
                                                                }}
                                                                fullWidth
                                                                size="small"
                                                                disabled={!selectedProduct}
                                                                error={!selectedProduct || promotionCount > (selectedProduct?.material_count || 0) || promotionCount < 1}
                                                                sx={{ 
                                                                    '& .MuiOutlinedInput-root': {
                                                                        borderRadius: 2,
                                                                        bgcolor: 'background.paper',
                                                                        '&:hover fieldset': {
                                                                            borderColor: 'primary.main'
                                                                        },
                                                                        '&.Mui-focused fieldset': {
                                                                            borderColor: 'primary.main'
                                                                        }
                                                                    }
                                                                }}
                                                            />
                                                        </Box>
                                                    </Paper>
                                                </Box>
                                            );
                                        })()}
                                    </Grid>

                                    {/* 结束日期设置 */}
                                    <Grid size={{ xs: 12, md: 4 }}>
                                        <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 'medium', color: 'text.primary' }}>
                                            推广结束日期
                                        </Typography>
                                        <Paper sx={{ 
                                            p: 2.5, 
                                            borderRadius: 2,
                                            border: '1px solid',
                                            borderColor: 'divider',
                                            bgcolor: 'background.paper',
                                            minHeight: 120,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center'
                                        }}>
                                            <Box sx={{ textAlign: 'center', mb: 1.5 }}>
                                                <Typography variant="caption" color="text.secondary">
                                                    选择推广结束时间
                                                </Typography>
                                            </Box>
                                            <DatePicker
                                                value={endDate}
                                                onChange={handleEndDateChange}
                                                minDate={dayjs()}
                                                format="YYYY年MM月DD日"
                                                slotProps={{
                                                    textField: {
                                                        required: true,
                                                        fullWidth: true,
                                                        size: 'small',
                                                        sx: {
                                                            '& .MuiOutlinedInput-root': {
                                                                borderRadius: 2,
                                                                '&:hover fieldset': {
                                                                    borderColor: 'primary.main'
                                                                },
                                                                '& input': {
                                                                    textAlign: 'center',
                                                                    fontWeight: 'medium'
                                                                }
                                                            }
                                                        }
                                                    }
                                                }}
                                            />
                                        </Paper>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Paper>
                    </Stack>

                    {/* 任务预览和创建区域 */}
                    <Box sx={{ borderTop: 1, borderColor: 'divider', mt: 4 }}>
                        {/* 任务预览 */}
                        {canSubmit() && (
                            <Box sx={{ 
                                p: 4, 
                                bgcolor: 'background.paper'
                            }}>
                                <Typography variant="h6" fontWeight="600" sx={{ mb: 3, color: 'text.primary' }}>
                                    任务配置预览
                                </Typography>
                                
                                {(() => {
                                    const selectedProduct = products.items.find(p => p.selected);
                                    
                                    return (
                                        <Box sx={{ 
                                            p: 3, 
                                            borderRadius: 2,
                                            bgcolor: 'grey.50',
                                            border: '1px solid',
                                            borderColor: 'divider'
                                        }}>
                                            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 3 }}>
                                                {/* 产品信息 */}
                                                <Box sx={{ 
                                                    flex: 1,
                                                    p: 2.5,
                                                    borderRadius: 2,
                                                    bgcolor: 'background.paper',
                                                    border: '1px solid',
                                                    borderColor: 'divider'
                                                }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                        <Avatar src={selectedProduct?.image} sx={{ width: 40, height: 40, mr: 2 }}>
                                                            <Package size={20} />
                                                        </Avatar>
                                                        <Box>
                                                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                                                推广产品
                                                            </Typography>
                                                            <Typography variant="subtitle1" fontWeight="600" sx={{ lineHeight: 1.2 }}>
                                                                {selectedProduct?.name}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    <Typography variant="body2" color="text.secondary">
                                                        可用素材: {selectedProduct?.material_count} 个
                                                    </Typography>
                                                </Box>
                                                
                                                {/* 推广平台 */}
                                                <Box sx={{ 
                                                    flex: 1,
                                                    p: 2.5,
                                                    borderRadius: 2,
                                                    bgcolor: 'background.paper',
                                                    border: '1px solid',
                                                    borderColor: 'divider'
                                                }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                        <Box sx={{ 
                                                            width: 40, 
                                                            height: 40, 
                                                            borderRadius: '50%',
                                                            bgcolor: '#FF2442',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            mr: 2
                                                        }}>
                                                            <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold' }}>
                                                                小
                                                            </Typography>
                                                        </Box>
                                                        <Box>
                                                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                                                推广平台
                                                            </Typography>
                                                            <Typography variant="subtitle1" fontWeight="600" sx={{ lineHeight: 1.2 }}>
                                                                {platform}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    <Typography variant="body2" color="text.secondary">
                                                        社交电商平台
                                                    </Typography>
                                                </Box>
                                            </Stack>
                                            
                                            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
                                                {/* 推广数量 */}
                                                <Box sx={{ 
                                                    flex: 1,
                                                    p: 2.5,
                                                    borderRadius: 2,
                                                    bgcolor: 'background.paper',
                                                    border: '1px solid',
                                                    borderColor: 'divider'
                                                }}>
                                                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', mb: 1 }}>
                                                        推广数量
                                                    </Typography>
                                                    <Typography variant="h5" fontWeight="600" color="primary.main" sx={{ mb: 1 }}>
                                                        {promotionCount}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        个素材将生成推广内容
                                                    </Typography>
                                                </Box>
                                                
                                                {/* 推广时间 */}
                                                <Box sx={{ 
                                                    flex: 1,
                                                    p: 2.5,
                                                    borderRadius: 2,
                                                    bgcolor: 'background.paper',
                                                    border: '1px solid',
                                                    borderColor: 'divider'
                                                }}>
                                                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', mb: 1 }}>
                                                        推广时间
                                                    </Typography>
                                                    <Typography variant="h6" fontWeight="600" sx={{ mb: 1 }}>
                                                        至 {endDate?.format('MM月DD日')}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        {endDate ? `${endDate.diff(dayjs(), 'day')} 天后结束` : ''}
                                                    </Typography>
                                                </Box>
                                            </Stack>
                                            
                                            {/* 提示信息 */}
                                            <Box sx={{ 
                                                mt: 3, 
                                                p: 2.5, 
                                                borderRadius: 2,
                                                bgcolor: 'info.50',
                                                border: '1px solid',
                                                borderColor: 'info.200'
                                            }}>
                                                <Typography variant="body2" color="info.main" sx={{ fontWeight: '500', mb: 1 }}>
                                                    💡 任务说明
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary">
                                                    系统将根据产品素材自动生成推广内容，任务创建后核心配置无法修改
                                                </Typography>
                                            </Box>
                                        </Box>
                                    );
                                })()}
                            </Box>
                        )}
                        
                        {/* 任务创建区域 */}
                        <Box sx={{ 
                            p: 4, 
                            textAlign: 'center',
                            bgcolor: 'background.paper'
                        }}>
                            {canSubmit() ? (
                                <Box>
                                    <Box sx={{ 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        justifyContent: 'center', 
                                        mb: 3
                                    }}>
                                        <Box sx={{ 
                                            width: 24, 
                                            height: 24, 
                                            borderRadius: '50%',
                                            bgcolor: 'success.main',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mr: 1.5
                                        }}>
                                            <Typography variant="caption" sx={{ color: 'white', fontWeight: 'bold' }}>
                                                ✓
                                            </Typography>
                                        </Box>
                                        <Typography variant="subtitle1" color="success.main" sx={{ fontWeight: '600' }}>
                                            配置完成，可以创建任务
                                        </Typography>
                                    </Box>
                                    
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 4, maxWidth: 400, mx: 'auto' }}>
                                        所有必要信息已填写完成，系统将根据配置生成推广任务并开始执行。
                                    </Typography>
                                    
                                    <Button
                                        variant="contained"
                                        onClick={submitTask}
                                        disabled={isSubmitting}
                                        size="large"
                                        startIcon={<Send size={18} />}
                                        sx={{ 
                                            minWidth: 200,
                                            height: 48,
                                            borderRadius: 3,
                                            textTransform: 'none',
                                            fontWeight: 600,
                                            fontSize: '1rem',
                                            px: 4
                                        }}
                                    >
                                        {isSubmitting ? "正在创建..." : "创建推广任务"}
                                    </Button>
                                    
                                    {isSubmitting && (
                                        <Box sx={{ mt: 3 }}>
                                            <Typography variant="body2" color="text.secondary" sx={{ 
                                                display: 'flex', 
                                                alignItems: 'center', 
                                                justifyContent: 'center',
                                                gap: 1
                                            }}>
                                                <Box sx={{ 
                                                    width: 14, 
                                                    height: 14, 
                                                    border: '2px solid',
                                                    borderColor: 'primary.main',
                                                    borderTopColor: 'transparent',
                                                    borderRadius: '50%',
                                                    animation: 'spin 1s linear infinite'
                                                }} />
                                                正在处理，请稍候...
                                            </Typography>
                                        </Box>
                                    )}
                                </Box>
                            ) : (
                                <Box>
                                    <Box sx={{ 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        justifyContent: 'center', 
                                        mb: 3
                                    }}>
                                        <Box sx={{ 
                                            width: 32, 
                                            height: 32, 
                                            borderRadius: '50%',
                                            bgcolor: 'grey.400',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mr: 2
                                        }}>
                                            <Typography variant="subtitle2" sx={{ color: 'white', fontWeight: 'bold' }}>
                                                !
                                            </Typography>
                                        </Box>
                                        <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                            请完成所有配置步骤
                                        </Typography>
                                    </Box>
                                    
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 4, maxWidth: 400, mx: 'auto' }}>
                                        需要选择产品并设置推广详情后才能创建任务。请按照页面顶部的步骤指引完成配置。
                                    </Typography>
                                    
                                    <Button
                                        variant="contained"
                                        disabled
                                        size="large"
                                        sx={{ 
                                            minWidth: 220,
                                            height: 56,
                                            borderRadius: 4,
                                            textTransform: 'none',
                                            fontWeight: 700,
                                            fontSize: '1.1rem'
                                        }}
                                    >
                                        创建推广任务
                                    </Button>
                                </Box>
                            )}
                        </Box>
                    </Box>
                </Paper>
            </Container>

            {/* 产品选择抽屉 */}
            <Drawer
                anchor={isMobile ? 'bottom' : 'right'}
                open={drawerOpen}
                onClose={() => setDrawerOpen(false)}
                PaperProps={{
                    sx: {
                        height: isMobile ? '90vh' : '100vh',
                        borderTopLeftRadius: isMobile ? 16 : 0,
                        borderTopRightRadius: isMobile ? 16 : 0,
                        bgcolor: 'background.paper'
                    }
                }}
            >
                <ProductSelectionDrawer
                    open={drawerOpen}
                    onClose={() => setDrawerOpen(false)}
                    isMobile={isMobile}
                    data={products}
                    onToggle={handleProductToggle}
                    theme={theme}
                    router={router}
                />
            </Drawer>
        </LocalizationProvider>
    );
} 