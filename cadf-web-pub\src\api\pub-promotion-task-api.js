import api from "@/core/api/api";

const RESOURCE = "pub_promotion_task";

export const pubPromotionTaskApi = {
    /**
     * 查询任务市场中的可接取任务
     * @param {string} account_id - 账户ID
     * @param {number} page - 页码
     * @param {number} page_size - 每页数量
     * @returns {Promise<Object>} 包含任务列表的分页响应
     */
    queryMarketTasks: async (account_id, page, page_size) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_market_tasks",
            data: {
                account_id,
                page,
                page_size,
            },
        });
    },

    /**
     * 接取指定的推广任务
     * @param {string} promotion_task_detail_id - 推广任务详情ID
     * @param {string} account_id - 账户ID
     * @returns {Promise<Object>} 接取结果
     */
    acceptTask: async (promotion_task_detail_id, account_id) => {
        return await api({
            resource: RESOURCE,
            method_name: "accept_task",
            data: {
                promotion_task_detail_id,
                account_id,
            },
        });
    },

    /**
     * 查询用户已接取的任务列表
     * @param {number} page - 页码
     * @param {number} page_size - 每页数量
     * @param {string} account_id - 账户ID
     * @returns {Promise<Object>} 包含已接取任务列表的分页响应
     */
    queryAcceptedTasks: async (page, page_size, account_id) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_accepted_tasks",
            data: {
                page,
                page_size,
                account_id,
            },
        });
    },

    /**
     * 查询账户列表及其任务状态
     * @param {string} platform_filter - 平台过滤条件
     * @returns {Promise<Object>} 包含账户及任务状态的列表
     */
    queryAccountsWithTaskStatus: async (platform_filter) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_accounts_with_task_status",
            data: {
                platform: platform_filter,
            },
        });
    },

    /**
     * 放弃已接取的任务
     * @param {string} promotion_task_detail_id - 推广任务详情ID
     * @returns {Promise<Object>} 放弃任务的结果
     */
    giveUpTask: async (promotion_task_detail_id) => {
        return await api({
            resource: RESOURCE,
            method_name: "give_up_task",
            data: {
                promotion_task_detail_id,
            },
        });
    },

    /**
     * 更新任务的发布链接
     * @param {string} promotion_task_detail_id - 推广任务详情ID
     * @param {string} publish_url - 发布链接URL
     * @returns {Promise<Object>} 更新结果
     */
    updatePublishLink: async (promotion_task_detail_id, publish_url) => {
        return await api({
            resource: RESOURCE,
            method_name: "modify_publish_link",
            data: {
                promotion_task_detail_id,
                publish_link: publish_url,
            },
        });
    },

    /**
     * 获取推广任务详情，用于查看任务详细信息
     * @param {string} promotion_task_detail_id - 推广任务详情ID
     * @returns {Promise<Object>} 包含任务详情的响应对象
     */
    getTaskDetailForView: async (promotion_task_detail_id) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_promotion_task_detail",
            data: {
                promotion_task_detail_id,
            },
        });
    },
};