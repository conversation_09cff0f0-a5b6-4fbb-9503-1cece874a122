from typing import Any, Dict, List

from models.models import Captain<PERSON><PERSON><PERSON><PERSON><PERSON>nue
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler


@register_handler("pub_captain_daily_revenue")
class PubCaptainDailyRevenueApi:
    @auth_required(["captain", "admin"])
    async def query_all(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        captain_user_id = data.get("user_id")

        revenue_records = await CaptainDailyRevenue.find(
            CaptainDailyRevenue.captain_user_id == captain_user_id
        ).sort(-CaptainDailyRevenue.date).to_list()
        return [record.to_dict() for record in revenue_records]
