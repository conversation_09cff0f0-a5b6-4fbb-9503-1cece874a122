"use client";

import React, {useEffect, useState} from 'react';
import {Alert, Avatar, Box, Button, Card, CardContent, CardHeader, Chip, CircularProgress, Divider, Grid, Stack, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {Bookmark, Eye, MessageCircle, Plus, RefreshCw, Share2, ThumbsUp} from 'lucide-react';
import {advProductTrafficMetricsApi} from '@/api/adv-product-traffic-metrics-api';
import Link from 'next/link';
import {useSnackbar} from 'notistack';
import InfiniteScrollList from '@/core/components/InfiniteScrollList';


// 统计卡片组件 - 符合苹果HIG规范的极简设计
const StatCard = ({icon, title, value, color = 'primary'}) => {
    const theme = useTheme();
    return (
        <Card sx={{
            height: '100%', 
            width: '100%', 
            borderRadius: 2, // 苹果标准圆角
            border: 'none', // 去掉边框，更简洁
            boxShadow: 'none', // 初始无阴影
            bgcolor: theme.palette.background.paper,
            transition: 'all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1)', // 苹果标准缓动
            '&:hover': {
                boxShadow: '0 4px 12px 0 rgba(0, 0, 0, 0.05)', // 轻微阴影
                transform: 'translateY(-1px)', // 微小上移
            },
            overflow: 'hidden'
        }}>
            <CardContent sx={{p: 3, pb: '24px !important'}}>
                {/* 图标区域 - 内容优先原则 */}
                <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1}}>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                        fontWeight: 400,
                        fontSize: '0.875rem',
                        lineHeight: 1.43
                    }}>
                        {title}
                    </Typography>
                    {icon && (
                        <Box sx={{
                            color: theme.palette[color]?.main || theme.palette.primary.main,
                            display: 'flex',
                            alignItems: 'center',
                            opacity: 0.8
                        }}>
                            {icon}
                        </Box>
                    )}
                </Box>
                {/* 数值区域 - 清晰性原则 */}
                <Box sx={{display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between'}}>
                    <Typography variant="h3" sx={{
                        fontWeight: 600, // 苹果标准字重
                        color: theme.palette.text.primary,
                        fontSize: '2rem',
                        lineHeight: 1.2,
                        mb: 0.5
                    }}>
                        {value !== null && value !== undefined ? value.toLocaleString() : '—'}
                    </Typography>
                </Box>
            </CardContent>
        </Card>
    );
};

// 平台信息配置
const PLATFORMS = [
    {id: 1, name: '小红书', color: '#FF2442'},
    {id: 2, name: '抖音', color: '#000000'},
    {id: 3, name: '微博', color: '#E6162D'},
    {id: 4, name: '哔哩哔哩', color: '#00A1D6'},
    {id: 5, name: '快手', color: '#FF4A00'},
    {id: 6, name: '知乎', color: '#0084FF'},
];

// 数据指标卡片组件
const DataMetricCard = ({icon, title, value, color}) => {
    const theme = useTheme();
    return (
        <Box sx={{
            display: 'flex',
            alignItems: 'center',
            p: 1.5,
            borderRadius: 2,
            bgcolor: `${color}.50`,
            border: `1px solid ${theme.palette[color]?.light || theme.palette.grey[300]}`,
            transition: 'all 0.2s ease',
            '&:hover': {
                bgcolor: `${color}.100`,
                transform: 'translateY(-1px)',
            }
        }}>
            <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: `${color}.main`,
                color: 'white',
                mr: 1.5
            }}>
                {icon}
            </Box>
            <Box sx={{flexGrow: 1}}>
                <Typography variant="h6" sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: '1.1rem',
                    lineHeight: 1.2
                }}>
                    {value?.toLocaleString() || 0}
                </Typography>
                <Typography variant="caption" sx={{
                    color: 'text.secondary',
                    fontSize: '0.75rem'
                }}>
                    {title}
                </Typography>
            </Box>
        </Box>
    );
};

// 产品卡片渲染函数
const renderProductCard = (productData, theme) => {
    
    const getPlatformChip = (platformName) => {
        const platformInfo = PLATFORMS.find(p => p.name === platformName);
        return platformInfo ? (
            <Chip
                key={platformName}
                label={platformName}
                size="small"
                sx={{
                    bgcolor: platformInfo.color,
                    color: 'white',
                    height: 24,
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    '& .MuiChip-label': {px: 1}
                }}
            />
        ) : null;
    };

    // 获取产品图片
    const getProductImage = () => {
        if (productData.images && productData.images.length > 0) {
            const validImage = productData.images.find(img => img.signed_url);
            return validImage ? validImage.signed_url : null;
        }
        return null;
    };

    // 计算总互动数
    const totalEngagement = (productData.total_like_count || 0) + 
                            (productData.total_comment_count || 0) + 
                            (productData.total_share_count || 0) + 
                            (productData.total_favorite_count || 0);

    const productImage = getProductImage();

    return (
        <Card sx={{
            width: '100%',
            borderRadius: 3,
            border: `1px solid ${theme.palette.grey[200]}`,
            boxShadow: theme.shadows[1],
            transition: 'all 0.3s ease',
            '&:hover': {
                boxShadow: theme.shadows[4],
                transform: 'translateY(-2px)',
                borderColor: theme.palette.primary.light,
            }
        }}>
            {/* 产品头部信息 */}
            <CardHeader
                avatar={
                    <Avatar
                        src={productImage}
                        alt={productData.title || `产品 ${productData.product_id.slice(-6)}`}
                        sx={{
                            width: 48,
                            height: 48,
                            border: `2px solid ${theme.palette.grey[200]}`,
                            bgcolor: theme.palette.grey[100]
                        }}
                    >
                        {!productImage && (productData.title || `产品 ${productData.product_id.slice(-6)}`).charAt(0)}
                    </Avatar>
                }
                title={
                    <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                        <Typography variant="h6" sx={{
                            fontWeight: 600,
                            color: 'text.primary',
                            fontSize: '1.1rem'
                        }}>
                            {productData.title || `产品 ${productData.product_id.slice(-6)}`}
                        </Typography>
                        <Box sx={{display: 'flex', gap: 0.5}}>
                            {productData.platform && productData.platform.trim() !== ''
                                ? getPlatformChip(productData.platform)
                                : <Chip label="暂无平台" size="small" sx={{height: 24, fontSize: '0.75rem'}}/>
                            }
                        </Box>
                    </Box>
                }
                sx={{pb: 1}}
            />

            {/* 核心数据指标 */}
            <CardContent sx={{pt: 0}}>
                <Box sx={{mb: 2}}>
                    <Typography variant="body2" color="text.secondary" sx={{mb: 1.5}}>
                        核心数据指标
                    </Typography>
                    <Grid container spacing={1.5}>
                        <Grid size={6}>
                            <DataMetricCard
                                icon={<Eye size={16}/>}
                                title="阅读量"
                                value={productData.total_view_count}
                                color="primary"
                            />
                        </Grid>
                        <Grid size={6}>
                            <DataMetricCard
                                icon={<ThumbsUp size={16}/>}
                                title="点赞量"
                                value={productData.total_like_count}
                                color="success"
                            />
                        </Grid>
                        <Grid size={6}>
                            <DataMetricCard
                                icon={<MessageCircle size={16}/>}
                                title="评论量"
                                value={productData.total_comment_count}
                                color="info"
                            />
                        </Grid>
                        <Grid size={6}>
                            <DataMetricCard
                                icon={<Share2 size={16}/>}
                                title="转发量"
                                value={productData.total_share_count}
                                color="warning"
                            />
                        </Grid>
                    </Grid>
                </Box>

                {/* 次要指标 */}
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    pt: 2,
                    borderTop: `1px solid ${theme.palette.grey[200]}`
                }}>
                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                        <Bookmark size={14} color={theme.palette.secondary.main}/>
                        <Typography variant="body2" color="text.secondary">
                            收藏: {productData.total_favorite_count?.toLocaleString() || 0}
                        </Typography>
                    </Box>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        px: 2,
                        py: 0.5,
                        borderRadius: 2,
                        bgcolor: theme.palette.grey[50]
                    }}>
                        <Typography variant="body2" color="text.secondary">
                            总互动:
                        </Typography>
                        <Typography variant="body2" sx={{
                            fontWeight: 600,
                            color: 'primary.main'
                        }}>
                            {totalEngagement.toLocaleString()}
                        </Typography>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );
};

export default function DataTrafficCenter() {
    const {enqueueSnackbar} = useSnackbar();
    const theme = useTheme();
    const [productTraffics, setProductTraffics] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [totalProducts, setTotalProducts] = useState(0);
    const [totalStats, setTotalStats] = useState(null);
    const [isLoadingStats, setIsLoadingStats] = useState(true);
    const [statsError, setStatsError] = useState(null);
    const [hasMore, setHasMore] = useState(true);

    // 每次加载的数量
    const productsPerPage = 4;

    const fetchListData = async (reset = true) => {
        if (reset) {
            setProductTraffics([]);
            setIsLoading(true);
            setError(null);
        }
        
        const currentPage = reset ? 1 : Math.floor(productTraffics.length / productsPerPage) + 1;
        
        try {
            const response = await advProductTrafficMetricsApi.queryAll({
                page: currentPage, 
                page_size: productsPerPage
            });

            if (response && Array.isArray(response.results)) {
                setProductTraffics(prev => reset ? response.results : [...prev, ...response.results]);
                // 使用返回的total字段
                setTotalProducts(response.total || 0);
                
                // 判断是否还有更多数据 - 通过当前页面数据量和总数判断
                const currentLoadedCount = reset ? response.results.length : productTraffics.length + response.results.length;
                setHasMore(currentLoadedCount < response.total);
            } else {
                if (reset) {
                    setProductTraffics([]);
                    setTotalProducts(0);
                }
                enqueueSnackbar('获取产品数据格式错误', {variant: 'warning'});
            }
        } catch (err) {
            console.error("Error fetching product traffic:", err);
            setError("加载产品流量数据失败，请稍后重试。");
            if (reset) {
                setProductTraffics([]);
                setTotalProducts(0);
            }
            enqueueSnackbar('加载数据失败', {variant: 'error'});
        } finally {
            setIsLoading(false);
        }
    };

    const loadMore = async () => {
        if (!hasMore) return;
        await fetchListData(false);
    };

    useEffect(() => {
        fetchListData();
    }, []);

    useEffect(() => {
        const fetchTotalStats = async () => {
            setIsLoadingStats(true);
            setStatsError(null);
            try {
                const statsResponse = await advProductTrafficMetricsApi.getTotalStats();
                if (statsResponse) {
                    setTotalStats(statsResponse);
                } else {
                    enqueueSnackbar('获取总览数据格式错误', {variant: 'warning'});
                    setTotalStats({
                        total_view_count: 0,
                        total_like_count: 0,
                        total_comment_count: 0,
                        total_favorite_count: 0,
                        total_share_count: 0
                    });
                }
            } catch (err) {
                console.error("Error fetching total stats:", err);
                setStatsError("加载总览数据失败，请稍后重试。");
                enqueueSnackbar('加载总览数据失败', {variant: 'error'});
                setTotalStats({
                    total_view_count: 0,
                    total_like_count: 0,
                    total_comment_count: 0,
                    total_favorite_count: 0,
                    total_share_count: 0
                });
            } finally {
                setIsLoadingStats(false);
            }
        };

        fetchTotalStats();
    }, []);

    // 渲染统计数据卡片
    const renderTotalStats = () => {
        const isLoading = isLoadingStats;
        const error = statsError;
        const stats = totalStats;

        if (isLoading || error || !stats) {
            const defaultValue = isLoading ? '加载中...' : (error ? '错误' : 'N/A');
            const loadingColor = isLoading ? 'grey' : (error ? 'error' : 'primary');

            const displayValue = (isLoading || error) ? defaultValue : 'N/A';

            return (
                <Grid container spacing={2} sx={{ width: '100%' }}>
                    <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                        <StatCard icon={<Eye size={18}/>} title="总阅读量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                        <StatCard icon={<ThumbsUp size={18}/>} title="总点赞量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                        <StatCard icon={<MessageCircle size={18}/>} title="总评论量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                        <StatCard icon={<Share2 size={18}/>} title="总转发量" value={displayValue} color={loadingColor}/>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 12, md: 2.4 }}>
                        <StatCard icon={<Bookmark size={18}/>} title="总收藏量" value={displayValue} color={loadingColor}/>
                    </Grid>
                </Grid>
            );
        }

        const totalViews = stats.total_view_count ?? 0;
        const totalLikes = stats.total_like_count ?? 0;
        const totalComments = stats.total_comment_count ?? 0;
        const totalShares = stats.total_share_count ?? 0;
        const totalFavorites = stats.total_favorite_count ?? 0;

        return (
            <Grid container spacing={2} sx={{ width: '100%' }}>
                <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                    <StatCard
                        icon={<Eye size={18}/>}
                        title="总阅读量"
                        value={totalViews}
                        color="primary"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                    <StatCard
                        icon={<ThumbsUp size={18}/>}
                        title="总点赞量"
                        value={totalLikes}
                        color="success"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                    <StatCard
                        icon={<MessageCircle size={18}/>}
                        title="总评论量"
                        value={totalComments}
                        color="secondary"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                    <StatCard
                        icon={<Share2 size={18}/>}
                        title="总转发量"
                        value={totalShares}
                        color="info"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 12, md: 2.4 }}>
                    <StatCard
                        icon={<Bookmark size={18}/>}
                        title="总收藏量"
                        value={totalFavorites}
                        color="warning"
                    />
                </Grid>
            </Grid>
        );
    };

    // 渲染新增产品卡片
    const renderAddProductCard = () => (
        <Link href="/protected/product-management/new" passHref style={{textDecoration: 'none'}}>
            <Card
                sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    width: '100%',
                    mx: 'auto',
                    borderStyle: 'dashed',
                    borderWidth: 2,
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.50',
                    transition: 'all 0.3s',
                    '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: 3,
                        borderColor: 'primary.dark'
                    },
                    cursor: 'pointer'
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        p: 2,
                        width: '100%'
                    }}
                >
                    <Box
                        sx={{
                            width: 60,
                            height: 60,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            mr: 2
                        }}
                    >
                        <Plus size={30} color="#fff"/>
                    </Box>
                    <Box>
                        <Typography variant="h6" color="primary.main" sx={{fontWeight: 'medium'}}>
                            新增产品
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{mt: 0.5}}>
                            添加产品开始监测流量数据
                        </Typography>
                    </Box>
                </Box>
            </Card>
        </Link>
    );

    return (
        <Box sx={{
            width: '100%',
            px: {xs: 2, sm: 3},
            maxWidth: '100%',
            bgcolor: 'background.default', // 确保背景色符合主题
            minHeight: '100vh'
        }}>
            <Box sx={{py: 4, width: '100%'}}>
                {/* 页面标题 - 符合苹果HIG的清晰层级 */}
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4}}>
                    <Typography variant="h4" sx={{
                        fontWeight: 700, // 苹果标准标题字重
                        color: 'text.primary',
                        fontSize: '2.125rem',
                        lineHeight: 1.2
                    }}>
                        产品数据总览
                    </Typography>
                </Box>

                {/* 统计数据卡片 */}
                {renderTotalStats()}

                {/* 产品详情标题 - 苹果风格的简洁设计 */}
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, mt: 5}}>
                    <Typography variant="h5" sx={{
                        fontWeight: 600,
                        color: 'text.primary',
                        fontSize: '1.5rem',
                        lineHeight: 1.3
                    }}>
                        产品流量详情 ({totalProducts})
                    </Typography>
                    <Button
                        variant="text" // 改为text样式，更符合苹果风格
                        startIcon={<RefreshCw size={16}/>}
                        onClick={() => fetchListData(true)}
                        disabled={isLoading}
                        sx={{
                            borderRadius: 2,
                            textTransform: 'none', // 取消大写转换
                            fontWeight: 500,
                            px: 2,
                            py: 1,
                            color: 'primary.main',
                            '&:hover': {
                                bgcolor: 'primary.50' // 轻微的背景色变化
                            }
                        }}
                    >
                        刷新
                    </Button>
                </Box>

                {/* 加载中状态 - 苹果风格 */}
                {isLoading && productTraffics.length === 0 && (
                    <Box sx={{
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center', 
                        minHeight: 200,
                        borderRadius: 2,
                        bgcolor: 'background.paper'
                    }}>
                        <CircularProgress size={24} thickness={4} sx={{color: 'primary.main'}}/>
                    </Box>
                )}

                {/* 错误状态 - 简洁设计 */}
                {error && productTraffics.length === 0 && (
                    <Alert 
                        severity="error" 
                        sx={{
                            mb: 2,
                            borderRadius: 2,
                            border: 'none',
                            boxShadow: 'none',
                            bgcolor: 'error.50',
                            color: 'error.dark'
                        }}
                    >
                        {error}
                    </Alert>
                )}

                {/* 空状态 - 苹果风格的优雅空状态 */}
                {!isLoading && !error && totalProducts === 0 && (
                    <Box sx={{
                        textAlign: 'center', 
                        mt: 8, 
                        mb: 6,
                        py: 6,
                        borderRadius: 2,
                        bgcolor: 'background.paper'
                    }}>
                        <Typography variant="h6" sx={{
                            color: 'text.secondary', 
                            mb: 1,
                            fontWeight: 500
                        }}>
                            暂无产品数据
                        </Typography>
                        <Typography variant="body2" sx={{
                            color: 'text.secondary',
                            opacity: 0.8
                        }}>
                            添加产品后，数据将在此处显示
                        </Typography>
                    </Box>
                )}

                {/* 产品列表 */}
                {totalProducts > 0 && (
                    <Box sx={{ width: '100%' }}>
                        <Box sx={{ width: '100%', mb: 2 }}>
                            {renderAddProductCard()}
                        </Box>
                        
                        <InfiniteScrollList
                            items={productTraffics}
                            renderItem={(traffic) => renderProductCard(traffic, theme)}
                            loadMore={loadMore}
                            hasMore={hasMore}
                            gridColumns={{
                                xs: 12,  // 手机上一排1个
                                sm: 12,  // 小屏幕上一排1个
                                md: 6,   // 中屏幕上一排2个
                                lg: 6,   // 大屏幕上一排2个
                                xl: 6    // 超大屏幕上一排2个
                            }}
                        />
                    </Box>
                )}
            </Box>
        </Box>
    );
}