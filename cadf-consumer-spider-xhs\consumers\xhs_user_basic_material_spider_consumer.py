import random
import time
from io import BytesIO

import aiohttp
from PIL import Image

from common_config.common_config import RedisKeyConfig, OSS_PIC_DIR
from models.models import UserBasicMaterial, ImageInfo, Account
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from spider.tools.xhs_tools import extract_url_from_text
from spider.xhs.xhs_note_content_spider_from_url import crawl_note_from_url


def convert_image_to_png_bytes(image_bytes: bytes) -> bytes:
    """将图片转换为PNG格式的bytes数据。"""
    img = Image.open(BytesIO(image_bytes))

    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
        img = img.convert('RGBA')
    else:
        img = img.convert('RGB')

    png_buffer = BytesIO()
    img.save(png_buffer, format='PNG')

    olog.debug(f"图片转换为PNG格式完成，大小: {len(png_buffer.getvalue())} bytes")
    return png_buffer.getvalue()


@consume_redis_set(redis_key=RedisKeyConfig.XHS_NOTE_CONTENT_SPIDER_SET, num_tasks=1)
async def handle_task(message_data: dict) -> None:
    """处理小红书用户素材笔记爬取任务。"""
    material_id = message_data["id_"]
    olog.info(f"开始处理 UserBasicMaterial ID: {material_id}")

    try:
        material = await UserBasicMaterial.get(material_id)
        material.fetch_status = "爬取中"
        await material.save()

        # 爬取数据
        online_accounts = await Account.find(Account.platform == "小红书", Account.status == "在线").to_list()
        used_account = random.choice(online_accounts)
        cookies_list = used_account.cookie
        url = extract_url_from_text(material.share_url, "小红书")

        olog.debug(f"开始爬取笔记内容")
        scraped_data, is_logged_in = await crawl_note_from_url(url, cookies_list)
        olog.info(f"笔记爬取完成，标题: {scraped_data.title}")

        # 更新账号登录状态
        current_time = int(time.time())
        new_status = "在线" if is_logged_in else "离线"

        used_account.status = new_status
        used_account.last_login_check_at = current_time
        await used_account.save()
        olog.debug(f"账号 {used_account.name} 状态更新为: {new_status}")

        # 处理图片
        formatted_images = []
        async with aiohttp.ClientSession() as session:
            for i, img_url in enumerate(scraped_data.images):
                async with session.get(img_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    response.raise_for_status()

                    png_bytes = convert_image_to_png_bytes(await response.read())
                    oss_key = await oss_client.upload_bytes(OSS_PIC_DIR, ".png", png_bytes)
                    formatted_images.append(ImageInfo(oss_key=oss_key, order=i))
                    olog.debug(f"图片 {i + 1}/{len(scraped_data.images)} 处理完成")

        # 更新素材数据
        material.fetch_status = "已完成"
        material.title = scraped_data.title
        material.content = scraped_data.content
        material.images = formatted_images
        await material.save()
        olog.info(f"用户素材处理完成 ID: {material_id}")

    except Exception as e:
        olog.exception(f"处理用户素材任务失败 ID: {material_id}")
        try:
            # 尝试标记状态为失败
            material = await UserBasicMaterial.get(material_id)
            material.fetch_status = "失败"
            await material.save()
            olog.info(f"已标记素材状态为失败 ID: {material_id}")
        except Exception as save_error:
            olog.exception(f"标记素材状态失败时出错 ID: {material_id}")
        raise
