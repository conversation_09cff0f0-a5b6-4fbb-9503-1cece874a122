"use client";

import {reduxStore} from "@/components/redux-store";
import {PROJECT_NAME} from "@/config";
import Alert from "@/core/components/alert";
import DefaultSkeleton from "@/core/components/skeleton";
import {Container} from "@mui/material";
import {AppRouterCacheProvider} from "@mui/material-nextjs/v13-appRouter";
import {ThemeProvider} from '@mui/material/styles';
import theme from '@/theme';
import Head from "next/head";
import {Suspense} from "react";
import {Provider} from "react-redux";
import "./globals.css";

export default function RootLayout({children}) {
    return (
        <html lang="en">
        <Head>
            <title>{PROJECT_NAME}</title>
        </Head>
        <body>
        <div>
            <AppRouterCacheProvider>
                <Suspense fallback={<DefaultSkeleton/>}>
                    <Provider store={reduxStore}>
                        <ThemeProvider theme={theme}>
                            <Alert/>
                            <Container
                                maxWidth={false}
                                disableGutters
                                sx={{
                                    bgcolor: "background.default",
                                    display: "flex",
                                    flexDirection: "column",
                                }}
                            >
                                {children}
                            </Container>
                        </ThemeProvider>
                    </Provider>
                </Suspense>
            </AppRouterCacheProvider>
        </div>
        </body>
        </html>
    );
}
