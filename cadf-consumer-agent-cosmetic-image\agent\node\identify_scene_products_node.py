from typing import Dict, Any
from pydantic import BaseModel, Field

from agent.state import OverallState
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog


class ImageCategoryResult(BaseModel):
    product_type: str = Field(
        None, description="具体的美妆产品类型，如洗发水、护发素等"
    )
    description: str = Field(..., description="图片主要内容描述")


async def identify_scene_products(state: OverallState) -> Dict[str, Any]:
    """识别场景图中的产品节点"""
    olog.info("开始执行场景图产品识别节点")
    
    image_path = state.scene_image_path
    olog.debug(f"处理图片路径: {image_path}")
    
    prompt_template = """
# 角色
你是一位专注于电商图片分析的AI视觉专家，擅长识别美妆产品及其细分类别。

# 任务
请分析图片内容，完成以下任务：
1. 识别图片中的美妆产品细分类别（如洗发水、护发素、沐浴露等）。
2. 用简洁的中文描述图片的主要内容。

# 返回值要求
- product_type: 具体的美妆产品类型（如洗发水、护发素、沐浴露等）。
- description: 用一句话描述图片的主要内容，确保语言简洁明了。
"""
    
    result = await structured_output_handler(
        prompt_template=prompt_template,
        params={},
        output_model=ImageCategoryResult,
        llm_name="QWEN_VL_MAX",
        image_path=image_path
    )
    
    scene_product_info = result.model_dump()
    olog.info(f"场景图产品识别结果: {scene_product_info}")
    
    return {
        "scene_product_info": scene_product_info
    }