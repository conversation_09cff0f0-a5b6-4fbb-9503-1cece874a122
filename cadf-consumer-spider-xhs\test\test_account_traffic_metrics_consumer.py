"""
小红书账户流量指标爬虫消费者测试例子
"""
import asyncio
from typing import Dict, Any

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from consumers.xhs_account_traffic_metrics_spider_consumer import handle_task


async def test_account_traffic_metrics_consumer():
    """
    测试小红书账户流量指标爬虫消费者
    """
    try:
        await init_models()
        
        # 模拟消息数据
        message_data: Dict[str, Any] = {
            "id_": "68724c33435ce73710ab0bc8"  # 替换为实际的账户ID
        }
        
        olog.info(f"开始测试消费者处理任务: {message_data}")
        
        # 调用消费者处理函数
        await handle_task(message_data)
        
        olog.info("消费者任务处理完成")
            
    except Exception as e:
        olog.exception(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_account_traffic_metrics_consumer())