"use client";

import {useEffect, useRef, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {Alert, Box, Button, Chip, CircularProgress, IconButton, InputAdornment, LinearProgress, Paper, TextField, Typography, useTheme} from '@mui/material';
import {ArrowLeft, ArrowRight, Edit2, Plus, Save, Trash2, WandSparkles, X} from 'lucide-react';
import {advProductApi} from '@/api/adv-product-api';
import {uploadImage} from '@/components/ImageUtils';
import {advAiAgentApi} from "@/api/adv-ai-agent-api";
import {useSnackbar} from 'notistack';

const createDefaultProduct = (id = null) => ({
    id_: id,
    name: '',
    images: [],
    description: '',
    domain: [],
});

export default function ProductDetailPage() {
    const theme = useTheme();
    const router = useRouter();
    const params = useParams();
    const [isNew, setIsNew] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const {enqueueSnackbar} = useSnackbar();

    const [product, setProduct] = useState(createDefaultProduct());
    const [saveSuccess, setSaveSuccess] = useState(false);

    const [newDomain, setNewDomain] = useState('');
    const [uploadProgress, setUploadProgress] = useState({});
    const [uploadErrors, setUploadErrors] = useState({});
    const [currentImageUrl, setCurrentImageUrl] = useState('');
    const fileInputRef = useRef(null);

    const [isGeneratingDesc, setIsGeneratingDesc] = useState(false);
    const [isGeneratingDomains, setIsGeneratingDomains] = useState(false);
    const [isIdentifyingName, setIsIdentifyingName] = useState(false);

    const images = Array.isArray(product?.images) ? product.images : [];

    const [saveLoading, setSaveLoading] = useState(false);

    const isUploading = Object.values(uploadProgress).some(p => p < 100);

    useEffect(() => {
        if (images.length > 0) {
            const currentImage = images[0];
            if (currentImage.url) {
                setCurrentImageUrl(currentImage.url);
            } else if (currentImage.key) {
                console.warn(`Image with key ${currentImage.key} has no URL provided.`);
                setCurrentImageUrl('https://placehold.co/400x600/eeeeee/999999?text=URL+Missing');
            } else {
                setCurrentImageUrl('https://placehold.co/400x600/eeeeee/999999?text=No+Image+Source');
            }
        } else {
            setCurrentImageUrl('');
        }
    }, [images]);

    useEffect(() => {
        const {id} = params;
        setLoading(true);

        const fetchProductData = async (productId) => {
            try {
                const data = await advProductApi.query_one(productId);
                const processedProduct = {
                    ...data,
                    id_: data._id,
                    name: data.title || '',
                    description: data.description || '',
                    domain: data.domain || [],
                    images: (data.images || []).map((img, index) => ({
                        ...img,
                        id: Date.now() + index,
                        key: img.oss_key,
                        url: img.signed_url || img.url,
                    })).sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity)),
                };
                setProduct(processedProduct);
                setIsNew(false);
                setNewDomain('');
            } catch (err) {
                console.error("获取产品数据失败:", err);
                const errorMessage = `加载产品信息失败: ${err.message || '未知错误'}`;
                enqueueSnackbar(errorMessage, {type: 'error'});
                setError(errorMessage);
                setLoading(false);
            } finally {
                setLoading(false);
            }
        };

        if (id === 'new') {
            setProduct(createDefaultProduct());
            setIsNew(true);
            setLoading(false);
        } else {
            setIsNew(false);
            fetchProductData(id);
        }

    }, [params, enqueueSnackbar]);

    const handleSaveChanges = async () => {
        setSaveLoading(true);
        setSaveSuccess(false);

        if (!product.name || product.name.trim() === '') {
            enqueueSnackbar('产品名称不能为空', {variant: 'error'});
            setSaveLoading(false);
            return;
        }
        if (!product.description || product.description.trim() === '') {
            enqueueSnackbar('描述不能为空', {variant: 'error'});
            setSaveLoading(false);
            return;
        }
        if (!product.domain || product.domain.length === 0) {
            enqueueSnackbar('请至少添加一个领域', {variant: 'error'});
            setSaveLoading(false);
            return;
        }
        if (!images || images.length === 0) {
            enqueueSnackbar('请至少上传一张图片', {variant: 'error'});
            setSaveLoading(false);
            return;
        }

        const productDataToSave = {
            title: product.name,
            description: product.description,
            domain: product.domain,
            images: product.images.map((img, index) => ({
                oss_key: img.key,
                order: index
            }))
        };

        try {
            if (isNew) {
                await advProductApi.create(
                    productDataToSave.title,
                    productDataToSave.description,
                    productDataToSave.domain,
                    productDataToSave.images
                );
                enqueueSnackbar('创建成功', {variant: 'success'});
            } else {
                await advProductApi.modify(
                    product.id_,
                    productDataToSave.title,
                    productDataToSave.description,
                    productDataToSave.domain,
                    productDataToSave.images
                );
                enqueueSnackbar('修改成功', {variant: 'success'});
            }
            // 保存成功后跳转到引导页面
            router.push('/protected/product-management/guide');
        } catch (err) {
            console.error("保存产品失败:", err);
            const errorMessage = `保存失败: ${err.message || '未知错误'}`;
            enqueueSnackbar(errorMessage, {variant: 'error'});
        } finally {
            setSaveLoading(false);
        }
    };

    const handleDeleteProduct = async () => {
        if (!product || !product.id_) {
            enqueueSnackbar('产品信息不完整，无法删除', {variant: 'error'});
            return;
        }

        setSaveLoading(true);
        try {
            await advProductApi.delete(product.id_);
            enqueueSnackbar('产品已成功删除', {variant: 'success'});
            router.push('/protected/product-management');
        } catch (err) {
            console.error("删除产品失败:", err);
            const errorMessage = `删除失败: ${err.message || '未知错误'}`;
            enqueueSnackbar(errorMessage, {variant: 'error'});
        } finally {
            setSaveLoading(false);
        }
    };

    const handleBackToList = () => {
        router.push('/protected/product-management');
    };

    const handleDescriptionChange = (event) => {
        setProduct({
            ...product,
            description: event.target.value
        });
    };

    const handleNewDomainChange = (event) => {
        setNewDomain(event.target.value);
    };

    const handleAddDomain = () => {
        if (newDomain.trim() !== '' && !product.domain.includes(newDomain.trim())) {
            const updatedDomain = [...product.domain, newDomain.trim()];
            setProduct({
                ...product,
                domain: updatedDomain
            });
            setNewDomain('');
        }
    };

    const handleRemoveDomain = (domainToRemove) => {
        const updatedDomain = product.domain.filter(item => item !== domainToRemove);
        setProduct({
            ...product,
            domain: updatedDomain
        });
    };

    const handleDomainKeyDown = (event) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            handleAddDomain();
        }
    };

    const handleDeleteImage = async () => {
        if (images.length > 0) {
            setProduct({
                ...product,
                images: []
            });
            setCurrentImageUrl('');
        }
    };

    const handleOpenUploadDialog = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const handleFileSelect = async (event) => {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        setUploadProgress({});
        setUploadErrors({});

        // 验证文件格式
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));

        if (invalidFiles.length > 0) {
            const errorMessage = `不支持的文件格式: ${invalidFiles.map(f => f.name).join(', ')}。仅支持 JPG、PNG 和 WebP 格式`;
            enqueueSnackbar(errorMessage, {variant: 'error'});
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
            return;
        }

        const uploadPromises = files.map(file => {
            const onProgress = (percentage) => {
                setUploadProgress(prev => ({...prev, [file.name]: percentage}));
            };

            return uploadImage(file, onProgress)
                .then(({key, url}) => ({
                    key: key,
                    url: url,
                    name: file.name,
                    size: `${(file.size / (1024 * 1024)).toFixed(2)}MB`,
                }))
                .catch(error => {
                    console.error(`上传文件 ${file.name} 失败:`, error);
                    setUploadErrors(prev => ({...prev, [file.name]: error.message || '上传失败'}));
                    enqueueSnackbar(`文件 ${file.name} 上传失败: ${error.message || '未知错误'}`, {variant: 'error'});
                    return null;
                });
        });

        try {
            const results = await Promise.all(uploadPromises);
            const successfulUploads = results.filter(result => result !== null);

            if (successfulUploads.length > 0) {
                addSelectedImagesToProduct(successfulUploads);
            }

            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        } catch (error) {
            console.error("处理上传时发生意外错误:", error);
        }
    };

    const addSelectedImagesToProduct = (uploadedImages) => {
        if (!uploadedImages || uploadedImages.length === 0) return;

        const firstUploadedImage = uploadedImages[0];

        const newImageEntry = {
            id: Date.now() + Math.random(),
            key: firstUploadedImage.key,
            url: firstUploadedImage.url,
            name: firstUploadedImage.name,
            size: firstUploadedImage.size,
        };

        const updatedImages = [newImageEntry];

        setProduct(prevProduct => ({
            ...prevProduct,
            images: updatedImages
        }));
    };

    const getFirstImageUrl = async () => {
        if (!images || images.length === 0) {
            enqueueSnackbar('请先上传图片', {variant: 'warning'});
            return null;
        }

        const firstImage = images[0];
        if (!firstImage.url) {
            enqueueSnackbar('无法获取图片URL', {variant: 'error'});
            return null;
        }
        
        return firstImage.url;
    };

    const handleGenerateDescription = async () => {
        setIsGeneratingDesc(true);
        const imageUrl = await getFirstImageUrl();
        if (!imageUrl) {
            setIsGeneratingDesc(false);
            return;
        }

        try {
            const result = await advAiAgentApi.generateDescription(imageUrl);
            setProduct(prev => ({...prev, description: result.description || ''}));
            enqueueSnackbar('AI 已生成产品描述', {variant: 'success'});
        } catch (err) {
            console.error("AI 生成描述失败:", err);
            enqueueSnackbar(`AI 生成描述失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setIsGeneratingDesc(false);
        }
    };

    const handleIdentifyDomains = async () => {
        setIsGeneratingDomains(true);
        const imageUrl = await getFirstImageUrl();
        if (!imageUrl) {
            setIsGeneratingDomains(false);
            return;
        }

        try {
            const result = await advAiAgentApi.identifyAndSaveDomains(imageUrl);
            const identified = result.identified_domains || [];
            setProduct(prev => {
                const existingDomains = prev.domain || [];
                const combinedDomains = [...new Set([...existingDomains, ...identified])];
                return {...prev, domain: combinedDomains};
            });
            enqueueSnackbar(`AI 已识别领域 (${identified.join(', ')})`, {variant: 'success'});
        } catch (err) {
            console.error("AI 识别领域失败:", err);
            enqueueSnackbar(`AI 识别领域失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setIsGeneratingDomains(false);
        }
    };

    const handleIdentifyName = async () => {
        setIsIdentifyingName(true);
        const imageUrl = await getFirstImageUrl();
        if (!imageUrl) {
            setIsIdentifyingName(false);
            return;
        }

        try {
            const result = await advAiAgentApi.identifyProductName(imageUrl);
            setProduct(prev => ({...prev, name: result.product_name || ''}));
            enqueueSnackbar('AI 已识别产品名称', {variant: 'success'});
        } catch (err) {
            console.error("AI 识别产品名称失败:", err);
            enqueueSnackbar(`AI 识别名称失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setIsIdentifyingName(false);
        }
    };

    const handleGoToAIGeneration = () => {
        router.push('/protected/ai-image-text-generation/create');
    };

    if (loading) {
        return (
            <Box sx={{py: 3, display: 'flex', justifyContent: 'center'}}>
                <Typography>加载中...</Typography>
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{py: 3}}>
                <Alert severity="error">{error}</Alert>
                <Box sx={{mt: 2}}>
                    <Button
                        startIcon={<ArrowLeft size={18}/>}
                        onClick={handleBackToList}
                    >
                        返回列表
                    </Button>
                </Box>
            </Box>
        );
    }

    return (
        <Box sx={{py: 3}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <Button
                    startIcon={<ArrowLeft size={18}/>}
                    onClick={handleBackToList}
                    sx={{mr: 2}}
                >
                    返回列表
                </Button>
                <Typography variant="h4" component="h1" sx={{fontWeight: 600, flexGrow: 1}}>
                    {isNew ? '新建产品' : '产品详情'}
                </Typography>
                {!isNew && (
                    <Button
                        variant="outlined"
                        color="error"
                        startIcon={<Trash2 size={18}/>}
                        onClick={handleDeleteProduct}
                        disabled={saveLoading}
                        sx={{mr: 2}}
                    >
                        {saveLoading ? <CircularProgress size={24} color="inherit"/> : '删除产品'}
                    </Button>
                )}
                <Button
                    variant="contained"
                    startIcon={<Save size={18}/>}
                    onClick={handleSaveChanges}
                    disabled={saveLoading}
                >
                    {saveLoading ? <CircularProgress size={24} color="inherit"/> : '保存更改'}
                </Button>
            </Box>

            {saveSuccess && (
                <Paper
                    elevation={1}
                    sx={{
                        borderRadius: 2,
                        p: 3,
                        mb: 3,
                        bgcolor: theme.palette.success.light,
                        display: 'flex',
                        flexDirection: {xs: 'column', sm: 'row'},
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: 1,
                    }}
                >
                    <Box>
                        <Typography variant="h6" sx={{color: theme.palette.success.contrastText, fontWeight: 600}}>
                            产品信息已保存成功
                        </Typography>
                        <Typography variant="body2" sx={{color: theme.palette.success.contrastText}}>
                            现在您可以使用AI图文生成功能，为您的产品创建营销内容
                        </Typography>
                    </Box>
                    <Button
                        variant="contained"
                        color="primary"
                        endIcon={<ArrowRight size={18}/>}
                        onClick={handleGoToAIGeneration}
                        sx={{
                            whiteSpace: 'nowrap',
                            bgcolor: theme.palette.common.white,
                            color: theme.palette.primary.main,
                            '&:hover': {
                                bgcolor: theme.palette.grey[100],
                            }
                        }}
                    >
                        前往AI图文生成
                    </Button>
                </Paper>
            )}

            <Box sx={{
                display: 'flex',
                flexDirection: {xs: 'column', md: 'row'},
                gap: 3,
                width: '100%'
            }}>
                <Paper
                    elevation={1}
                    sx={{
                        borderRadius: 2,
                        overflow: 'hidden',
                        position: 'relative',
                        width: {xs: '100%', md: '45%'},
                        height: 'fit-content'
                    }}
                >
                    <Box sx={{position: 'relative', display: 'flex', justifyContent: 'center'}}>
                        {isUploading && (
                            <Box sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                zIndex: 2,
                            }}>
                                <CircularProgress/>
                                <Typography sx={{mt: 1}}>上传中...</Typography>
                            </Box>
                        )}
                        {currentImageUrl ? (
                            <Box
                                component="img"
                                src={currentImageUrl}
                                alt="产品图片"
                                sx={{
                                    width: '100%',
                                    maxHeight: 500,
                                    objectFit: 'contain',
                                    display: 'block'
                                }}
                            />
                        ) : images.length === 0 ? (
                            <Box
                                sx={{
                                    width: '100%',
                                    height: 400,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    bgcolor: theme.palette.grey[100],
                                    textAlign: 'center',
                                    color: theme.palette.text.secondary,
                                    p: 2
                                }}
                            >
                                <Typography variant="h6" gutterBottom>
                                    {images.length > 0 ? "无法加载图片" : "暂无图片"}
                                </Typography>
                                <Typography variant="body2" sx={{mt: 1}}>
                                    建议上传透明背景或白底图片，以获得最佳AI生成效果。
                                </Typography>
                            </Box>
                        ) : null}

                        <Box
                            sx={{
                                position: 'absolute',
                                top: 10,
                                right: 10,
                                display: 'flex',
                                gap: 1,
                                bgcolor: 'rgba(0, 0, 0, 0.5)',
                                borderRadius: 1,
                                p: 0.5
                            }}
                        >
                            <IconButton
                                size="small"
                                onClick={handleOpenUploadDialog}
                                sx={{color: 'white'}}
                            >
                                <Plus size={18}/>
                            </IconButton>
                            <input
                                type="file"
                                accept=".jpg,.jpeg,.png,.webp"
                                style={{display: 'none'}}
                                onChange={handleFileSelect}
                                ref={fileInputRef}
                            />
                            {images.length > 0 && (
                                <IconButton
                                    size="small"
                                    onClick={handleDeleteImage}
                                    sx={{color: 'white'}}
                                >
                                    <Trash2 size={18}/>
                                </IconButton>
                            )}
                        </Box>
                    </Box>
                </Paper>

                <Paper
                    elevation={1}
                    sx={{
                        borderRadius: 2,
                        p: 3,
                        width: {xs: '100%', md: '55%'},
                        height: 'fit-content'
                    }}
                >
                    <Box sx={{mb: 3}}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                            <Typography variant="subtitle1" sx={{fontWeight: 600}}>
                                产品名称
                            </Typography>
                            <Button
                                size="small"
                                startIcon={isIdentifyingName ? <CircularProgress size={16} color="inherit"/> : <WandSparkles size={16}/>}
                                onClick={handleIdentifyName}
                                disabled={isIdentifyingName || images.length === 0 || isUploading}
                                sx={{textTransform: 'none'}}
                            >
                                AI识别
                            </Button>
                        </Box>
                        <TextField
                            fullWidth
                            variant="outlined"
                            value={product.name}
                            onChange={(e) => setProduct({...product, name: e.target.value})}
                            placeholder="请输入产品名称"
                        />
                    </Box>

                    <Box sx={{mb: 3}}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                            <Typography variant="subtitle1" sx={{fontWeight: 600}}>
                                领域
                            </Typography>
                            <Button
                                size="small"
                                startIcon={isGeneratingDomains ? <CircularProgress size={16} color="inherit"/> : <WandSparkles size={16}/>}
                                onClick={handleIdentifyDomains}
                                disabled={isGeneratingDomains || images.length === 0 || isUploading}
                                sx={{textTransform: 'none'}}
                            >
                                AI识别
                            </Button>
                        </Box>
                        <Box sx={{mb: 2}}>
                            <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 0.8, mb: 1.5, alignItems: 'center'}}>
                                {product.domain && product.domain.map((item, index) => (
                                    <Chip
                                        key={index}
                                        label={item}
                                        size="small"
                                        onDelete={() => handleRemoveDomain(item)}
                                        deleteIcon={<X size={14}/>}
                                        sx={{
                                            bgcolor: theme.palette.grey[100],
                                            borderRadius: '16px',
                                            color: theme.palette.text.primary,
                                            '& .MuiChip-deleteIcon': {
                                                color: theme.palette.text.secondary,
                                            },
                                            '& .MuiChip-deleteIcon:hover': {
                                                color: theme.palette.error.main,
                                            }
                                        }}
                                    />
                                ))}
                                {product.domain.length === 0 && (
                                    <Typography variant="body2" color="text.secondary">
                                        暂无领域
                                    </Typography>
                                )}
                            </Box>
                            <TextField
                                fullWidth
                                size="small"
                                value={newDomain}
                                onChange={handleNewDomainChange}
                                onKeyDown={handleDomainKeyDown}
                                placeholder="添加领域，按回车确认"
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                edge="end"
                                                onClick={handleAddDomain}
                                                disabled={newDomain.trim() === ''}
                                            >
                                                <Edit2 size={16}/>
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: '8px',
                                    }
                                }}
                            />
                        </Box>
                    </Box>

                    <Box sx={{mb: 3}}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                            <Typography variant="subtitle1" sx={{fontWeight: 600}}>
                                描述
                            </Typography>
                            <Button
                                size="small"
                                startIcon={isGeneratingDesc ? <CircularProgress size={16} color="inherit"/> : <WandSparkles size={16}/>}
                                onClick={handleGenerateDescription}
                                disabled={isGeneratingDesc || images.length === 0 || isUploading}
                                sx={{textTransform: 'none'}}
                            >
                                AI生成
                            </Button>
                        </Box>
                        <TextField
                            fullWidth
                            multiline
                            rows={6}
                            variant="outlined"
                            value={product.description}
                            onChange={handleDescriptionChange}
                            placeholder="请输入产品描述"
                        />
                    </Box>
                </Paper>
            </Box>

            <Box sx={{mt: 2}}>
                {Object.entries(uploadProgress).map(([fileName, progress]) => (
                    progress < 100 && (
                        <Box key={fileName} sx={{mb: 1}}>
                            <Typography variant="body2">{`上传中: ${fileName} (${progress}%)`}</Typography>
                            <LinearProgress variant="determinate" value={progress}/>
                        </Box>
                    )
                ))}
                {Object.entries(uploadErrors).map(([fileName, error]) => (
                    <Alert severity="error" key={fileName} sx={{mb: 1}}>{`文件 ${fileName} 上传失败: ${error}`}</Alert>
                ))}
            </Box>
        </Box>
    );
} 