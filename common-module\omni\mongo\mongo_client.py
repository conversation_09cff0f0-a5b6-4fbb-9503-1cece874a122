"""
mongo:
  url: mongodb://aqua:<EMAIL>:27017/aqua
"""
import importlib
import inspect
from typing import List, Type

import motor.motor_asyncio
from beanie import init_beanie, Document

from omni.config.config_loader import config_dict
from omni.log.log import olog


async def get_mongo_database() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    """
    获取 MongoDB 数据库连接。
    
    Returns:
        database: MongoDB 数据库对象
    """
    mongo_url = config_dict.get('mongo', {}).get('url')
    client = motor.motor_asyncio.AsyncIOMotorClient(mongo_url)
    database = client.get_default_database()
    return database


def _discover_models() -> List[Type[Document]]:
    """
    从 'models.models' 模块动态发现所有 Beanie Document 模型。
    """
    document_models = []
    module_name = "models.models"
    try:
        module = importlib.import_module(module_name)
        for name, obj in inspect.getmembers(module):
            if inspect.isclass(obj) and issubclass(obj, Document) and obj is not Document:
                document_models.append(obj)

    except (ImportError, SyntaxError):
        olog.exception("无法从 models.models 导入模型")

    return document_models


async def init_models() -> None:
    """
    初始化所有的 Beanie Document 模型。
    """
    database = await get_mongo_database()
    document_models = _discover_models()
    
    # 打印每个注册的集合名称
    for model in document_models:
        collection_name = getattr(model.Settings, 'name', model.__name__)
        olog.info(f"注册MongoDB Document: {collection_name}")
    
    await init_beanie(database=database, document_models=document_models)
