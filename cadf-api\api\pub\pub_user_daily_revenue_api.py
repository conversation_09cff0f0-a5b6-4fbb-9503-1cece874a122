import time
from datetime import datetime, timezone
from typing import Any, Dict

from models.models import (
    UserDailyRevenue,
)
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse


def get_midnight_timestamp(dt_object):
    """获取给定datetime对象当天零点的时间戳"""
    return int(
        time.mktime(
            dt_object.replace(hour=0, minute=0, second=0, microsecond=0).timetuple()
        )
    )


def format_timestamp(ts):
    """将时间戳格式化为 YYYY-MM-DD，如果时间戳无效则返回 None"""
    if ts and isinstance(ts, (int, float)):
        try:
            return datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d")
        except (TypeError, ValueError):
            return None
    return None


@register_handler("pub_user_daily_revenue")
class PubUserDailyRevenueApi:

    @auth_required(["admin", "creator"])
    async def query_daily_summary(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """
        **目的**: 查询指定用户的每日收益汇总信息，支持分页。此方法先分页查询日期，再根据日期获取数据聚合。
        """
        user_id = data.get("user_id")
        page = data.get("page", 1)
        page_size = data.get("page_size", 10)

        pipeline = [
            {"$match": {"user_id": user_id}},
            {
                "$group": {
                    "_id": "$date",
                    "total_views": {"$sum": "$daily_views"},
                    "total_revenue": {"$sum": "$daily_revenue"},
                    "statuses": {"$addToSet": "$status"},
                    "settlement_times": {"$push": "$settled_at"},
                }
            },
            {
                "$addFields": {
                    "date_str": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": {"$toDate": {"$multiply": ["$_id", 1000]}},
                        }
                    },
                    "status": {
                        "$cond": {
                            "if": {"$in": ["已结算", "$statuses"]},
                            "then": "已结算",
                            "else": "未结算",
                        }
                    },
                    "latest_settled_ts": {"$max": "$settlement_times"},
                }
            },
            {
                "$addFields": {
                    "settlementDate": {
                        "$cond": {
                            "if": {"$eq": ["$status", "已结算"]},
                            "then": {
                                "$dateToString": {
                                    "format": "%Y-%m-%d",
                                    "date": {
                                        "$toDate": {
                                            "$multiply": ["$latest_settled_ts", 1000]
                                        }
                                    },
                                }
                            },
                            "else": None,
                        }
                    }
                }
            },
            {"$sort": {"_id": -1}},
            {
                "$facet": {
                    "metadata": [{"$count": "total"}],
                    "results": [
                        {"$skip": (page - 1) * page_size},
                        {"$limit": page_size},
                        {
                            "$project": {
                                "_id": 0,
                                "date": "$date_str",
                                "totalViews": "$total_views",
                                "totalRevenue": "$total_revenue",
                                "status": "$status",
                                "settlementDate": "$settlementDate",
                            }
                        },
                    ],
                }
            },
        ]

        result = await UserDailyRevenue.aggregate(pipeline).to_list()

        items = result[0]["results"] if result and result[0]["results"] else []
        total = result[0]["metadata"][0]["total"] if result and result[0]["metadata"] else 0

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=items,
        )

