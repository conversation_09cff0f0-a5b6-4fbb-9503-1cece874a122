"use client";

import {Box, Card, CardContent, CardMedia, Chip, Divider, Grid, Paper, Typography, TextField, Button, Modal, IconButton, Stack} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {MessageSquare, Send, UserCheck, ThumbsUp, Bookmark, Eye, BarChart2, Zap} from 'lucide-react';
import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';

// 模拟产品数据
const mockProduct = {
    id: 1,
    name: '简约风格双肩包',
    image: 'https://placehold.co/300x400/e0f2fe/0c4a6e?text=Backpack',
    platform: '小红书',
    comments: 1352,
    date: '2023-05-15',
    category: '箱包',
    description: '简约设计的学生双肩包，轻便耐用，多色可选',
    url: 'https://example.com/product/1',
    analysisData: {
        totalComments: 1352,
        positiveRate: 78,
        negativeRate: 8,
        neutralRate: 14,
        commentGrowth: 12,
        intentCustomers: 324,
        postedComments: 286,
        exposure: 2500
    }
};
// 统计卡片组件
const StatCard = ({title, value, color = 'primary', suffix = "", icon}) => {
    const theme = useTheme();

    return (
        <Card
            sx={{
                borderRadius: 2,
                boxShadow: 'none',
                border: `1px solid ${theme.palette.divider}`,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <CardContent sx={{p: 2, '&:last-child': {pb: 2}, flex: 1}}>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                    <Typography color="text.secondary" variant="body2">
                        {title}
                    </Typography>
                    {icon && (
                        <Box
                            sx={{
                                width: 36,
                                height: 36,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: theme.palette[color]?.light || theme.palette.primary.light,
                                color: theme.palette[color]?.main || theme.palette.primary.main
                            }}
                        >
                            {icon}
                        </Box>
                    )}
                </Box>

                <Typography
                    variant="h4"
                    component="div"
                    sx={{
                        color: theme.palette[color]?.main,
                        fontWeight: 'medium',
                        lineHeight: 1.2
                    }}
                >
                    {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
                </Typography>
            </CardContent>
        </Card>
    );
};

export default function ProductBasicInfo({ product = mockProduct }) {
    const theme = useTheme();
    const params = useParams();
    const router = useRouter();
    
    const currentExposure = product?.analysisData?.exposure || 0;

    if (!product) {
        return null;
    }

    return (
        <Paper
            elevation={0}
            sx={{
                p: 3,
                borderRadius: 2,
                mb: 4,
                border: `1px solid ${theme.palette.divider}`
            }}
        >
            <Grid container spacing={3} sx={{width: '100%', flexGrow: 1}}>
                <Grid size={{xs: 12, md: 3}}>
                    <Card sx={{borderRadius: 2, boxShadow: 'none', height: '100%'}}>
                        <CardMedia
                            component="img"
                            image={product.image}
                            alt={product.name}
                            sx={{
                                aspectRatio: '3/4',
                                objectFit: 'cover',
                                width: '100%'
                            }}
                        />
                    </Card>
                </Grid>

                <Grid size={{xs: 12, md: 9}}>
                    <Box>
                        <Box sx={{
                            display: 'flex', 
                            flexDirection: {xs: 'column', md: 'row'}, 
                            justifyContent: 'space-between', 
                            alignItems: {xs: 'flex-start', md: 'center'},
                            mb: {xs: 2, md: 0}
                        }}>
                            <Typography variant="h4" component="h1" fontWeight="bold" sx={{mb: {xs: 2, md: 0}}}>
                                {product.name}
                            </Typography>
                        </Box>

                        <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1}}>
                            <Chip
                                label={product.platform}
                                size="small"
                                color="primary"
                            />
                            <Chip
                                label={`曝光量: ${currentExposure}`}
                                size="small"
                                color="default"
                            />
                        </Box>

                        <Divider sx={{my: 3}}/>

                        <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                            评论基础数据
                        </Typography>

                        <Box sx={{mt: 2}}>
                            <Grid container spacing={2} sx={{width: '100%'}}>
                                <Grid size={{xs: 12, sm: 4}}>
                                    <Box sx={{height: '100%'}}>
                                        <StatCard
                                            title="评论数量"
                                            value={product.analysisData.totalComments}
                                            color="primary"
                                            icon={<MessageSquare size={20}/>}
                                        />
                                    </Box>
                                </Grid>
                                <Grid size={{xs: 12, sm: 4}}>
                                    <Box sx={{height: '100%'}}>
                                        <StatCard
                                            title="意向客户"
                                            value={product.analysisData.intentCustomers}
                                            color="success"
                                            icon={<UserCheck size={20}/>}
                                        />
                                    </Box>
                                </Grid>
                                <Grid size={{xs: 12, sm: 4}}>
                                    <Box sx={{height: '100%'}}>
                                        <StatCard
                                            title="布评总数"
                                            value={product.analysisData.postedComments}
                                            color="info"
                                            icon={<Send size={20}/>}
                                        />
                                    </Box>
                                </Grid>
                            </Grid>
                        </Box>
                    </Box>
                </Grid>
            </Grid>
        </Paper>
    );
} 