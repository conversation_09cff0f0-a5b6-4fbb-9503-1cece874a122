import {AlertType} from "@/core/components/alert";
import {addAlert} from "@/core/components/redux/alert-slice";
import {copyToClipboard} from "@/core/tools/common-tool";
import {ContentCopy} from "@mui/icons-material";
import ReactMarkdown from 'react-markdown';
import {useDispatch} from "react-redux";
import {Prism as SyntaxHighlighter} from 'react-syntax-highlighter';
import {tomorrow} from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkExternalLinks from 'remark-external-links';
import remarkGfm from 'remark-gfm';
import {Box, Typography} from "@mui/material";
import React, {useMemo} from 'react';

// 创建图片缓存
const imageCache = new Map();

// 统一的文件类型检查函数
const checkFileType = (url, extensions) => {
    const lowerUrl = url.toLowerCase();
    return extensions.some(ext => lowerUrl.includes(ext));
};

// 判断链接是否为可下载文件
const isDownloadableFile = (url) => {
    const fileExtensions = [
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
        '.zip', '.rar', '.7z', '.tar', '.gz',
        '.txt', '.csv', '.json', '.xml'
    ];
    return checkFileType(url, fileExtensions);
};

// 判断链接是否为图片
const isImageLink = (url) => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
    return checkFileType(url, imageExtensions);
};

const MemoizedImage = React.memo(function Image({src, alt, ...props}) {
    // 使用 useMemo 缓存图片 URL
    const cachedSrc = useMemo(() => {
        if (imageCache.has(src)) {
            return imageCache.get(src);
        }
        imageCache.set(src, src);
        return src;
    }, [src]);

    // 判断文件类型
    const isImage = isImageLink(src);
    const isFile = isDownloadableFile(src);

    if (!isImage && isFile) {
        return <DownloadLink href={src}/>;
    }

    return (
        <Box
            component="img"
            src={cachedSrc}
            alt={alt}
            loading="lazy"
            decoding="async"
            sx={{
                maxWidth: "500px",
                width: "100%",
                height: "auto",
                display: "block",
                margin: "1rem 0",
            }}
            {...props}
        />
    );
});

const ImageLink = ({href, children}) => (
    <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', my: 1}}>
        <MemoizedImage src={href} alt={children}/>
        <Typography variant="caption" sx={{mt: 0.5}}>
            {children}
        </Typography>
    </Box>
);

const DownloadLink = function DownloadLink({href, ...props}) {
    return (
        <Box
            component="a"
            href={href}
            download=""
            sx={{
                textDecoration: 'none',
                color: 'primary.main',
                '&:hover': {
                    textDecoration: 'underline'
                }
            }}
            {...props}
        >
            点击下载
        </Box>
    );
};

const NormalLink = ({href, target, rel, children, ...props}) => (
    <Box
        component="a"
        href={href}
        target={target}
        rel={rel}
        sx={{
            alignItems: 'center',
            textDecoration: 'none',
            color: 'primary.main',
            '&:hover': {
                textDecoration: 'underline'
            }
        }}
        {...props}
    >
        {children}
    </Box>
);

const MarkdownRenderer = React.memo(function MarkdownRenderer({content}) {
    const dispatch = useDispatch();

    const copyCode = (codeText) => {
        copyToClipboard(codeText)
        dispatch(addAlert({type: AlertType.SUCCESS, message: "复制成功"}));
    };

    // 统一的Markdown组件样式
    const markdownComponents = useMemo(() => ({
        a: function MarkdownLink({node, href, children, ...props}) {
            if (isImageLink(href)) {
                return <ImageLink href={href} children={children}/>;
            }
            if (isDownloadableFile(href)) {
                return <DownloadLink href={href}/>;
            }
            return <NormalLink href={href} children={children} {...props} />;
        },
        code: function MarkdownCode({node, inline, className, children, ...props}) {
            const match = /language-(\w+)/.exec(className || '');
            const codeText = String(children).replace(/\n$/, '');

            return !inline && match ? (
                <div className="relative">
                    <div className="absolute right-2 top-2">
                        <button onClick={() => copyCode(codeText)} className="text-white bg-green-500 border-none rounded p-1">
                            <ContentCopy fontSize="small"/>
                        </button>
                    </div>
                    <SyntaxHighlighter
                        language={match ? match[1] : 'text'}
                        style={tomorrow}
                        customStyle={{
                            borderRadius: '4px',
                            padding: '1rem',
                            marginTop: '0.5rem',
                            marginBottom: '0.5rem'
                        }}
                    >
                        {codeText}
                    </SyntaxHighlighter>
                </div>
            ) : (
                <Box component="code" sx={{
                    display: "block",
                    maxWidth: "100%",
                    whiteSpace: 'pre-wrap',
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                    overflowX: "hidden"
                }} {...props}>
                    {children}
                </Box>
            );
        },
        img: MemoizedImage
    }), []);

    return (
        <Box sx={{
            '& .markdown': {
                '& p': {fontSize: '0.875rem'},  // 14px
                '& h1': {fontSize: '1.5rem'},   // 24px
                '& h2': {fontSize: '1.25rem'},  // 20px
                '& h3': {fontSize: '1.125rem'}, // 18px
                '& h4': {fontSize: '1rem'},     // 16px
                '& h5': {fontSize: '0.875rem'}, // 14px
                '& h6': {fontSize: '0.75rem'},  // 12px
                '& li': {
                    fontSize: '0.875rem',
                    marginLeft: {
                        xs: '0.5rem',  // 手机端缩进
                        sm: '1rem'     // 平板及以上保持默认
                    }
                },
                '& ol, & ul': {
                    paddingLeft: {
                        xs: '0.5rem',  // 手机端缩进
                        sm: '1rem'     // 平板及以上保持默认
                    }
                },
                '& code': {fontSize: '0.8125rem'}, // 13px
            }
        }}>
            <ReactMarkdown
                className='markdown'
                remarkPlugins={[remarkGfm, remarkExternalLinks]}
                components={markdownComponents}
            >
                {content}
            </ReactMarkdown>
        </Box>
    );
});

export default MarkdownRenderer;