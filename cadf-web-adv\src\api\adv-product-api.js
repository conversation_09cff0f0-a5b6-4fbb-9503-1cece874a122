import api from "@/core/api/api";

const RESOURCE = "adv_product_api";

export const advProductApi = {
    create: async (title, description, domain, images) => {
        return await api({
            resource: RESOURCE,
            method_name: "create",
            data: {
                title,
                description,
                domain,
                images,
            },
        });
    },

    modify: async (id_, title, description, domain, images) => {
        return await api({
            resource: RESOURCE,
            method_name: "modify",
            data: {
                _id: id_,
                title,
                description,
                domain,
                images,
            },
        });
    },

    query_all: async (search = '', page = 1, page_size = 10) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_all",
            data: {
                search,
                page,
                page_size,
            },
        });
    },

    query_one: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_one",
            data: {
                _id: id_,
            },
        });
    },

    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    }
}