import io
import os
import uuid

import httpx
from PIL import Image

from common_config.common_config import LOCAL_TEMP_DIR


def download_image_to_temp(image_url: str) -> str:
    """
    下载图片到临时目录，并返回本地文件路径。

    :param image_url: 图片的URL
    :return: 本地临时文件路径
    """
    try:
        response = httpx.get(image_url)
        response.raise_for_status()  # 确保请求成功
        img_data = response.content

        # 从内存中读取图片
        img = Image.open(io.BytesIO(img_data))

        # 生成临时文件路径
        temp_filename = f"image_{uuid.uuid4().hex}.png"
        temp_path = os.path.join(LOCAL_TEMP_DIR, temp_filename)

        # 保存为PNG格式到临时目录
        img.save(temp_path, format="PNG")

        return temp_path
    except httpx.HTTPStatusError as e:
        # 可以根据需要添加更详细的错误处理和日志记录
        print(f"请求图片失败: {e.response.status_code} - {e.request.url}")
        raise
    except Exception as e:
        print(f"处理图片时发生错误: {e}")
        raise
