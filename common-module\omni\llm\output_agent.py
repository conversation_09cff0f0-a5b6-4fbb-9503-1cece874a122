import asyncio
import base64
import functools
import io
from typing import Type, Dict, Any, TypeVar, List, Optional, Callable

from PIL import Image
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel

from omni.llm.chat_model_factory import get_llm
from omni.log.log import olog
from omni.exception.retry_decorator import retry_on_exception

T = TypeVar("T", bound=BaseModel)
F = TypeVar("F", bound=Callable[..., Any])


async def _convert_image_to_png_base64(image_path: str) -> str:
    """将图片文件转换为PNG格式的base64编码字符串"""

    def _sync_convert_image():
        with Image.open(image_path) as img:
            if img.mode != 'RGB':
                img = img.convert('RGB')

            buffer = io.BytesIO()
            img.save(buffer, format='PNG')

            return base64.b64encode(buffer.getvalue()).decode('utf-8')

    return await asyncio.to_thread(_sync_convert_image)



@retry_on_exception(max_retries=3)
async def structured_output_handler(
        prompt_template: str,
        params: Dict[str, Any],
        output_model: Type[T],
        llm_name: Optional[str] = None,
        tags: Optional[List[str]] = None,
        image_path: Optional[str] = None,
) -> T:
    """异步版本的structured_prompt_agent，支持多模态输入"""
    olog.info("开始执行结构化输出处理")

    tags = tags or ["structured_output_agent"]
    llm = get_llm(llm_name)
    parser = PydanticOutputParser(pydantic_object=output_model)

    if "{format_instructions}" not in prompt_template:
        prompt_template += "\n\n{format_instructions}"

    params["format_instructions"] = parser.get_format_instructions()

    if image_path:
        olog.debug(f"处理图片路径: {image_path}")
        image_base64 = await _convert_image_to_png_base64(image_path)
        prompt = prompt_template.format(**params)
        message = HumanMessage(
            content=[
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}},
            ]
        )
        chain = llm | parser
        result = await chain.ainvoke([message], config=RunnableConfig(tags=tags))
    else:
        olog.debug("处理纯文本模式")
        prompt = ChatPromptTemplate.from_template(prompt_template)
        chain = prompt | llm | parser
        result = await chain.ainvoke(params, config=RunnableConfig(tags=tags))

    olog.info("结构化输出处理完成")
    return result


@retry_on_exception(max_retries=3)
async def text_output_handler(
        prompt_template: str,
        params: Dict[str, Any],
        llm_name: Optional[str] = None,
        tags: Optional[List[str]] = None,
        image_path: Optional[str] = None,
) -> str:
    """异步版本的文本输出处理函数，支持多模态输入"""
    olog.info("开始执行文本输出处理")

    tags = tags or ["text_output_handler"]
    llm = get_llm(llm_name)

    if image_path:
        olog.debug(f"处理图片路径: {image_path}")
        image_base64 = await _convert_image_to_png_base64(image_path)
        prompt = prompt_template.format(**params)
        message = HumanMessage(
            content=[
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}},
            ]
        )
        response = await llm.ainvoke([message], config=RunnableConfig(tags=tags))
    else:
        olog.debug("处理纯文本模式")
        prompt = ChatPromptTemplate.from_template(prompt_template)
        chain = prompt | llm
        response = await chain.ainvoke(params, config=RunnableConfig(tags=tags))

    result = response.content if hasattr(response, 'content') else str(response)
    olog.info("文本输出处理完成")
    return result


@retry_on_exception(max_retries=3)
async def text2structured_output_handler(output_model: Type[T], raw_data: str) -> T:
    """将原始数据转换为指定的 Pydantic 模型格式"""
    olog.info("开始执行文本转结构化处理")

    prompt_template = """
    # 任务
    将输入的数据转化为指定的结构化格式

    #输入的数据
    ```{data}```

    {format_instructions}
    """

    prompt = ChatPromptTemplate.from_template(prompt_template)
    llm = get_llm()
    parser = PydanticOutputParser(pydantic_object=output_model)

    chain = prompt | llm | parser
    params = {"data": raw_data, "format_instructions": parser.get_format_instructions()}
    config = RunnableConfig(tags=["text2structured_output_handler"])

    result = await chain.ainvoke(params, config=config)
    olog.info("文本转结构化处理完成")
    return result
