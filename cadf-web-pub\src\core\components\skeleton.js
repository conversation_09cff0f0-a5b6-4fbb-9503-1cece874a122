"use client";

import {Box, Container, Skeleton, Grid, Stack} from '@mui/material';
import {useTheme} from '@mui/material/styles';

export default function DefaultSkeleton() {
    const theme = useTheme();
    
    return (
        <Container disableGutters sx={{px: 3}}>
            <Grid container spacing={3}>
                <Grid size={12}>
                    <Stack spacing={3} sx={{py: 3}}>
                        <Skeleton 
                            variant="text" 
                            sx={{
                                fontSize: '1.25rem', 
                                borderRadius: 2,
                            }}
                        />
                        <Skeleton 
                            variant="rectangular" 
                            height={180} 
                            sx={{
                                borderRadius: 3, 
                                bgcolor: theme.palette.grey[100],
                            }}
                        />
                        <Stack direction="row" spacing={2}>
                            <Skeleton 
                                variant="rounded" 
                                width={80} 
                                height={24}
                                sx={{ borderRadius: 2 }}
                            />
                            <Skeleton 
                                variant="rounded" 
                                width={80} 
                                height={24}
                                sx={{ borderRadius: 2 }}
                            />
                        </Stack>
                        <Stack spacing={1}>
                            <Skeleton 
                                variant="text" 
                                sx={{ borderRadius: 1 }}
                            />
                            <Skeleton 
                                variant="text" 
                                sx={{ borderRadius: 1 }}
                            />
                            <Skeleton 
                                variant="text" 
                                width="65%" 
                                sx={{ borderRadius: 1 }}
                            />
                        </Stack>
                    </Stack>
                </Grid>
            </Grid>
        </Container>
    );
}