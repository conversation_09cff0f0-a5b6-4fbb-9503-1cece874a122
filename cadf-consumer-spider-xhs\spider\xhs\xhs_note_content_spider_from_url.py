"""
从URL进入小红书并且获取图片与评论 - Playwright版本
"""
from typing import Dict, <PERSON><PERSON>, List

from config.config import SpiderConfig
from omni.log.log import olog
from spider.schemas.note_content_schema import XHSNoteContentSchema
from spider.tools.browser_context import new_playwright_page
from omni.exception.retry_decorator import retry_on_exception

# --------------------
# 小红书笔记页面元素选择器
# --------------------
# 笔记作者选择器
AUTHOR_NAME_SELECTOR = '.author-wrapper .name .username'
# 笔记标题选择器
NOTE_TITLE_SELECTOR = '#detail-title'
# 笔记内容选择器
NOTE_CONTENT_SELECTOR = '#detail-desc .note-text'
# 发布时间选择器
PUBLISH_DATE_SELECTOR = '.bottom-container .date'
# 图片容器选择器
IMAGE_CONTAINER_SELECTOR = '.img-container img'
# 笔记滚动容器选择器，用于滚动加载图片
NOTE_SCROLLER_SELECTOR = '.note-scroller'


@retry_on_exception(max_retries=3)
async def crawl_note_from_url(url: str, cookies: List[Dict]) -> Tuple[XHSNoteContentSchema, bool]:
    """
    从指定的小红书笔记URL爬取笔记的图片、作者、内容和发布时间

    :param url: 小红书笔记URL
    :param cookies: cookies列表
    :return: 包含笔记信息的数据和登录状态的元组 (笔记数据, 是否登录)
    """
    olog.info(f"开始爬取小红书笔记: {url}")
    note_data = {
        'author': '',
        'title': '',
        'content': '',
        'date': '',
        'images': []
    }

    # 登录状态检查的API
    login_api_pattern = "/api/sns/web/v2/user/me"
    is_logged_in = False

    async with new_playwright_page(
            cookies=cookies,
            headless=SpiderConfig.HEADLESS,
            use_proxy=SpiderConfig.USE_PROXY,
            no_imgs=SpiderConfig.NO_IMGS
    ) as page:

        # 检查登录状态并导航
        olog.debug("检查登录状态")
        async with page.expect_response(
                lambda r: login_api_pattern in r.url,
                timeout=10000
        ) as response_info:
            await page.goto(url, wait_until="domcontentloaded")

        response = await response_info.value
        response_data = await response.json()
        data = response_data.get("data", {})
        if data.get("guest") is not True:
            is_logged_in = True
            olog.info("用户已登录")
        else:
            olog.warning("用户处于游客状态，未登录")

        # 等待页面加载
        await page.wait_for_selector(NOTE_SCROLLER_SELECTOR, timeout=10000)

        # 爬取作者信息
        author_element = await page.query_selector(AUTHOR_NAME_SELECTOR)
        if author_element:
            note_data['author'] = await author_element.text_content()

        # 爬取标题
        title_element = await page.query_selector(NOTE_TITLE_SELECTOR)
        if title_element:
            note_data['title'] = await title_element.text_content()

        # 爬取内容
        content_element = await page.query_selector(NOTE_CONTENT_SELECTOR)
        if content_element:
            note_data['content'] = await content_element.text_content()

        # 爬取发布时间
        date_element = await page.query_selector(PUBLISH_DATE_SELECTOR)
        if date_element:
            date_text = await date_element.text_content()
            note_data['date'] = date_text.strip() if date_text else ''

        # 爬取图片
        image_elements = await page.query_selector_all(IMAGE_CONTAINER_SELECTOR)
        if image_elements:
            for img in image_elements:
                img_url = await img.get_attribute('src')
                if img_url and img_url not in note_data['images']:
                    note_data['images'].append(img_url)
            
            # 把第一张图放到最后去
            if len(note_data['images']) > 1:
                first_image = note_data['images'].pop(0)
                note_data['images'].append(first_image)

    olog.info(f"笔记 '{note_data['title']}' 爬取完成，登录状态: {is_logged_in}")
    return XHSNoteContentSchema(**note_data), is_logged_in
