"use client";

import { useEffect, useState, useCallback } from 'react';
import {
  Alert, Box, Button, Chip, CircularProgress, Container,
  Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
  FormControl, Grid, IconButton, InputLabel, MenuItem, Pagination,
  Paper, Select, Typography
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ArrowLeft, Trash2, RefreshCw } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import ImageCard from '@/components/ImageCard';
import { advAiGeneratedMaterialApi } from '@/api/adv-ai-generated-material-api';

const ITEMS_PER_PAGE = 8;
const STATUS_OPTIONS = [
  { value: '', label: '全部' },
  { value: '待生成', label: '待生成' },
  { value: '生成中', label: '生成中' },
  { value: '已完成', label: '已完成' },
  { value: '失败', label: '失败' }
];

const getStatusColor = (status, type = 'default') => {
  switch (status) {
    case '已完成': return 'success';
    case '失败': return 'error';
    case '生成中': return 'warning';
    case '待生成': return 'info';
    default: return 'default';
  }
};

const getImageStatusColor = (status) => {
  switch (status) {
    case '已完成': return '#4caf50'; // 绿色
    case '失败': return '#f44336'; // 红色
    case '生成中': return '#ff9800'; // 橙色
    case '待生成': return '#2196f3'; // 蓝色
    default: return '#9e9e9e'; // 灰色
  }
};

const getTextStatusColor = (status) => {
  switch (status) {
    case '已完成': return '#8bc34a'; // 浅绿色
    case '失败': return '#e91e63'; // 粉红色
    case '生成中': return '#ff5722'; // 深橙色
    case '待生成': return '#673ab7'; // 紫色
    default: return '#607d8b'; // 蓝灰色
  }
};

export default function GenerationListPage() {
  const theme = useTheme();
  const router = useRouter();
  const params = useParams();
  const { enqueueSnackbar } = useSnackbar();
  
  // State management
  const [page, setPage] = useState(1);
  const [results, setResults] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [deleteCandidateId, setDeleteCandidateId] = useState(null);
  const [imageGenerationStatus, setImageGenerationStatus] = useState('');
  const [textGenerationStatus, setTextGenerationStatus] = useState('');
  const [regenerating, setRegenerating] = useState(false);

  // Helper functions
  const processResults = useCallback((results) => {
    return results
      .filter(item => item && item._id)
      .map(item => ({
        ...item,
        id: item._id,
        images: (item.images || []).map(img => 
          img.signed_url || 'https://placehold.co/300x200/cccccc/333333?text=URL+Error'
        ),
        viralTitle: item.title || item.content?.substring(0, 50) || '无标题',
        product: item.product_id || 'N/A',
        material: item.material_id || 'N/A',
      }));
  }, []);

  const fetchData = useCallback(async () => {
    const taskId = params?.id;
    if (!taskId) {
      setError("缺少任务 ID");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await advAiGeneratedMaterialApi.queryByTaskId(
        taskId,
        page,
        ITEMS_PER_PAGE,
        imageGenerationStatus || undefined,
        textGenerationStatus || undefined
      );
      
      setTotalCount(response.total || 0);
      setResults(processResults(response.results || []));
    } catch (err) {
      console.error("Failed to fetch generated materials:", err);
      setError(err.message || '获取生成内容失败');
      setTotalCount(0);
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, [params?.id, page, imageGenerationStatus, textGenerationStatus, processResults]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Computed values
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);
  
  // Event handlers
  const handlePageChange = useCallback((event, value) => {
    setPage(value);
  }, []);

  const handleGoBack = useCallback(() => {
    router.push('/protected/ai-image-text-generation');
  }, [router]);

  const handleContentSelect = useCallback((contentId) => {
    router.push(`/protected/ai-image-text-generation/${params.id}/detail/${contentId}`);
  }, [router, params.id]);

  const handleStatusChange = useCallback((type, value) => {
    if (type === 'image') {
      setImageGenerationStatus(value);
    } else {
      setTextGenerationStatus(value);
    }
    setPage(1);
  }, []);

  const handleClearFilters = useCallback(() => {
    setImageGenerationStatus('');
    setTextGenerationStatus('');
    setPage(1);
  }, []);

  const handleReject = useCallback((e, contentId) => {
    e.stopPropagation();
    setDeleteCandidateId(contentId);
    setOpenConfirmDialog(true);
  }, []);

  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setDeleteCandidateId(null);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!deleteCandidateId) return;

    handleCloseConfirmDialog();

    try {
      await advAiGeneratedMaterialApi.delete(deleteCandidateId);
      setResults(prev => prev.filter(item => item.id !== deleteCandidateId));
      setTotalCount(prev => prev - 1);

      if (results.length === 1 && page > 1) {
        setPage(prevPage => prevPage - 1);
      }
    } catch (err) {
      console.error("Failed to delete material:", err);
      enqueueSnackbar(`删除失败: ${err.message || '未知错误'}`, { variant: 'error' });
    }
  }, [deleteCandidateId, handleCloseConfirmDialog, results.length, page, enqueueSnackbar]);

  const handleRegenerateFailedMaterials = useCallback(async () => {
    const taskId = params?.id;
    if (!taskId) return;

    setRegenerating(true);
    try {
      await advAiGeneratedMaterialApi.regenerateFailedMaterials(taskId);
      enqueueSnackbar('重新生成请求已提交', { variant: 'success' });
      
      // 刷新数据
      fetchData();
    } catch (err) {
      console.error("Failed to regenerate failed materials:", err);
      enqueueSnackbar(`重新生成失败: ${err.message || '未知错误'}`, { variant: 'error' });
    } finally {
      setRegenerating(false);
    }
  }, [params?.id, enqueueSnackbar, fetchData]);

    return (
        <Container maxWidth="xl" sx={{py: 3}}>
            <Box>
                <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                    <Button
                        startIcon={<ArrowLeft size={18}/>}
                        onClick={handleGoBack}
                        sx={{mr: 2}}
                    >
                        返回生成列表
                    </Button>
                    <Typography variant="h5" component="h1" sx={{fontWeight: 600, flexGrow: 1}}>
                        生成内容列表
                    </Typography>
                </Box>

                <Paper
                    elevation={0}
                    sx={{
                        mb: 4,
                        p: 0,
                        overflow: 'hidden',
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <Box sx={{
                        p: 2,
                        bgcolor: theme.palette.primary.light,
                        color: theme.palette.primary.contrastText,
                        borderBottom: `1px solid ${theme.palette.divider}`
                    }}>
                        <Typography variant="subtitle1" fontWeight="medium">
                            任务的生成内容
                        </Typography>
                    </Box>

                    <Box sx={{p: 3}}>
                        {loading && (
                            <Box sx={{display: 'flex', justifyContent: 'center', py: 5}}>
                                <CircularProgress/>
                            </Box>
                        )}

                        {error && (
                            <Alert severity="error" sx={{mb: 2}}>{error}</Alert>
                        )}

                        {!loading && !error && (
                            <>
                                <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                                    <FormControl sx={{ minWidth: 150 }} size="small">
                                        <InputLabel>图片生成状态</InputLabel>
                                        <Select
                                            value={imageGenerationStatus}
                                            label="图片生成状态"
                                            onChange={(e) => handleStatusChange('image', e.target.value)}
                                        >
                                            {STATUS_OPTIONS.map(option => (
                                                <MenuItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                    
                                    <FormControl sx={{ minWidth: 150 }} size="small">
                                        <InputLabel>文本生成状态</InputLabel>
                                        <Select
                                            value={textGenerationStatus}
                                            label="文本生成状态"
                                            onChange={(e) => handleStatusChange('text', e.target.value)}
                                        >
                                            {STATUS_OPTIONS.map(option => (
                                                <MenuItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                    
                                    {(imageGenerationStatus || textGenerationStatus) && (
                                        <Button 
                                            variant="outlined" 
                                            size="small"
                                            onClick={handleClearFilters}
                                        >
                                            清除筛选
                                        </Button>
                                    )}
                                    
                                    <Button 
                                        variant="contained" 
                                        size="small"
                                        color="primary"
                                        startIcon={regenerating ? <CircularProgress size={16} color="inherit" /> : <RefreshCw size={16} />}
                                        onClick={handleRegenerateFailedMaterials}
                                        disabled={regenerating}
                                        sx={{ ml: 'auto' }}
                                    >
                                        {regenerating ? '重新生成中...' : '重新生成失败素材'}
                                    </Button>
                                </Box>

                                <Box sx={{mb: 2}}>
                                    <Typography variant="body2" color="text.secondary">
                                        共找到 {totalCount} 个生成内容
                                    </Typography>
                                </Box>

                                <Grid container spacing={3} sx={{ width: '100%' }}>
                                    {results.map((content) => (
                                        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={content.id}>
                                            <ImageCard
                                                image={content.images?.[0]}
                                                onClick={() => handleContentSelect(content.id)}
                                            >
                                                <Box sx={{p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column'}}>
                                                    <Typography gutterBottom variant="subtitle1" component="div" noWrap sx={{mb: 1, fontWeight: 500}}>
                                                        {content.title || '无标题'}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary" sx={{mb: 2, flexGrow: 1, overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical'}}>
                                                        {content.content?.substring(0, 100) || '无内容'}
                                                    </Typography>
                                                    <Box sx={{ mb: 2 }}>
                                                        <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                                                            <Chip
                                                                label={`图片: ${content.image_generation_status || '未知'}`}
                                                                size="small"
                                                                variant="filled"
                                                                sx={{ 
                                                                    fontSize: '0.7rem', 
                                                                    height: 20,
                                                                    backgroundColor: getImageStatusColor(content.image_generation_status),
                                                                    color: 'white',
                                                                    fontWeight: 500,
                                                                    '& .MuiChip-label': {
                                                                        px: 1
                                                                    }
                                                                }}
                                                            />
                                                            <Chip
                                                                label={`文本: ${content.text_generation_status || '未知'}`}
                                                                size="small"
                                                                variant="filled"
                                                                sx={{ 
                                                                    fontSize: '0.7rem', 
                                                                    height: 20,
                                                                    backgroundColor: getTextStatusColor(content.text_generation_status),
                                                                    color: 'white',
                                                                    fontWeight: 500,
                                                                    '& .MuiChip-label': {
                                                                        px: 1
                                                                    }
                                                                }}
                                                            />
                                                        </Box>
                                                        {content.generation_fail_reason && (
                                                            <Typography 
                                                                variant="caption" 
                                                                sx={{ 
                                                                    display: 'block',
                                                                    fontSize: '0.7rem',
                                                                    lineHeight: 1.2,
                                                                    backgroundColor: 'error.light',
                                                                    color: 'error.contrastText',
                                                                    px: 1,
                                                                    py: 0.5,
                                                                    borderRadius: 1,
                                                                    wordBreak: 'break-word'
                                                                }}
                                                            >
                                                                失败原因: {content.generation_fail_reason}
                                                            </Typography>
                                                        )}
                                                    </Box>
                                                    
                                                </Box>
                                                <Box sx={{
                                                    display: 'flex',
                                                    justifyContent: 'flex-end',
                                                    alignItems: 'center',
                                                    p: 1,
                                                    borderTop: `1px solid ${theme.palette.divider}`,
                                                    bgcolor: theme.palette.grey[50]
                                                }}>
                                                    <IconButton
                                                        aria-label="delete"
                                                        size="small"
                                                        onClick={(e) => handleReject(e, content.id)}
                                                        sx={{
                                                            color: theme.palette.error.main,
                                                            '&:hover': {
                                                                bgcolor: theme.palette.error.light + '20',
                                                            }
                                                        }}
                                                    >
                                                        <Trash2 size={16}/>
                                                    </IconButton>
                                                </Box>
                                            </ImageCard>
                                        </Grid>
                                    ))}
                                </Grid>

                                {totalCount === 0 && !loading && (
                                    <Box sx={{py: 4, textAlign: 'center'}}>
                                        <Typography color="text.secondary">未找到任何内容</Typography>
                                    </Box>
                                )}

                                {totalPages > 1 && (
                                    <Box sx={{mt: 4, display: 'flex', justifyContent: 'center'}}>
                                        <Pagination
                                            count={totalPages}
                                            page={page}
                                            onChange={handlePageChange}
                                            color="primary"
                                            size="medium"
                                        />
                                    </Box>
                                )}
                            </>
                        )}
                    </Box>
                </Paper>
            </Box>

            <Dialog
                open={openConfirmDialog}
                onClose={handleCloseConfirmDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{"确认删除"}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        确定要删除这个生成内容吗？此操作无法撤销。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseConfirmDialog} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleConfirmDelete} color="error" autoFocus>
                        确认删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
} 