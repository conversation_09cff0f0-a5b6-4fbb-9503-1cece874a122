"use client";

import React, { useEffect, useRef, useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";

/**
 * 简单词云组件 - 当标准词云组件出现问题时的替代方案
 * @param {Object} props
 * @param {Array<{text: string, value: number}>} props.words - 单词与权重数组，例如：[{text: "云计算", value: 30}, {text: "大数据", value: 20}]
 * @param {number|string} [props.width="100%"] - 词云宽度
 * @param {string} [props.aspectRatio="4:3"] - 宽高比，例如"4:3"
 * @param {number} [props.minFontSize=14] - 最小字体大小
 * @param {number} [props.maxFontSize=80] - 最大字体大小
 * @param {Function} [props.onWordClick] - 点击单词的回调函数
 */
const SimpleWordCloud = ({
  words = [],
  width = "100%",
  aspectRatio = "16:9",
  minFontSize = 20,
  maxFontSize = 60,
  onWordClick,
}) => {
  const theme = useTheme();
  const containerRef = useRef(null);
  const [wordPositions, setWordPositions] = useState([]);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [calculatedHeight, setCalculatedHeight] = useState("300px"); // 默认初始高度，会被宽高比计算覆盖
  
  // 处理宽高比
  useEffect(() => {
    if (!containerRef.current) return;
    
    // 解析宽高比，格式如 "4:3"
    const [widthRatio, heightRatio] = aspectRatio.split(':').map(Number);
    
    if (!widthRatio || !heightRatio) return;
    
    const updateAspectRatio = () => {
      const containerWidth = containerRef.current.offsetWidth;
      const newHeight = (containerWidth * heightRatio) / widthRatio;
      setCalculatedHeight(`${newHeight}px`);
    };
    
    updateAspectRatio();
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateAspectRatio);
    
    return () => {
      window.removeEventListener('resize', updateAspectRatio);
    };
  }, [aspectRatio]);
  
  // 检测容器尺寸
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      // 添加安全检查，确保组件未被卸载
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      setContainerDimensions({
        width: rect.width,
        height: rect.height
      });
    };
    
    // 初始化尺寸
    updateDimensions();
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateDimensions);
    
    // 创建ResizeObserver监听容器大小变化
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(() => {
        // 这里也需要添加安全检查
        if (!containerRef.current) return;
        updateDimensions();
      });
      
      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }
      
      return () => {
        window.removeEventListener('resize', updateDimensions);
        resizeObserver.disconnect();
      };
    }
    
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);
  
  if (!words || words.length === 0) {
    return (
      <Box 
        ref={containerRef}
        sx={{ 
          width, 
          height: calculatedHeight,
          bgcolor: "background.paper", 
          display: "flex", 
          justifyContent: "center", 
          alignItems: "center" 
        }}
      >
        暂无数据
      </Box>
    );
  }

  // 计算最大和最小值，用于字体大小计算
  const maxValue = Math.max(...words.map(item => item.value));
  const minValue = Math.min(...words.map(item => item.value));
  
  // 颜色列表
  const colorList = [
    theme.palette.primary.main,
    theme.palette.primary.light,
    theme.palette.secondary.main,
    theme.palette.secondary.light,
    theme.palette.info.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
  ];

  // 计算字体大小
  const calculateFontSize = (value) => {
    if (maxValue === minValue) return (minFontSize + maxFontSize) / 2;
    const ratio = (value - minValue) / (maxValue - minValue);
    return minFontSize + ratio * (maxFontSize - minFontSize);
  };
  
  // 选择颜色
  const getWordColor = (text) => {
    const index = Math.abs(text.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0)) % colorList.length;
    
    return colorList[index];
  };
  
  // 计算词语安全位置
  useEffect(() => {
    if (!containerRef.current || containerDimensions.width === 0 || containerDimensions.height === 0) return;
    
    // 计算词语位置，确保不超出边界且不重叠
    const calculateWordPositions = () => {
      // 添加安全检查，确保组件未被卸载
      if (!containerRef.current) return;
      
      const containerWidth = containerDimensions.width;
      const containerHeight = containerDimensions.height;
      
      // 按照权重从大到小排序，保证重要词语优先放置
      const sortedWords = [...words].sort((a, b) => b.value - a.value);
      
      // 已放置的词语区域记录，用于碰撞检测
      const occupiedAreas = [];
      
      // 计算每个词语的位置
      const positions = [];
      
      for (const word of sortedWords) {
        const fontSize = calculateFontSize(word.value);
        // 根据文本长度和字体大小估算宽度和高度
        const estimatedWidth = word.text.length * fontSize * 1.1;
        const estimatedHeight = fontSize * 1.2;
        
        // 最大尝试次数，防止无限循环
        const maxAttempts = 100;
        let attempts = 0;
        let foundValidPosition = false;
        
        // 尝试找到一个不重叠的位置
        while (attempts < maxAttempts && !foundValidPosition) {
          // 计算安全边界
          const maxLeft = Math.max(10, estimatedWidth / 2);
          const maxRight = containerWidth - maxLeft;
          const maxTop = Math.max(10, estimatedHeight / 2);
          const maxBottom = containerHeight - maxTop;
          
          // 生成随机位置
          const left = Math.random() * (maxRight - maxLeft) + maxLeft;
          const top = Math.random() * (maxBottom - maxTop) + maxTop;
          
          // 当前词语占据的矩形区域
          const currentRect = {
            left: left - estimatedWidth / 2,
            right: left + estimatedWidth / 2,
            top: top - estimatedHeight / 2,
            bottom: top + estimatedHeight / 2
          };
          
          // 检查是否与已放置的词语重叠
          let hasOverlap = false;
          for (const area of occupiedAreas) {
            if (
              currentRect.left < area.right &&
              currentRect.right > area.left &&
              currentRect.top < area.bottom &&
              currentRect.bottom > area.top
            ) {
              hasOverlap = true;
              break;
            }
          }
          
          if (!hasOverlap) {
            // 找到有效位置
            occupiedAreas.push(currentRect);
            positions.push({
              left: (left / containerWidth) * 100,
              top: (top / containerHeight) * 100,
              fontSize,
              color: getWordColor(word.text)
            });
            foundValidPosition = true;
          }
          
          attempts++;
        }
        
        // 如果实在找不到不重叠的位置，就使用最后一次尝试的位置
        if (!foundValidPosition) {
          const fallbackLeft = Math.random() * containerWidth;
          const fallbackTop = Math.random() * containerHeight;
          positions.push({
            left: (fallbackLeft / containerWidth) * 100,
            top: (fallbackTop / containerHeight) * 100,
            fontSize,
            color: getWordColor(word.text)
          });
        }
      }
      
      setWordPositions(positions);
    };
    
    calculateWordPositions();
  }, [words, containerDimensions, minFontSize, maxFontSize]);

  return (
    <Box 
      ref={containerRef}
      sx={{ 
        width, 
        height: calculatedHeight,
        bgcolor: "background.paper",
        position: "relative",
        overflow: "hidden"
      }}
    >
      {words.map((word, index) => {
        const position = wordPositions[index] || { left: 50, top: 50, fontSize: minFontSize, color: theme.palette.primary.main };
        
        return (
          <Typography
            key={`${word.text}-${index}`}
            sx={{
              position: "absolute",
              left: `${position.left}%`,
              top: `${position.top}%`,
              fontSize: position.fontSize,
              color: position.color,
              fontWeight: position.fontSize > (minFontSize + maxFontSize) / 2 ? "bold" : "normal",
              cursor: "pointer",
              transform: `translate(-50%, -50%)`,
              whiteSpace: "nowrap",
              textShadow: "0px 0px 1px rgba(0,0,0,0.1)",
              maxWidth: "90%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              "&:hover": {
                opacity: 0.8,
              }
            }}
            onClick={(e) => onWordClick && onWordClick(word, e)}
          >
            {word.text}
          </Typography>
        );
      })}
    </Box>
  );
};

export default SimpleWordCloud; 