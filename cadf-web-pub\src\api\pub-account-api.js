import api from "@/core/api/api";

const RESOURCE = "pub_account_api";

export const pubAccountApi = {
    create: async (platform, cookie) => {
        return await api({
            resource: RESOURCE,
            method_name: "create",
            data: {
                platform,
                cookie,
            },
        });
    },

    modify: async (id_, name, domain) => {
        return await api({
            resource: RESOURCE,
            method_name: "modify",
            data: {
                _id: id_,
                name,
                domain,
            },
        });
    },

    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    },

    queryOne: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_one",
            data: {
                _id: id_,
            },
        });
    },

    queryAll: async (platform) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_all",
            data: {
                platform,
            },
        });
    },
};