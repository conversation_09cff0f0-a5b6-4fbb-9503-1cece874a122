from typing import Dict, Any
from omni.llm.output_agent import structured_output_handler
from pydantic import BaseModel

from agent.state import OverallState
from omni.log.log import olog

# 固定的自拍照风格提示词
PORTRAIT_STYLE_PROMPT = """人像摄影，日常快照风格，非精心构图或打光
用 iPhone 后置镜头拍照，略带快门速度不够造成的运动模糊
略带快门速度不够造成的运动模糊，构图随意、角度尴尬、画面不够对称或美观，画质带有日常感和粗糙感
整体呈现出一种刻意的平庸感-就像是从口袋里拿手机时不小心拍到的一张自拍。
出镜人物必须是网红脸的美女或者帅哥。"""


class DetailedPromptModel(BaseModel):
    chinese_prompt: str


async def generate_portrait_prompt(state: OverallState) -> Dict[str, Any]:
    """生成人像提示词节点"""
    olog.info("开始执行人像提示词生成节点")
    
    prompt_template = """# 角色
你是一名专精于AI绘画提示词工程的专家。

# 背景
图片描述:
```
{image_elements}
```

# 任务
根据图片描述，生成更详细、更丰富、更有创意的图片描述和提示词，适合直接作为AI绘画模型的输入。

请在生成的提示词中必须融入以下提示词：
```
{portrait_style}
```

# 返回值
```
# 中文提示词
[在这里提供详细的中文提示词，必须包含上述自拍照固定文本]
```"""
    
    result_model = await structured_output_handler(
        prompt_template=prompt_template,
        params={
            "image_elements": state.image_elements,
            "portrait_style": PORTRAIT_STYLE_PROMPT
        },
        output_model=DetailedPromptModel,
        llm_name="QWEN_PLUS"
    )
    
    olog.info("提示词生成完成")
    return {"generated_prompt": result_model.chinese_prompt}