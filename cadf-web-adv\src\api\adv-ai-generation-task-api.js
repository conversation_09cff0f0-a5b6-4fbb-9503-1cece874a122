import api from "@/core/api/api";

const RESOURCE = "adv_ai_generation_task_api";

export const advAiGenerationTaskApi = {
    create: async (productId, materialIds, targetQuantity) => {
        return await api({
            resource: RESOURCE,
            method_name: "create",
            data: {
                product_id: productId,
                material_ids: materialIds,
                target_quantity: targetQuantity,
            },
        });
    },

    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    },

    query_all: async (taskName, page, pageSize) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_all",
            data: {
                task_name: taskName,
                page: page,
                page_size: pageSize,
            },
        });
    }
}