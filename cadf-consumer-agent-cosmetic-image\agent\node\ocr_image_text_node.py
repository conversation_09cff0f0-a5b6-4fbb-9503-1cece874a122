from typing import Dict, Any
from pydantic import BaseModel, Field

from omni.llm.output_agent import structured_output_handler
from agent.state import OverallState
from omni.log.log import olog


class OcrOutput(BaseModel):
    """OCR 识别的结构化输出"""
    has_text: bool = Field(..., description="是否识别到文字")
    content: str = Field(..., description="识别的文字内容")


async def ocr_image_text(state: OverallState) -> Dict[str, Any]:
    """图片文字识别节点"""
    olog.info("开始执行图片文字识别节点")
    
    image_path: str = state.scene_image_path
    olog.debug(f"使用图片路径: {image_path}")
    
    prompt_template: str = """
# 角色
你是一个专业的图像文字识别引擎。

# 任务
请识别并提取图片中的文字。
不要识别印刷在产品上的logo、品牌名字、使用说明等文字。
"""

    structured_result: OcrOutput = await structured_output_handler(
        prompt_template=prompt_template,
        params={},
        output_model=OcrOutput,
        llm_name="QWEN_VL_MAX",
        tags=["ocr_image_text"],
        image_path=image_path
    )
    
    olog.debug(f"原始识别结果: 有文字={structured_result.has_text}, 内容长度={len(structured_result.content.strip())}")
    
    if len(structured_result.content.strip()) <= 10:
        structured_result.has_text = False
        olog.debug("文字内容过短，标记为无文字")
    
    olog.info(f"图片文字识别节点执行完成: 有文字={structured_result.has_text}")
    
    result = {
        "ocr_text": structured_result.content,
        "has_text": structured_result.has_text
    }
    
    # 如果没有文字，这将成为终点节点，需要设置final_image_path
    if not structured_result.has_text:
        if hasattr(state, 'processed_image_path') and state.processed_image_path:
            result["final_image_path"] = state.processed_image_path
            olog.debug(f"无文字图片，设置final_image_path: {state.processed_image_path}")
        else:
            # 如果还没有processed_image_path，说明前面的处理步骤失败了
            olog.error("前面的图片处理步骤失败，没有生成processed_image_path")
            raise Exception("图片处理失败，无法生成处理后的图片")
    
    return result