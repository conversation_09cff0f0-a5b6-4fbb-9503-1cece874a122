import traceback
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, <PERSON><PERSON>

from config.config import SpiderConfig
from omni.exception.retry_decorator import retry_on_exception
from omni.log.log import olog
from spider.schemas.note_metrics_schema import NoteMetrics
from spider.schemas.xhs_user_posted_notes_schema import XHSUserPostedNotesResponse
from spider.tools.browser_context import new_playwright_page

@retry_on_exception(max_retries=3)
async def crawl_account_metrics(cookies: List[Dict], days_ago: int = 7) -> Tu<PERSON>[List[NoteMetrics], bool]:
    url = "https://creator.xiaohongshu.com/new/note-manager?source=official"
    olog.info(f"开始爬取小红书账户指标: {url}")

    time_threshold = datetime.now() - timedelta(days=days_ago)
    api_pattern = '/web_api/sns/v5/creator/note/user/posted'
    login_api_pattern = "/api/galaxy/user/info"

    async with new_playwright_page(
            cookies=cookies,
            headless=SpiderConfig.HEADLESS,
            use_proxy=SpiderConfig.USE_PROXY,
            no_imgs=SpiderConfig.NO_IMGS
    ) as page:
        olog.debug("检查登录状态并获取初始指标数据")

        is_logged_in = True
        try:
            async with page.expect_response(lambda r: login_api_pattern in r.url, timeout=100 * 1000) as login_info, \
                    page.expect_response(lambda r: api_pattern in r.url, timeout=100 * 1000) as metrics_info:
                await page.goto(url, wait_until="domcontentloaded")

            login_response = await login_info.value
            metrics_response = await metrics_info.value
            olog.info("用户已登录")
        except Exception as e:
            if 'playwright._impl._errors.TimeoutError' in traceback.format_exc():
                is_logged_in = False
                olog.info("用户未登录")
                return [], is_logged_in
            else:
                raise e

        response_data = await metrics_response.json()
        validated_response = XHSUserPostedNotesResponse.model_validate(response_data)

        metrics_data = []
        for note in validated_response.data.notes:
            publish_date = datetime.strptime(note.time, '%Y-%m-%d %H:%M')

            if publish_date < time_threshold:
                olog.debug(f"笔记 {note.id} 发布于 {publish_date}，超出时间范围，停止处理")
                break

            metrics_data.append(NoteMetrics(
                note_id=note.id,
                title=note.display_title,
                publish_date=publish_date.isoformat(),
                views=note.view_count,
                comments=note.comments_count,
                likes=note.likes,
                favorites=note.collected_count,
                shares=note.shared_count,
            ))

        olog.info(f"账户指标爬取完成，共获取 {len(metrics_data)} 条数据，登录状态: {is_logged_in}")
        return metrics_data, is_logged_in
