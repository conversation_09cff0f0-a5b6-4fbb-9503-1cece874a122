import time
from typing import Dict, Any, List

from common_config.common_config import RedisKeyConfig
from models.models import PromotionTaskDetail, Account, AiGeneratedMaterial
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from spider.xhs.xhs_note_content_spider_from_url import crawl_note_from_url


@consume_redis_set(redis_key=RedisKeyConfig.XHS_NOTE_URL_VERIFY_SET, num_tasks=1)
async def handle_task(message_data: Dict[str, Any]) -> None:
    """处理小红书发布验证任务"""
    task_detail_id: str = message_data["id_"]
    olog.info(f"开始处理发布验证任务: {task_detail_id}")
    
    try:
        task_detail: PromotionTaskDetail = await PromotionTaskDetail.get(task_detail_id)
        account: Account = await Account.get(task_detail.account_id)
        material: AiGeneratedMaterial = await AiGeneratedMaterial.get(task_detail.ai_generated_material_id)

        if account.platform != "小红书":
            task_detail.validation_status = '失败'
            task_detail.validation_details = f"不支持平台 '{account.platform}' 的发布验证"
            await task_detail.save()
            olog.warning(f"任务 {task_detail_id} 平台不支持: {account.platform}")
            return

        # 爬取发布内容
        olog.debug("开始爬取发布内容")
        scraped_data, is_logged_in = await crawl_note_from_url(
            url=task_detail.publish_url,
            cookies=account.cookie
        )

        # 更新账号登录状态
        current_time: int = int(time.time())
        new_status: str = "在线" if is_logged_in else "离线"
        
        account.status = new_status
        account.last_login_check_at = current_time
        await account.save()
        olog.debug(f"账号 {account.name} 状态更新为: {new_status}")

        failures: List[str] = []
        
        # 验证标题
        if not scraped_data.title.startswith(material.title):
            failures.append("发布的标题与预期不符，请检查标题是否正确发布")

        # 验证图片数量
        expected_count: int = len(material.images) if material.images else 0
        actual_count: int = len(scraped_data.images)
        if expected_count != actual_count:
            if actual_count == 0:
                failures.append("未检测到任何图片，请确认图片是否成功上传")
            elif actual_count < expected_count:
                failures.append(f"图片数量不足，应上传{expected_count}张图片，实际只有{actual_count}张")
            else:
                failures.append(f"图片数量过多，应上传{expected_count}张图片，实际上传了{actual_count}张")

        # 更新任务状态
        if failures:
            task_detail.validation_status = '失败'
            task_detail.validation_details = "; ".join(failures)
            olog.warning(f"任务 {task_detail_id} 验证失败: {'; '.join(failures)}")
        else:
            task_detail.validation_status = '成功'
            task_detail.validation_details = None
            olog.debug(f"任务 {task_detail_id} 验证成功")

        await task_detail.save()
        olog.info(f"任务 {task_detail_id} 处理完成")

    except Exception as e:
        olog.exception(f"处理发布验证任务 {task_detail_id} 时发生错误")
        task_detail = await PromotionTaskDetail.get(task_detail_id)
        task_detail.validation_status = '失败'
        task_detail.validation_details = f"系统内部错误: {str(e)[:100]}"
        await task_detail.save()
