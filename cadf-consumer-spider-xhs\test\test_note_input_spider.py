"""
小红书笔记搜索和评论爬虫测试例子
"""
import asyncio
import json

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from spider.xhs.xhs_note_input_spider_from_explore import search_and_comment
from models.models import Account


async def test_note_input_spider():
    """
    测试小红书笔记搜索和评论爬虫
    """
    try:
        await init_models()
        # 从数据库获取指定ID的账户cookie
        account = await Account.get("68724c33435ce73710ab0bc8")
        if not account or not account.cookie:
            olog.error("未找到ID为123123的账户或该账户没有cookie")
            return
        
        cookies = account.cookie
        olog.info(f"使用账户 {account.name} 的cookie进行测试")
        
        # 测试参数 - 请根据实际需求修改
        search_keyword = "美妆"          # 搜索关键字
        filter_keyword = "护肤"          # 筛选关键字（笔记标题中需包含此关键字）
        comment_text = "很棒的分享！"    # 要发布的评论内容
        
        olog.info(f"测试参数:")
        olog.info(f"  - 搜索关键字: {search_keyword}")
        olog.info(f"  - 筛选关键字: {filter_keyword}")
        olog.info(f"  - 评论内容: {comment_text}")
        
        olog.warning("注意：此操作会实际发布评论，请谨慎使用！")
        
        # 调用爬虫函数
        success: bool = await search_and_comment(
            search_keyword=search_keyword,
            filter_keyword=filter_keyword,
            comment_text=comment_text,
            cookies=cookies
        )
        
        # 打印结果
        olog.info("搜索和评论操作结果:")
        
        # 构建结果数据并转换为JSON
        result_data = {
            "operation": "search_and_comment",
            "parameters": {
                "search_keyword": search_keyword,
                "filter_keyword": filter_keyword,
                "comment_text": comment_text
            },
            "success": success,
            "status": "评论发布成功" if success else "评论发布失败或未找到匹配的笔记"
        }
        
        formatted_json = json.dumps(result_data, ensure_ascii=False, indent=2, default=str)
        olog.info(formatted_json)
            
    except Exception as e:
        olog.exception(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_note_input_spider())