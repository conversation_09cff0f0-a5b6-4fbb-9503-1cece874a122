from agent.product_description_recog_agent import product_description_recog
from agent.product_domain_recog_agent import product_domain_recog
from agent.product_name_recog_agent import product_name_recog
from models.models import DataDictionary
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler


@register_handler('adv_ai_agent_api')
class AdvAiAgentApi:

    @auth_required(['admin', 'advertiser'])
    async def generate_description(self, data):
        """
        根据提供的图片URL生成产品描述。
        """
        image_url = data.get('image_url')

        # 输入验证
        if not image_url:
            raise MException("image_url 不能为空")

        if not isinstance(image_url, str) or not image_url.strip():
            raise MException("image_url 必须是有效的字符串")

        description = await product_description_recog([image_url])
        return {'description': description}

    @auth_required(['admin', 'advertiser'])
    async def identify_product_name(self, data):
        """
        根据提供的图片URL识别产品名称。
        """
        image_url = data.get('image_url')

        # 输入验证
        if not image_url:
            raise MException("image_url 不能为空")

        if not isinstance(image_url, str) or not image_url.strip():
            raise MException("image_url 必须是有效的字符串")

        product_name = await product_name_recog([image_url])
        return {'product_name': product_name}

    @auth_required(['admin', 'advertiser'])
    async def identify_and_save_domains(self, data):
        """
        根据提供的图片URL识别产品领域，并将新领域保存到数据字典。
        """
        image_url = data.get('image_url')

        # 输入验证
        if not image_url:
            raise MException("image_url 不能为空")

        if not isinstance(image_url, str) or not image_url.strip():
            raise MException("image_url 必须是有效的字符串")

        # 1. 获取现有领域 (从DataDictionary中 category 为 'domain' 的记录)
        existing_domain_docs = await DataDictionary.find(DataDictionary.category == 'domain').to_list()
        existing_domains = [doc.key for doc in existing_domain_docs]

        # 2. 调用 Agent 识别领域
        identified_domains = await product_domain_recog([image_url], existing_domains)

        # 3. 保存新识别出的领域到 DataDictionary
        newly_added_domains = []
        for domain in identified_domains:
            # 检查领域是否已存在
            existing_entry = await DataDictionary.find_one(DataDictionary.category == 'domain',
                                                           DataDictionary.key == domain)
            if not existing_entry:
                new_domain = DataDictionary(
                    category='domain',
                    key=domain,
                    value=domain  # 将领域名同时作为 key 和 value 存储
                )
                await new_domain.insert()
                newly_added_domains.append(domain)

        # 只返回识别出的领域
        return {'identified_domains': identified_domains}
