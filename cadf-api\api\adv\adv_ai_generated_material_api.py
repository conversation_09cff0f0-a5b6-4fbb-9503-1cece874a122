from typing import Any, Dict

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import AiGeneratedMaterial
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set


@register_handler('adv_ai_generated_material_api')
class AdvAiGeneratedMaterialApi:

    @auth_required(['admin', 'advertiser'])
    async def regenerate_failed_materials(self, data: Dict[str, Any]) -> None:
        ai_generation_task_id = data.get('ai_generation_task_id')
        user_id = data.get('user_id')

        # 输入验证
        if not ai_generation_task_id:
            raise MException("ai_generation_task_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        # 查找所有失败的素材
        failed_materials = await AiGeneratedMaterial.find({
            "ai_generation_task_id": ai_generation_task_id,
            "user_id": user_id,
            "is_deleted": False,
            "$or": [
                {"image_generation_status": "失败"},
                {"text_generation_status": "失败"}
            ]
        }).to_list()

        if not failed_materials:
            olog.info("没有找到失败的素材")
            return

        # 重置失败素材的状态
        update_count = 0
        image_regenerate_ids = []
        text_regenerate_ids = []
        
        for material in failed_materials:
            update_fields = {}
            
            # 如果图片生成失败，重置为待生成
            if material.image_generation_status == "失败":
                update_fields["image_generation_status"] = "待生成"
                image_regenerate_ids.append(str(material.id))
            
            # 如果文本生成失败，重置为待生成
            if material.text_generation_status == "失败":
                update_fields["text_generation_status"] = "待生成"
                text_regenerate_ids.append(str(material.id))
            
            # 清除失败原因
            if material.generation_fail_reason:
                update_fields["generation_fail_reason"] = None
            
            if update_fields:
                await material.update({"$set": update_fields})
                update_count += 1

        # 发送消息到Redis
        if image_regenerate_ids:
            redis_image_set_key = RedisKeyConfig.XHS_IMAGE_GEN_SET
            image_result = await publish_messages_to_redis_set(
                redis_image_set_key, [{"id_": material_id} for material_id in image_regenerate_ids]
            )
            if image_result is not None:
                olog.info(f"成功向 Redis Set '{redis_image_set_key}' 添加了 {image_result} 个图片重新生成任务 ID")

        if text_regenerate_ids:
            redis_text_set_key = RedisKeyConfig.XHS_TEXT_GEN_SET
            text_result = await publish_messages_to_redis_set(
                redis_text_set_key, [{"id_": material_id} for material_id in text_regenerate_ids]
            )
            if text_result is not None:
                olog.info(f"成功向 Redis Set '{redis_text_set_key}' 添加了 {text_result} 个文本重新生成任务 ID")

        olog.info(f"成功重置 {update_count} 个失败素材的状态，总失败素材数: {len(failed_materials)}")


    @auth_required(['admin', 'advertiser'])
    async def regenerate_image(self, data: Dict[str, Any]) -> None:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        # 查找指定的素材
        material = await AiGeneratedMaterial.find_one(
            AiGeneratedMaterial.id == PydanticObjectId(id_),
            AiGeneratedMaterial.user_id == user_id,
            AiGeneratedMaterial.is_deleted == False
        )

        if not material:
            raise MException("未找到指定的 AI 生成素材")

        # 只重置图片生成相关的状态
        update_fields = {}
        
        # 重置图片生成状态为待生成
        if hasattr(material, 'image_generation_status'):
            update_fields["image_generation_status"] = "待生成"
        
        # 清除已生成的图片内容
        if hasattr(material, 'images') and material.images:
            update_fields["images"] = []
        
        # 清除失败原因
        if hasattr(material, 'generation_fail_reason') and material.generation_fail_reason:
            update_fields["generation_fail_reason"] = None

        # 更新素材状态
        if update_fields:
            await material.update({"$set": update_fields})

        # 发送消息到Redis
        material_id = str(material.id)
        redis_image_set_key = RedisKeyConfig.XHS_IMAGE_GEN_SET
        image_result = await publish_messages_to_redis_set(
            redis_image_set_key, [{"id_": material_id}]
        )
        if image_result is not None:
            olog.info(f"成功向 Redis Set '{redis_image_set_key}' 添加了图片重新生成任务 ID: {material_id}")

        olog.info(f"图片重新生成请求已提交，素材ID: {material_id}，状态: 待生成")

    @auth_required(['admin', 'advertiser'])
    async def regenerate_text(self, data: Dict[str, Any]) -> None:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        # 查找指定的素材
        material = await AiGeneratedMaterial.find_one(
            AiGeneratedMaterial.id == PydanticObjectId(id_),
            AiGeneratedMaterial.user_id == user_id,
            AiGeneratedMaterial.is_deleted == False
        )

        if not material:
            raise MException("未找到指定的 AI 生成素材")

        # 只重置文案生成相关的状态
        update_fields = {}
        
        # 重置文本生成状态为待生成
        if hasattr(material, 'text_generation_status'):
            update_fields["text_generation_status"] = "待生成"
        
        # 清除已生成的文案内容
        if hasattr(material, 'content') and material.content:
            update_fields["content"] = ""
        
        if hasattr(material, 'title') and material.title:
            update_fields["title"] = ""
        
        # 清除失败原因
        if hasattr(material, 'generation_fail_reason') and material.generation_fail_reason:
            update_fields["generation_fail_reason"] = None

        # 更新素材状态
        if update_fields:
            await material.update({"$set": update_fields})

        # 发送消息到Redis
        material_id = str(material.id)
        redis_text_set_key = RedisKeyConfig.XHS_TEXT_GEN_SET
        text_result = await publish_messages_to_redis_set(
            redis_text_set_key, [{"id_": material_id}]
        )
        if text_result is not None:
            olog.info(f"成功向 Redis Set '{redis_text_set_key}' 添加了文本重新生成任务 ID: {material_id}")

        olog.info(f"文案重新生成请求已提交，素材ID: {material_id}，状态: 待生成")
    
    @auth_required(['admin', 'advertiser'])
    async def delete(self, data: Dict[str, Any]) -> None:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        material = await AiGeneratedMaterial.find_one(
            AiGeneratedMaterial.id == PydanticObjectId(id_),
            AiGeneratedMaterial.user_id == user_id
        )
        if material:
            await material.update({"$set": {"is_deleted": True}})

    @auth_required(['admin', 'advertiser'])
    async def query_by_task_id(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        ai_generation_task_id = data.get('ai_generation_task_id')
        user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        image_generation_status = data.get('image_generation_status')
        text_generation_status = data.get('text_generation_status')

        match_stage = {
            'ai_generation_task_id': ai_generation_task_id,
            'user_id': user_id,
            'is_deleted': False
        }

        # 添加状态筛选条件
        if image_generation_status:
            match_stage['image_generation_status'] = image_generation_status
        if text_generation_status:
            match_stage['text_generation_status'] = text_generation_status

        pipeline = [
            {"$match": match_stage},
            {"$sort": {"create_at": -1}},
            {
                "$facet": {
                    "metadata": [{"$count": "total_count"}],
                    "results": [{"$skip": (page - 1) * page_size}, {"$limit": page_size}],
                }
            },
        ]

        aggregation_result = await AiGeneratedMaterial.aggregate(pipeline).to_list()

        if not aggregation_result or not aggregation_result[0].get("metadata"):
            materials_list = []
            total = 0
        else:
            materials_list = aggregation_result[0]["results"]
            total = aggregation_result[0]["metadata"][0]["total_count"] if aggregation_result[0]["metadata"] else 0

        # 转换_id字段并添加签名 URL
        for material_dict in materials_list:
            # 转换_id字段
            material_dict['_id'] = str(material_dict['_id'])
            material_dict.pop('id', None)

            if 'images' in material_dict and isinstance(material_dict['images'], list):
                # 按 order 排序
                material_dict['images'].sort(key=lambda img: img.get('order', 0))
                for image_info in material_dict['images']:
                    if 'oss_key' in image_info and image_info['oss_key']:
                        # 生成签名 URL，有效时间默认1小时
                        image_info['signed_url'] = await oss_client.signed_get_url(image_info['oss_key'])
                    else:
                        image_info['signed_url'] = None  # oss_key 不存在或为空

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=materials_list
        )

    @auth_required(['admin', 'advertiser'])
    async def query_by_id(self, data: Dict[str, Any]) -> Dict[str, Any]:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")

        material = await AiGeneratedMaterial.find_one(
            AiGeneratedMaterial.id == PydanticObjectId(id_),
            AiGeneratedMaterial.user_id == user_id,
            AiGeneratedMaterial.is_deleted == False
        )

        if not material:
            raise MException("未找到指定的 AI 生成素材")

        material_dict = material.to_dict()
        if 'images' in material_dict and isinstance(material_dict['images'], list):
            material_dict['images'].sort(key=lambda img: img.get('order', 0))
            for image_info in material_dict['images']:
                if 'oss_key' in image_info and image_info['oss_key']:
                    image_info['signed_url'] = await oss_client.signed_get_url(image_info['oss_key'])
                else:
                    image_info['signed_url'] = None

        return material_dict


    
