"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
    Alert, Avatar, Box, Button, Chip, CircularProgress, Container, 
    Divider, Grid, LinearProgress, Pagination, Paper, Stack, Typography, 
    useMediaQuery, useTheme, Card, CardContent, CardActionArea, IconButton
} from '@mui/material';
import { 
    Clock, AlertTriangle, ChevronLeft, Home, 
    Tag, Monitor, Calendar, Package, RefreshCw
} from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useSnackbar } from 'notistack';
import NextLink from 'next/link';


function TaskMarketContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const searchParams = useSearchParams();
    const { enqueueSnackbar } = useSnackbar();

    const accountIdFromQuery = searchParams.get('accountId');
    const pageFromQuery = parseInt(searchParams.get('page') || '1', 10);

    const [tasksState, setTasksState] = useState({ tasks: [], totalCount: 0, loading: true, error: null });
    const [currentPage, setCurrentPage] = useState(pageFromQuery);
    const itemsPerPage = 8;

    useEffect(() => {
        setCurrentPage(parseInt(searchParams.get('page') || '1', 10));
    }, [searchParams]);

    useEffect(() => {
        if (accountIdFromQuery) {
            const fetchMarketTasks = async () => {
                setTasksState(prev => ({ ...prev, loading: true, error: null }));
                try {
                    const response = await pubPromotionTaskApi.queryMarketTasks(accountIdFromQuery, Math.max(1, currentPage), itemsPerPage);
                    if (response && response.results) {
                        let fetchedTasks = response.results.map(task => ({
                            ...task,
                            imageUrls: task.image_url ? [task.image_url] : ['https://placehold.co/300x400/e0f2fe/0c4a6e?text=Task'],
                        }));
                        setTasksState({ tasks: fetchedTasks, totalCount: response.total || 0, loading: false, error: null });
                    } else {
                        setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的任务市场数据' });
                    }
                } catch (error) {
                    console.error(`获取任务市场列表失败:`, error);
                    const errorMsg = `获取任务列表失败: ${error.message || '未知错误'}`;
                    setTasksState({ tasks: [], totalCount: 0, loading: false, error: errorMsg });
                    enqueueSnackbar(errorMsg, { variant: 'error' });
                }
            };
            fetchMarketTasks();
        } else {
            setTasksState({ tasks: [], totalCount: 0, loading: false, error: null });
        }
    }, [accountIdFromQuery, currentPage]);

    const handlePageChange = (event, value) => {
        setCurrentPage(value);
        const currentQuery = new URLSearchParams(searchParams.toString());
        currentQuery.set('page', value);
        router.push(`${router.pathname}?${currentQuery.toString()}`, { scroll: false });
    };

    const handleAcceptTaskAndNavigate = async (task) => {
        if (!accountIdFromQuery) {
            enqueueSnackbar("请先选择一个账户来接受任务。", { variant: 'error' });
            return;
        }

        try {
            await pubPromotionTaskApi.acceptTask(task._id, accountIdFromQuery);
            enqueueSnackbar("任务接取成功！正在跳转到任务详情...", { variant: 'success' });

            router.push(`/protected/tasks/detail/${task._id}`);

        } catch (error) {
            console.error("接取任务失败:", error);
            enqueueSnackbar(`接取任务失败: ${error.message || '未知错误'}`, { variant: 'error' });
        }
    };

    const handleRefresh = () => {
        if (accountIdFromQuery) {
            setTasksState(prev => ({ ...prev, loading: true, error: null }));
            // 重新触发 fetchMarketTasks
            const fetchMarketTasks = async () => {
                try {
                    const response = await pubPromotionTaskApi.queryMarketTasks(accountIdFromQuery, Math.max(1, currentPage), itemsPerPage);
                    if (response && response.results) {
                        let fetchedTasks = response.results.map(task => ({
                            ...task,
                            imageUrls: task.image_url ? [task.image_url] : ['https://placehold.co/300x400/e0f2fe/0c4a6e?text=Task'],
                        }));
                        setTasksState({ tasks: fetchedTasks, totalCount: response.total || 0, loading: false, error: null });
                    } else {
                        setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的任务市场数据' });
                    }
                } catch (error) {
                    console.error(`获取任务市场列表失败:`, error);
                    const errorMsg = `获取任务列表失败: ${error.message || '未知错误'}`;
                    setTasksState({ tasks: [], totalCount: 0, loading: false, error: errorMsg });
                    enqueueSnackbar(errorMsg, { variant: 'error' });
                }
            };
            fetchMarketTasks();
        }
    };

    let pageTitle = "任务市场";
    let pageSubtitle = "浏览并接受感兴趣的任务";
    if (accountIdFromQuery) {
        pageSubtitle = "选择合适的任务并接受";
    } else {
        pageSubtitle = "请选择一个账户以查看任务市场";
    }

    return (
        <Container maxWidth="xl" sx={{ py: 4 }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <IconButton onClick={() => router.back()} sx={{ display: { xs: 'inline-flex', sm: 'inline-flex' } }}>
                    <ChevronLeft />
                </IconButton>
                <Typography variant="caption" color="text.secondary" onClick={() => router.back()} sx={{cursor: 'pointer', '&:hover': {textDecoration: 'underline'}}}>
                    返回任务中心
                </Typography>
            </Stack>
            
            <Box sx={{ mb: 4 }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={12}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box>
                                <Typography variant={isMobile ? "h5" : "h4"} component="h1" sx={{ fontWeight: 600 }}>
                                    {pageTitle}
                                </Typography>
                                <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                                    {pageSubtitle}
                                </Typography>
                            </Box>
                            <IconButton 
                                onClick={handleRefresh} 
                                disabled={!accountIdFromQuery || tasksState.loading}
                                sx={{ 
                                    bgcolor: 'primary.main',
                                    color: 'white',
                                    '&:hover': { bgcolor: 'primary.dark' },
                                    '&:disabled': { bgcolor: 'action.disabledBackground' }
                                }}
                            >
                                <RefreshCw size={20} />
                            </IconButton>
                        </Box>
                    </Grid>
                </Grid>
            </Box>
            
            {(tasksState.loading) && (
                <LinearProgress sx={{ mb: 3, borderRadius: 1 }} />
            )}
            
            {tasksState.error && (
                <Alert 
                    severity="error" 
                    variant="outlined"
                    sx={{ 
                        mb: 3, 
                        borderRadius: 2 
                    }}
                >
                    {tasksState.error}
                </Alert>
            )}
            
            {!tasksState.loading && !tasksState.error && tasksState.tasks.length === 0 && (
                <Paper 
                    elevation={0} 
                    sx={{
                        p: 5,
                        textAlign: 'center',
                        borderRadius: 2,
                        backgroundColor: theme.palette.background.default,
                        border: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <AlertTriangle size={48} style={{ margin: '0 auto 16px', color: theme.palette.text.secondary }} />
                    <Typography variant="h6" gutterBottom>
                        {accountIdFromQuery ? "暂无可展示的任务" : "请选择账户以浏览任务"}
                    </Typography>
                    <Typography color="text.secondary" sx={{ maxWidth: 500, mx: 'auto' }}>
                        {accountIdFromQuery 
                            ? (tasksState.totalCount > 0 
                                ? `当前条件下共有 ${tasksState.totalCount} 个候选任务。` 
                                : "当前没有符合条件的任务可供接取，请稍后再试。")
                            : "选择一个账户后，这里将展示为您推荐的任务。"}
                    </Typography>
                </Paper>
            )}

            {!tasksState.loading && !tasksState.error && tasksState.tasks.length > 0 && (
                <>
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" color="text.secondary">
                            共找到 {tasksState.totalCount} 个任务
                        </Typography>
                    </Box>
                    
                    <Grid container spacing={3}>
                        {tasksState.tasks.map((task) => (
                            <Grid key={task._id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                                <Card 
                                    sx={{ 
                                        height: '100%', 
                                        display: 'flex', 
                                        flexDirection: 'column',
                                        borderRadius: 2,
                                        boxShadow: 1,
                                        transition: 'all 0.2s ease-in-out',
                                        '&:hover': {
                                            boxShadow: 3,
                                            transform: 'translateY(-2px)'
                                        }
                                    }}
                                >
                                    <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                                        {/* 任务标题和图片 */}
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                            <Avatar 
                                                src={task.imageUrls && task.imageUrls.length > 0 && !task.imageUrls[0].includes('placehold.co') 
                                                    ? task.imageUrls[0] 
                                                    : undefined}
                                                sx={{ 
                                                    width: 48, 
                                                    height: 48,
                                                    backgroundColor: theme.palette.primary.light,
                                                    color: theme.palette.primary.main
                                                }}
                                            >
                                                {!task.imageUrls || task.imageUrls.length === 0 || task.imageUrls[0].includes('placehold.co') 
                                                    ? <Package size={24} /> 
                                                    : null}
                                            </Avatar>
                                            <Typography 
                                                variant="h6" 
                                                sx={{ 
                                                    fontWeight: 600,
                                                    fontSize: '1.1rem',
                                                    lineHeight: 1.3,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    flex: 1
                                                }}
                                            >
                                                {task.title || '未命名任务'}
                                            </Typography>
                                        </Box>

                                        {/* 主要信息：领域、平台、截止时间 */}
                                        <Stack spacing={1.5} sx={{ mb: 2 }}>
                                            {/* 任务领域 - 最重要 */}
                                            {task.domain && task.domain.length > 0 && (
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                                                    <Tag size={16} style={{ color: theme.palette.primary.main }} />
                                                    {Array.isArray(task.domain) ? (
                                                        task.domain.map((domain, index) => (
                                                            <Chip
                                                                key={index}
                                                                label={domain}
                                                                size="small"
                                                                sx={{ 
                                                                    backgroundColor: theme.palette.primary.light,
                                                                    color: theme.palette.primary.main,
                                                                    fontWeight: 600,
                                                                    fontSize: '0.75rem',
                                                                    height: 24
                                                                }}
                                                            />
                                                        ))
                                                    ) : (
                                                        <Typography 
                                                            variant="body1" 
                                                            sx={{ 
                                                                fontWeight: 600,
                                                                color: theme.palette.primary.main,
                                                                fontSize: '0.95rem'
                                                            }}
                                                        >
                                                            {task.domain}
                                                        </Typography>
                                                    )}
                                                </Box>
                                            )}

                                            {/* 任务平台 - 次重要 */}
                                            {task.platform && (
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Monitor size={16} style={{ color: theme.palette.text.secondary }} />
                                                    <Typography 
                                                        variant="body2" 
                                                        sx={{ 
                                                            fontWeight: 500,
                                                            color: theme.palette.text.primary,
                                                            fontSize: '0.9rem'
                                                        }}
                                                    >
                                                        {task.platform}
                                                    </Typography>
                                                </Box>
                                            )}

                                            {/* 截止时间 - 重要 */}
                                            {task.deadline && task.deadline !== "N/A" && (
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Clock size={16} style={{ color: theme.palette.error.main }} />
                                                    <Typography 
                                                        variant="body2" 
                                                        sx={{ 
                                                            fontWeight: 500,
                                                            color: theme.palette.error.main,
                                                            fontSize: '0.9rem'
                                                        }}
                                                    >
                                                        {task.deadline}
                                                    </Typography>
                                                </Box>
                                            )}
                                        </Stack>

                                        {/* 任务描述 - 弱化 */}
                                        {task.content && (
                                            <Typography 
                                                variant="body2" 
                                                color="text.secondary" 
                                                sx={{ 
                                                    mb: 2,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    lineHeight: 1.4,
                                                    fontSize: '0.85rem',
                                                    opacity: 0.8
                                                }}
                                            >
                                                {task.content}
                                            </Typography>
                                        )}

                                        {/* 接受任务按钮 */}
                                        <Box sx={{ mt: 'auto' }}>
                                            <Button 
                                                variant="contained" 
                                                fullWidth 
                                                disableElevation
                                                onClick={(e) => { 
                                                    e.stopPropagation(); 
                                                    handleAcceptTaskAndNavigate(task); 
                                                }} 
                                                sx={{ 
                                                    borderRadius: 1.5,
                                                    textTransform: 'none',
                                                    fontWeight: 500,
                                                    py: 1.2
                                                }}
                                            >
                                                接受任务
                                            </Button>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                    
                    {tasksState.totalCount > itemsPerPage && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                            <Pagination
                                count={Math.ceil(tasksState.totalCount / itemsPerPage)}
                                page={currentPage}
                                onChange={handlePageChange}
                                color="primary"
                                shape="rounded"
                                showFirstButton
                                showLastButton
                            />
                        </Box>
                    )}
                </>
            )}
        </Container>
    );
}

export default function TaskMarketPage() {
    return (
        <Suspense fallback={
            <Container maxWidth="xl" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
                <CircularProgress />
            </Container>
        }>
            <TaskMarketContent />
        </Suspense>
    );
} 