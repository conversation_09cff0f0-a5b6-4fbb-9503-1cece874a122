from typing import Dict, Any

from agent.state import OverallState
from omni.llm.output_agent import text_output_handler
from omni.log.log import olog


async def identify_elements(state: OverallState) -> Dict[str, Any]:
    """识别图片元素节点"""
    olog.info("开始执行图片元素识别节点")
    
    image_path = state.scene_image_path
    
    prompt_template = """
# 角色
你是一名专业的图片识别和分析专家，精通识别图片中的所有元素和细节。

# 任务
详细分析并识别图片中的所有关键元素、特征和细节，尽可能全面和准确。
忽略图片上文字，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素。

# 返回值
```
# 人物特征
如果有人物,请详细描述人物的以下特征：
- 发色（如黑色、金色、棕色等）
- 发型（如长发、短发、卷发、直发等）
- 肤色（如白皙、古铜色、深色等）
- 种族: 中国人
- 性别
- 年龄段（如儿童、青年、成年人、老年人）
- 表情（如微笑、严肃、愤怒等）
- 姿势和动作（如站立、坐着、跑步、跳跃等）
- 穿着风格（如休闲、正式、运动、时尚等）
- 服装颜色和类型（如红色T恤、蓝色西装等）
- 其他显著特征（如有无眼镜、胡须、纹身、配饰等）

# 物体识别
- 主要物体（列出画面中的主要物体）
- 物体颜色和材质
- 物体位置和排列
- 特殊物品或标志性元素

# 场景特征
- 场景类型（如室内、室外、自然风光、城市街道等）
- 环境描述（如海滩、山脉、森林、办公室、客厅等）
- 时间（如白天、黄昏、夜晚等）
- 天气状况（如晴朗、多云、雨天等）
- 光线条件（如明亮、昏暗、逆光、侧光等）

# 美学和视觉元素
- 主色调（画面的主要颜色）
- 构图特点（如对称、三分法、主体突出等）
- 视角（如平视、俯视、仰视等）
- 质感和纹理（如光滑、粗糙、金属质感等）
- 景深和焦点

# 其他重要细节
- 特殊效果（如景深模糊、运动模糊等）
- 独特或不寻常的元素
- 图片整体氛围和风格（如正式、休闲、艺术、写实等）
```
"""

    raw_result = await text_output_handler(
        prompt_template=prompt_template,
        params={},
        llm_name="QWEN_VL_MAX",
        tags=["identify_elements"],
        image_path=image_path
    )
    
    olog.info("图片元素识别完成")
    
    return {
        "image_elements": raw_result
    }