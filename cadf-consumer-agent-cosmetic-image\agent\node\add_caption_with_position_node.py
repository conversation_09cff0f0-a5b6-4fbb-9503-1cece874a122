from typing import Dict, Any
import random
import asyncio

from agent.utils.image_text_styler import ImageTextStyler
from agent.utils.temp_file_manager import save_temp_pic
from agent.state import OverallState
from omni.log.log import olog


async def add_caption_with_position(state: OverallState) -> Dict[str, Any]:
    """确定文字位置并添加配文到图片节点"""
    olog.info("开始执行配文位置确定和添加节点")
    
    image_path = state.processed_image_path
    caption_items = state.caption_items
    
    # 限制配文数量为3个，并确保横向不重叠（同一行左右不能同时选中）
    n = len(caption_items)
    
    if n > 3:
        olog.warning(f"配文数量({n})超过限制数量(3)，将只使用前3个配文")
        caption_items = caption_items[:3]
        n = 3
    
    # 定义横向不重叠的位置组合
    all_positions = ["左上", "右上", "左中", "右中", "左下", "右下"]
    
    # 根据配文数量选择位置，确保横向不重叠
    if n == 1:
        text_positions = [random.choice(all_positions)]
    elif n == 2:
        # 2个配文时，确保不在同一行
        available_combinations = [
            ["左上", "左中"], ["左上", "左下"], ["左上", "右中"],
            ["右上", "左中"], ["右上", "左下"], ["右上", "右中"],
            ["左中", "左下"], ["左中", "右下"],
            ["右中", "左下"], ["右中", "右下"]
        ]
        text_positions = random.choice(available_combinations)
    else:  # n == 3
        # 3个配文时，确保不在同一行
        available_combinations = [
            ["左上", "左中", "左下"], ["左上", "左中", "右下"], ["左上", "右中", "左下"], ["左上", "右中", "右下"],
            ["右上", "左中", "左下"], ["右上", "左中", "右下"], ["右上", "左下", "右中"], ["右上", "右中", "右下"],
            ["左中", "左下", "右下"]
        ]
        text_positions = random.choice(available_combinations)
    olog.debug(f"文字位置确定完成: {text_positions}")
    
    async def read_image_file(path: str) -> bytes:
        def _read():
            with open(path, "rb") as f:
                return f.read()
        return await asyncio.to_thread(_read)
    
    image_bytes = await read_image_file(image_path)
    styler = ImageTextStyler()
    
    for caption, position in zip(caption_items, text_positions):
        olog.debug(f"添加配文到位置: {position}")
        result_bytes = await styler.apply_style(
            image_bytes=image_bytes,
            title=caption.get('title', ''),
            bodies=caption.get('contents', []),
            position=position
        )
        if result_bytes and len(result_bytes) > 0:
            image_bytes = result_bytes
            olog.debug(f"成功添加配文到位置: {position}, 图片大小: {len(image_bytes)} bytes")
        else:
            olog.warning(f"位置 {position} 的配文添加失败，跳过此配文")
    
    if not image_bytes or len(image_bytes) == 0:
        olog.error("最终图片数据为空，无法保存文件")
        raise ValueError("图片处理失败，无法生成最终图片")
    
    temp_path = await save_temp_pic(image_bytes)
    
    olog.info(f"配文添加完成，保存到: {temp_path}")
    return {"final_image_path": temp_path}

