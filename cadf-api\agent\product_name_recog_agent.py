from omni.llm.output_agent import text_output_handler
from agent.image_tools import download_image_to_temp

PROMPT = """
# 角色
你是一名图像识别专家。

# 背景
你收到了多张关于某个产品或服务的图片。

# 任务
根据输入的多张图片内容，识别图片中展示的产品或服务的**具体名称**。

# 约束
1.  整合所有图片的信息进行识别。
2.  如果图片中没有明确的产品名称，请根据产品的外观和特征进行推断。
3.  只返回产品名称，不要包含其他描述性文字。

# 返回值
返回识别出的产品名称文本。
"""


async def product_name_recog(image_urls: list[str]) -> str:
    image_path = download_image_to_temp(image_urls[0])

    result = await text_output_handler(
        prompt_template=PROMPT,
        params={},
        llm_name="QWEN_VL_MAX",
        tags=["product_name_recognition"],
        image_path=image_path
    )
    return result.strip()
