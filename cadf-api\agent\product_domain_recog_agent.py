from typing import List

from pydantic import BaseModel, Field

from omni.llm.output_agent import structured_output_handler
from agent.image_tools import download_image_to_temp

PROMPT = """
# 角色
你是一名专业的图片分析师和行业专家。

# 背景
系统中已有的领域列表如下，请在识别时参考：
{existing_domains_str}

# 任务
根据输入的多张图片内容，识别所有图片中展示的产品或服务可能属于的领域。

# 约束
1. 综合分析所有图片，识别产品或服务所属的所有唯一领域。
2. 优先使用或参考系统已有的相似领域 (`{existing_domains_str}`)，若无相关领域则生成新的领域名称。
3. 返回包含所有可能的领域。

# 返回值
{format_instructions}
"""


class DomainList(BaseModel):
    domains: List[str] = Field(description="所有图片中识别出的唯一领域列表，参考了系统已有领域")


async def product_domain_recog(image_urls: list[str], existing_domains: List[str]) -> List[str]:
    existing_domains_str = "\n".join([f"- {domain}" for domain in existing_domains]) if existing_domains else "无"
    image_path = download_image_to_temp(image_urls[0])

    result: DomainList = await structured_output_handler(
        prompt_template=PROMPT,
        params={"existing_domains_str": existing_domains_str},
        output_model=DomainList,
        llm_name="QWEN_VL_MAX",
        tags=["product_domain_recognition"],
        image_path=image_path
    )
    return result.domains
