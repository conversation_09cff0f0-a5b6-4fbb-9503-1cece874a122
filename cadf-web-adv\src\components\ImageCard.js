import React from 'react';
import {Box, Card, CardActionArea, CardMedia} from '@mui/material';
import {useTheme} from '@mui/material/styles';

const ImageCard = ({
                         image,  // 接收单张图片URL
                         onClick,
                         children,
                         coverOverlay // 新增：可选的遮罩层内容
                     }) => {
    const theme = useTheme();
    
    // 默认值
    const defaultImage = 'https://placehold.co/300x400/e0f2fe/0c4a6e?text=Product';

    // 确定要显示的图片
    const imageToShow = image || defaultImage;

    return (
        <Card sx={{
            width: '100%', 
            height: '100%', 
            boxShadow: theme.shadows[1], 
            borderRadius: 3, 
            display: 'flex', 
            flexDirection: 'column', 
            position: 'relative',
            border: `1px solid ${theme.palette.grey[200]}`,
            bgcolor: theme.palette.background.paper,
            transition: 'all 0.3s ease',
            '&:hover': {
                boxShadow: theme.shadows[4],
                transform: 'translateY(-2px)',
                borderColor: theme.palette.primary.light,
            }
        }}>
            {/* 新增：遮罩层，zIndex最高，覆盖整个卡片 */}
            {coverOverlay && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        zIndex: 20,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 3,
                        pointerEvents: 'none', // 避免遮罩影响点击
                    }}
                >
                    {coverOverlay}
                </Box>
            )}
            {/* 图片部分使用CardActionArea */}
            <CardActionArea 
                onClick={onClick} 
                sx={{
                    position: 'relative',
                    '&:hover .MuiCardActionArea-focusHighlight': {
                        opacity: 0.04,
                    }
                }}
            >
                <Box sx={{
                    width: '100%',
                    paddingTop: '133%', // 1:1的宽高比
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: '12px 12px 0 0',
                }}>
                    <CardMedia
                        component="img"
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            transition: 'transform 0.3s ease',
                        }}
                        image={imageToShow}
                        alt="Product"
                    />
                </Box>
            </CardActionArea>

            {/* 内容部分不使用CardActionArea，避免button嵌套 */}
            <Box
                sx={{
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer',
                    p: 0,
                    transition: 'background-color 0.2s ease',
                    '&:hover': {
                        bgcolor: theme.palette.grey[50],
                    }
                }}
                onClick={onClick}
            >
                {children}
            </Box>
        </Card>
    );
};

export default ImageCard;