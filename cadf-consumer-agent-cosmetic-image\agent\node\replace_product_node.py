import asyncio
from typing import Dict, Any

from agent.utils.openai_image_edit import OpenaiImageEditor
from agent.state import OverallState
from agent.utils.temp_file_manager import save_temp_pic
from omni.log.log import olog


async def replace_product(state: OverallState) -> Dict[str, Any]:
    """替换图片中的产品节点"""
    olog.info("开始执行产品替换节点")
    
    
    scene_placeholder = state.scene_product_info.get("product_type", "")
    product_placeholder = state.product_info.get("product_type", "")
    
    scene_image_bytes = await asyncio.to_thread(
        lambda: open(state.scene_image_path, "rb").read()
    )
    product_image_bytes = await asyncio.to_thread(
        lambda: open(state.product_image_path, "rb").read()
    )
    
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    prompt = f"""
# 任务说明
1. 将第一张图中的所有非产品固有的营销文字、标语、标签等移除，确保最终场景干净，仅保留自然元素。
2. 将第一张图中的 `{scene_placeholder}` 替换为第二张图中的 `{product_placeholder}`。
3. 对产品固有的品牌标志、型号、成分说明等文字图案进行逼真的失焦模糊处理（类似轻微失焦或动态模糊），使其轮廓可见但细节不清。禁止使用涂抹、打码或粗暴覆盖的方式。

# 注意事项
- 确保替换后的产品与场景自然融合，避免生硬的边缘或违和感。
- 模糊处理需保持真实感，避免过度模糊导致信息完全丢失。
- 最终生成的图片应保持高分辨率，无明显人工处理痕迹。
"""
    
    edited_image_data, error_type = await editor.generate_image_from_image(
        image_bytes_list=[scene_image_bytes, product_image_bytes], 
        prompt=prompt
    )

    temp_path = await save_temp_pic(edited_image_data)
    
    olog.info(f"产品替换完成，保存到: {temp_path}")
    return {"processed_image_path": temp_path}