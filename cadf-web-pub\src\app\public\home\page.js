"use client"

import {<PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, Container, Di<PERSON>r, Grid, Stack, Typography, useMediaQuery, useTheme, Chip} from '@mui/material';
import {TrendingUp, DollarSign, Users, BarChart3, Zap, Monitor, Download, Target, ArrowRight, Calendar, Globe} from 'lucide-react';
import Cookies from "js-cookie";
import {useRouter} from 'next/navigation';
import {useEffect, useState} from 'react';
import {DASHBOARD_PATH, DOWNLOAD_PATH} from "@/config";

export default function Home() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const [animatedNumbers, setAnimatedNumbers] = useState({
        revenue: 0,
        users: 0,
        clicks: 0
    });

    useEffect(() => {
        const token = Cookies.get("access_token");
        if (token) {
            router.push(DASHBOARD_PATH);
        }
    }, [router]);

    useEffect(() => {
        const animateNumbers = () => {
            const targets = { revenue: 158.6, users: 50000, clicks: 1.2 };
            const duration = 2000;
            const steps = 60;
            const stepDuration = duration / steps;

            let currentStep = 0;
            const interval = setInterval(() => {
                currentStep++;
                const progress = currentStep / steps;
                const easeOut = 1 - Math.pow(1 - progress, 3);

                setAnimatedNumbers({
                    revenue: +(targets.revenue * easeOut).toFixed(1),
                    users: Math.floor(targets.users * easeOut),
                    clicks: +(targets.clicks * easeOut).toFixed(1)
                });

                if (currentStep >= steps) {
                    clearInterval(interval);
                }
            }, stepDuration);
        };

        const timer = setTimeout(animateNumbers, 500);
        return () => clearTimeout(timer);
    }, []);

    return (
        <Box sx={{minHeight: '100vh', bgcolor: 'background.default'}}>
            {/* 英雄区域 */}
            <Box
                sx={{
                    bgcolor: 'primary.main',
                    py: {xs: 8, sm: 10, md: 16},
                    position: 'relative'
                }}
            >
                <Container maxWidth="lg">
                    <Grid container spacing={{xs: 4, md: 8}} alignItems="center">
                        <Grid size={{xs: 12, md: 6}}>
                            <Box sx={{textAlign: {xs: 'center', md: 'left'}}}>
                                <Chip
                                    label="流量变现专家"
                                    size="medium"
                                    sx={{
                                        bgcolor: 'primary.light',
                                        color: 'primary.contrastText',
                                        mb: 3,
                                        fontWeight: 500,
                                        border: 'none'
                                    }}
                                />
                                <Typography
                                    variant="h1"
                                    component="h1"
                                    sx={{
                                        fontSize: {xs: '2.25rem', sm: '2.75rem', md: '3.5rem'},
                                        fontWeight: 600,
                                        color: 'primary.contrastText',
                                        mb: 2,
                                        lineHeight: 1.1,
                                        letterSpacing: '-0.01em'
                                    }}
                                >
                                    让流量
                                    <Box component="span" sx={{color: 'secondary.main'}}> 创造价值</Box>
                                </Typography>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', sm: '1.125rem', md: '1.25rem'},
                                        color: 'primary.contrastText',
                                        opacity: 0.9,
                                        mb: 4,
                                        lineHeight: 1.5,
                                        maxWidth: '540px',
                                        mx: {xs: 'auto', md: 0},
                                        fontWeight: 400
                                    }}
                                >
                                    专业的流量主平台，智能广告匹配，透明收益结算，让您的每一次点击都变成真金白银
                                </Typography>
                                <Stack
                                    direction={{xs: 'column', sm: 'row'}}
                                    spacing={2}
                                    sx={{justifyContent: {xs: 'center', md: 'flex-start'}}}
                                >
                                    <Button
                                        variant="contained"
                                        size="large"
                                        endIcon={<ArrowRight size={18}/>}
                                        href="/public/login"
                                        sx={{
                                            bgcolor: 'background.paper',
                                            color: 'primary.main',
                                            px: 3,
                                            py: 1.5,
                                            fontWeight: 600,
                                            fontSize: '1rem',
                                            borderRadius: 2,
                                            boxShadow: 'none',
                                            '&:hover': {
                                                bgcolor: 'grey.100',
                                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                            },
                                            transition: 'all 0.2s ease'
                                        }}
                                    >
                                        立即开始赚钱
                                    </Button>
                                    <Button
                                        variant="outlined"
                                        size="large"
                                        startIcon={<Download size={18}/>}
                                        href={DOWNLOAD_PATH}
                                        sx={{
                                            borderColor: 'primary.contrastText',
                                            color: 'primary.contrastText',
                                            px: 3,
                                            py: 1.5,
                                            fontWeight: 600,
                                            fontSize: '1rem',
                                            borderRadius: 2,
                                            '&:hover': {
                                                borderColor: 'primary.contrastText',
                                                bgcolor: 'rgba(255,255,255,0.08)'
                                            },
                                            transition: 'all 0.2s ease'
                                        }}
                                    >
                                        下载客户端
                                    </Button>
                                </Stack>
                            </Box>
                        </Grid>
                        <Grid size={{xs: 12, md: 6}}>
                            <Box 
                                sx={{
                                    position: 'relative',
                                    height: {xs: 300, md: 400},
                                    mt: {xs: 6, md: 0},
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    overflow: 'hidden'
                                }}
                            >
                                {/* 渐变背景 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.15) 100%)',
                                        borderRadius: 3,
                                        backdropFilter: 'blur(10px)',
                                        border: '1px solid rgba(255,255,255,0.2)'
                                    }}
                                />
                                
                                {/* 装饰性几何图形 */}
                                {/* 大圆 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: -20,
                                        right: -20,
                                        width: 120,
                                        height: 120,
                                        borderRadius: '50%',
                                        background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
                                        border: '2px solid rgba(255,255,255,0.2)',
                                        animation: 'float 6s ease-in-out infinite'
                                    }}
                                />
                                
                                {/* 中圆 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        bottom: 40,
                                        left: 30,
                                        width: 80,
                                        height: 80,
                                        borderRadius: '50%',
                                        background: 'linear-gradient(225deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',
                                        border: '1px solid rgba(255,255,255,0.15)',
                                        animation: 'float 4s ease-in-out infinite reverse'
                                    }}
                                />
                                
                                {/* 小圆 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 60,
                                        left: 80,
                                        width: 40,
                                        height: 40,
                                        borderRadius: '50%',
                                        background: 'linear-gradient(135deg, rgba(255,255,255,0.12), rgba(255,255,255,0.06))',
                                        border: '1px solid rgba(255,255,255,0.25)',
                                        animation: 'float 5s ease-in-out infinite'
                                    }}
                                />
                                
                                {/* 菱形 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 120,
                                        right: 80,
                                        width: 60,
                                        height: 60,
                                        transform: 'rotate(45deg)',
                                        background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
                                        border: '1px solid rgba(255,255,255,0.2)',
                                        borderRadius: 1,
                                        animation: 'rotate 10s linear infinite'
                                    }}
                                />
                                
                                {/* 三角形 */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        bottom: 80,
                                        right: 40,
                                        width: 0,
                                        height: 0,
                                        borderLeft: '25px solid transparent',
                                        borderRight: '25px solid transparent',
                                        borderBottom: '43px solid rgba(255,255,255,0.1)',
                                        animation: 'pulse 3s ease-in-out infinite'
                                    }}
                                />
                                
                                {/* 中心内容 */}
                                <Box
                                    sx={{
                                        position: 'relative',
                                        zIndex: 2,
                                        textAlign: 'center',
                                        color: 'primary.contrastText'
                                    }}
                                >
                                    <Box
                                        sx={{
                                            width: 80,
                                            height: 80,
                                            borderRadius: '50%',
                                            background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1))',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mx: 'auto',
                                            mb: 2,
                                            backdropFilter: 'blur(10px)',
                                            border: '2px solid rgba(255,255,255,0.3)',
                                            animation: 'glow 2s ease-in-out infinite alternate'
                                        }}
                                    >
                                        <TrendingUp size={36} color="white"/>
                                    </Box>
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            fontSize: {xs: '1.5rem', md: '2rem'},
                                            fontWeight: 700,
                                            mb: 1,
                                            textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                        }}
                                    >
                                        智能变现
                                    </Typography>
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            fontSize: {xs: '1rem', md: '1.125rem'},
                                            opacity: 0.9,
                                            fontWeight: 400,
                                            textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                                        }}
                                    >
                                        让每一次点击都有价值
                                    </Typography>
                                </Box>
                                
                                {/* CSS动画定义 */}
                                <style jsx>{`
                                    @keyframes float {
                                        0%, 100% { transform: translateY(0px); }
                                        50% { transform: translateY(-10px); }
                                    }
                                    @keyframes rotate {
                                        0% { transform: rotate(45deg); }
                                        100% { transform: rotate(405deg); }
                                    }
                                    @keyframes pulse {
                                        0%, 100% { opacity: 0.5; }
                                        50% { opacity: 1; }
                                    }
                                    @keyframes glow {
                                        0% { box-shadow: 0 0 20px rgba(255,255,255,0.3); }
                                        100% { box-shadow: 0 0 30px rgba(255,255,255,0.5), 0 0 40px rgba(255,255,255,0.3); }
                                    }
                                `}</style>
                            </Box>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 实时数据展示区域 */}
            <Box sx={{py: {xs: 8, md: 12}, bgcolor: 'background.paper'}}>
                <Container maxWidth="lg">
                    <Box sx={{textAlign: 'center', mb: 6}}>
                        <Typography
                            variant="h3"
                            component="h2"
                            sx={{
                                fontSize: {xs: '1.75rem', md: '2.5rem'},
                                fontWeight: 700,
                                color: 'text.primary',
                                mb: 2
                            }}
                        >
                            平台实时数据
                        </Typography>
                        <Typography
                            variant="h6"
                            color="text.secondary"
                            sx={{fontSize: {xs: '1rem', md: '1.25rem'}}}
                        >
                            数据说话，成果可见
                        </Typography>
                    </Box>

                    <Grid container spacing={3}>
                        <Grid size={{xs: 12, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    textAlign: 'center',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 64,
                                        height: 64,
                                        borderRadius: 2,
                                        bgcolor: 'success.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 2
                                    }}
                                >
                                    <DollarSign size={32} color="#4caf50"/>
                                </Box>
                                <Typography
                                    variant="h3"
                                    sx={{
                                        fontSize: {xs: '1.75rem', md: '2rem'},
                                        fontWeight: 700,
                                        color: 'success.main',
                                        mb: 0.5
                                    }}
                                >
                                    ¥{animatedNumbers.revenue}万
                                </Typography>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '0.875rem', md: '1rem'},
                                        color: 'text.primary',
                                        mb: 0.5,
                                        fontWeight: 600
                                    }}
                                >
                                    今日总收益
                                </Typography>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{fontSize: '0.75rem'}}
                                >
                                    较昨日增长 +12.8%
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    textAlign: 'center',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 64,
                                        height: 64,
                                        borderRadius: 2,
                                        bgcolor: 'primary.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 2
                                    }}
                                >
                                    <Users size={32} color="#1976d2"/>
                                </Box>
                                <Typography
                                    variant="h3"
                                    sx={{
                                        fontSize: {xs: '1.75rem', md: '2rem'},
                                        fontWeight: 700,
                                        color: 'primary.main',
                                        mb: 0.5
                                    }}
                                >
                                    {animatedNumbers.users.toLocaleString()}+
                                </Typography>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '0.875rem', md: '1rem'},
                                        color: 'text.primary',
                                        mb: 0.5,
                                        fontWeight: 600
                                    }}
                                >
                                    活跃流量主
                                </Typography>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{fontSize: '0.75rem'}}
                                >
                                    覆盖全行业领域
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    textAlign: 'center',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 64,
                                        height: 64,
                                        borderRadius: 2,
                                        bgcolor: 'warning.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 2
                                    }}
                                >
                                    <TrendingUp size={32} color="#ff9800"/>
                                </Box>
                                <Typography
                                    variant="h3"
                                    sx={{
                                        fontSize: {xs: '1.75rem', md: '2rem'},
                                        fontWeight: 700,
                                        color: 'warning.main',
                                        mb: 0.5
                                    }}
                                >
                                    {animatedNumbers.clicks}亿
                                </Typography>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '0.875rem', md: '1rem'},
                                        color: 'text.primary',
                                        mb: 0.5,
                                        fontWeight: 600
                                    }}
                                >
                                    日广告展示
                                </Typography>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{fontSize: '0.75rem'}}
                                >
                                    高质量流量保证
                                </Typography>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 核心功能区域 */}
            <Box sx={{py: {xs: 8, md: 12}, bgcolor: 'grey.50'}}>
                <Container maxWidth="lg">
                    <Box sx={{textAlign: 'center', mb: 8}}>
                        <Typography
                            variant="h3"
                            component="h2"
                            sx={{
                                fontSize: {xs: '1.75rem', md: '2.5rem'},
                                fontWeight: 700,
                                color: 'text.primary',
                                mb: 2
                            }}
                        >
                            专为流量主设计
                        </Typography>
                        <Typography
                            variant="h6"
                            color="text.secondary"
                            sx={{
                                fontSize: {xs: '1rem', md: '1.25rem'},
                                maxWidth: '600px',
                                mx: 'auto'
                            }}
                        >
                            从流量接入到收益提现，全流程智能化管理
                        </Typography>
                    </Box>

                    <Grid container rowSpacing={10} columnSpacing={3} sx={{ mb: 4 }}>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'primary.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <Target size={24} color="#1976d2"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    精准广告匹配
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    AI智能分析您的流量特征，自动匹配最适合的广告内容，提升点击率和转化率
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'success.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <DollarSign size={24} color="#4caf50"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    透明收益结算
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    实时收益统计，每笔收入清晰可见，支持多种提现方式，结算周期短
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'warning.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <BarChart3 size={24} color="#ff9800"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    数据分析洞察
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    专业的数据分析报告，帮助您了解流量价值，优化广告位布局，提升收益
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'secondary.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <Zap size={24} color="#9c27b0"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    极速接入部署
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    一键接入，无需复杂配置，支持网站、APP、小程序等多种流量形式
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'info.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <Globe size={24} color="#2196f3"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    全球优质广告
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    汇聚全球优质广告主资源，确保广告质量，提供多样化的广告选择
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, sm: 6, md: 4}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 3,
                                    height: '100%',
                                    borderRadius: 2,
                                    bgcolor: 'background.paper',
                                    border: '1px solid',
                                    borderColor: 'grey.200',
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: 2,
                                        bgcolor: 'error.light',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mb: 2
                                    }}
                                >
                                    <Calendar size={24} color="#f44336"/>
                                </Box>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: {xs: '1rem', md: '1.125rem'},
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 1.5
                                    }}
                                >
                                    7x24专业服务
                                </Typography>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.875rem', lineHeight: 1.5}}
                                >
                                    专业客服团队全天候支持，快速响应，确保您的流量变现无忧
                                </Typography>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 成功案例区域 */}
            <Box sx={{py: {xs: 12, md: 12}, bgcolor: 'background.paper'}}>
                <Container maxWidth="lg">
                    <Box sx={{textAlign: 'center', mb: 8}}>
                        <Typography
                            variant="h3"
                            component="h2"
                            sx={{
                                fontSize: {xs: '1.75rem', md: '2.5rem'},
                                fontWeight: 700,
                                color: 'text.primary',
                                mb: 2
                            }}
                        >
                            成功案例
                        </Typography>
                        <Typography
                            variant="h6"
                            color="text.secondary"
                            sx={{fontSize: {xs: '1rem', md: '1.25rem'}}}
                        >
                            看看其他流量主是如何实现收益增长的
                        </Typography>
                    </Box>

                    <Grid container spacing={{xs: 10, md: 4}}>
                        <Grid size={{xs: 12, md: 6}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 4,
                                    borderRadius: 4,
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    height: '100%'
                                }}
                            >
                                <Box sx={{mb: 3}}>
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            fontSize: {xs: '1.1rem', md: '1.25rem'},
                                            fontWeight: 600,
                                            color: 'text.primary',
                                            mb: 1
                                        }}
                                    >
                                        科技博客站长
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{fontSize: '0.875rem'}}
                                    >
                                        月访问量：50万PV
                                    </Typography>
                                </Box>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.9rem', lineHeight: 1.6, mb: 3}}
                                >
                                    "接入Cyber AD后，我的广告收益提升了300%，不仅广告质量高，结算也很及时。数据分析功能让我更好地了解用户行为，优化内容策略。"
                                </Typography>
                                <Box sx={{display: 'flex', alignItems: 'center', gap: 2}}>
                                    <Chip
                                        label="月收益 ¥18,000"
                                        color="success"
                                        size="small"
                                        sx={{fontWeight: 600}}
                                    />
                                    <Chip
                                        label="同比增长 +300%"
                                        color="primary"
                                        size="small"
                                        sx={{fontWeight: 600}}
                                    />
                                </Box>
                            </Card>
                        </Grid>
                        <Grid size={{xs: 12, md: 6}}>
                            <Card
                                elevation={0}
                                sx={{
                                    p: 4,
                                    borderRadius: 4,
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    height: '100%'
                                }}
                            >
                                <Box sx={{mb: 3}}>
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            fontSize: {xs: '1.1rem', md: '1.25rem'},
                                            fontWeight: 600,
                                            color: 'text.primary',
                                            mb: 1
                                        }}
                                    >
                                        移动应用开发者
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{fontSize: '0.875rem'}}
                                    >
                                        日活跃用户：8万DAU
                                    </Typography>
                                </Box>
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{fontSize: '0.9rem', lineHeight: 1.6, mb: 3}}
                                >
                                    "平台的AI匹配算法很智能，推荐的广告和我的用户群体高度匹配。现在广告不再是负担，而是增值服务，用户体验反而更好了。"
                                </Typography>
                                <Box sx={{display: 'flex', alignItems: 'center', gap: 2}}>
                                    <Chip
                                        label="月收益 ¥25,000"
                                        color="success"
                                        size="small"
                                        sx={{fontWeight: 600}}
                                    />
                                    <Chip
                                        label="点击率 +150%"
                                        color="primary"
                                        size="small"
                                        sx={{fontWeight: 600}}
                                    />
                                </Box>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            {/* 开始使用区域 */}
            <Box
                sx={{
                    py: {xs: 8, md: 12},
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    textAlign: 'center'
                }}
            >
                <Container maxWidth="md">
                    <Typography
                        variant="h3"
                        component="h2"
                        sx={{
                            fontSize: {xs: '1.75rem', md: '2.25rem'},
                            fontWeight: 600,
                            mb: 2
                        }}
                    >
                        开始您的流量变现之旅
                    </Typography>
                    <Typography
                        variant="h6"
                        sx={{
                            fontSize: {xs: '1rem', md: '1.125rem'},
                            mb: 4,
                            opacity: 0.9,
                            fontWeight: 400
                        }}
                    >
                        加入50,000+流量主，让您的流量价值最大化
                    </Typography>
                    <Stack
                        direction={{xs: 'column', sm: 'row'}}
                        spacing={2}
                        justifyContent="center"
                        sx={{mb: 3}}
                    >
                        <Button
                            variant="contained"
                            size="large"
                            endIcon={<ArrowRight size={18}/>}
                            href="/public/login"
                            sx={{
                                bgcolor: 'background.paper',
                                color: 'primary.main',
                                px: 3,
                                py: 1.5,
                                fontWeight: 600,
                                fontSize: '1rem',
                                borderRadius: 2,
                                boxShadow: 'none',
                                '&:hover': {
                                    bgcolor: 'grey.100',
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                },
                                transition: 'all 0.2s ease'
                            }}
                        >
                            立即注册
                        </Button>
                        <Button
                            variant="outlined"
                            size="large"
                            startIcon={<Download size={18}/>}
                            href={DOWNLOAD_PATH}
                            sx={{
                                borderColor: 'primary.contrastText',
                                color: 'primary.contrastText',
                                px: 3,
                                py: 1.5,
                                fontWeight: 600,
                                fontSize: '1rem',
                                borderRadius: 2,
                                '&:hover': {
                                    borderColor: 'primary.contrastText',
                                    bgcolor: 'rgba(255,255,255,0.08)'
                                },
                                transition: 'all 0.2s ease'
                            }}
                        >
                            下载客户端
                        </Button>
                    </Stack>
                    <Typography
                        variant="body2"
                        sx={{
                            fontSize: '0.875rem',
                            opacity: 0.8
                        }}
                    >
                        免费注册，3分钟快速接入，当天即可开始赚钱
                    </Typography>
                </Container>
            </Box>

            {/* 底部区域 */}
            <Box sx={{bgcolor: 'background.paper', py: {xs: 6, md: 8}}}>
                <Container maxWidth="lg">
                    <Grid container spacing={4}>
                        <Grid size={{xs: 12, md: 4}}>
                            <Typography
                                variant="h5"
                                component="div"
                                sx={{
                                    fontSize: {xs: '1.25rem', md: '1.5rem'},
                                    fontWeight: 700,
                                    color: 'text.primary',
                                    mb: 2
                                }}
                            >
                                Cyber AD
                            </Typography>
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{fontSize: '0.9rem', lineHeight: 1.6, mb: 3}}
                            >
                                专业的流量主平台，致力于帮助内容创作者和流量主实现价值最大化
                            </Typography>
                        </Grid>
                        <Grid size={{xs: 12, md: 4}}>
                            <Typography
                                variant="h6"
                                sx={{
                                    fontSize: {xs: '1rem', md: '1.125rem'},
                                    fontWeight: 600,
                                    color: 'text.primary',
                                    mb: 2
                                }}
                            >
                                快速链接
                            </Typography>
                            <Stack spacing={1}>
                                <Button
                                    variant="text"
                                    size="small"
                                    href="/public/login"
                                    sx={{
                                        justifyContent: 'flex-start',
                                        color: 'text.secondary',
                                        fontSize: '0.875rem',
                                        textTransform: 'none'
                                    }}
                                >
                                    立即注册
                                </Button>
                                <Button
                                    variant="text"
                                    size="small"
                                    href={DOWNLOAD_PATH}
                                    sx={{
                                        justifyContent: 'flex-start',
                                        color: 'text.secondary',
                                        fontSize: '0.875rem',
                                        textTransform: 'none'
                                    }}
                                >
                                    下载客户端
                                </Button>
                            </Stack>
                        </Grid>
                        <Grid size={{xs: 12, md: 4}}>
                            <Typography
                                variant="h6"
                                sx={{
                                    fontSize: {xs: '1rem', md: '1.125rem'},
                                    fontWeight: 600,
                                    color: 'text.primary',
                                    mb: 2
                                }}
                            >
                                联系我们
                            </Typography>
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{fontSize: '0.875rem'}}
                            >
                                客服邮箱：<EMAIL>
                            </Typography>
                        </Grid>
                    </Grid>
                    <Divider sx={{my: 4}}/>
                    <Box sx={{textAlign: 'center'}}>
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{fontSize: '0.875rem'}}
                        >
                            © {new Date().getFullYear()} Cyber AD. 版权所有
                        </Typography>
                    </Box>
                </Container>
            </Box>
        </Box>
    );
}
