import api from "@/core/api/api";

const RESOURCE = "adv_promotion_task_api";

export const advPromotionTaskApi = {
    queryProductOverview: async (search, page = 1, page_size = 10) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_product_overview",
            data: {
                search,
                page,
                page_size,
            },
        });
    },

    createPromotionTask: async (product_id, count, platform, end_date) => {
        return await api({
            resource: RESOURCE,
            method_name: "create",
            data: {
                product_id,
                count,
                platform,
                end_date,
            },
        });
    },

    // Function to query promotion tasks (now PromotionTask)
    queryPromotionTasks: async (page = 1, page_size = 10) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_promotion_tasks",
            data: {
                page: page,
                page_size: page_size,
            },
        });
    },


    // Function to query promotion task details (paginated)
    queryPromotionTaskDetails: async (taskId, page = 1, page_size = 5) => { // Default page_size 5
        return await api({
            resource: RESOURCE,
            method_name: "query_promotion_task_details",
            data: {
                task_id: taskId,
                page: page,
                page_size: page_size,
            },
        });
    },

    // Function to delete a promotion task (marks as deleted)
    deletePromotionTask: async (taskId) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                task_id: taskId,
            },
        });
    },

};
