import api from "@/core/api/api";

export const captainManagementApi = {
  // 查询所有用户
  queryAllUsers: async () => {
    return await api({
      resource: "captain_management",
      method_name: "query_all_users",
    });
  },

  // 查询可以提升为舰长的用户
  queryPromotableUsers: async () => {
    return await api({
      resource: "captain_management",
      method_name: "query_promotable_users",
    });
  },

  // 查询舰长列表
  queryCaptainList: async () => {
    return await api({
      resource: "captain_management",
      method_name: "query_captain_list",
    });
  },

  // 提升用户为舰长
  promoteToCaptain: async (userId) => {
    return await api({
      resource: "captain_management",
      method_name: "promote_to_captain",
      target_user_id: userId,
    });
  },

  // 撤销舰长权限
  revokeCaptain: async (userId) => {
    return await api({
      resource: "captain_management",
      method_name: "revoke_captain",
      target_user_id: userId,
    });
  },
};
