"use client"

import React from 'react';
import { 
  Container, 
  Typography, 
  Card, 
  CardContent, 
  Stack, 
  Grid, 
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Link,
  Breadcrumbs
} from '@mui/material';
import { 
  MessageCircle,
  ThumbsUp,
  ShoppingCart,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import NextLink from 'next/link';

// 模拟数据 - 在实际项目中应从API获取
const controlCommentData = {
  acquisition: {
    count: 125,
    trend: "+15%",
    comments: [
      { id: 1, content: "这个产品真的太好用了，极力推荐！", time: "2023-06-18 10:23", link: "https://example.com/forum/post/12345" },
      { id: 2, content: "有没有大佬知道这个产品在哪里能买到？看起来很不错", time: "2023-06-17 15:42", link: "https://example.com/forum/post/12346" },
      { id: 3, content: "朋友推荐的，前来了解一下，看评论都说很好", time: "2023-06-16 09:18", link: "https://example.com/forum/post/12347" },
      { id: 4, content: "这款产品的性价比太高了，用起来特别方便", time: "2023-06-15 14:22", link: "https://example.com/forum/post/12348" },
      { id: 5, content: "路过看到好多人推荐，准备入手试试", time: "2023-06-14 11:05", link: "https://example.com/forum/post/12349" },
      { id: 6, content: "第一次使用这个品牌的产品，体验非常不错", time: "2023-06-13 16:30", link: "https://example.com/forum/post/12350" },
      { id: 7, content: "这个产品的设计太人性化了，解决了我的很多问题", time: "2023-06-12 09:40", link: "https://example.com/forum/post/12351" },
      { id: 8, content: "大家有没有用过这个产品？看评论感觉不错", time: "2023-06-11 13:15", link: "https://example.com/forum/post/12352" }
    ],
    platform: "社交论坛",
    name: "获客方向",
    description: "展示所有引导用户多发评论，增加互动的控评内容",
    color: "#8884d8",
    icon: MessageCircle
  },
  reputation: {
    count: 189,
    trend: "+24%",
    comments: [
      { id: 1, content: "用了一个月了，效果非常明显，比市面上同类产品好太多", time: "2023-06-18 14:35", link: "https://example.com/forum/post/23456" },
      { id: 2, content: "客服很专业，解答了我所有疑问，很满意", time: "2023-06-17 11:20", link: "https://example.com/forum/post/23457" },
      { id: 3, content: "包装很精美，产品质量也很好，值得推荐", time: "2023-06-16 16:48", link: "https://example.com/forum/post/23458" },
      { id: 4, content: "这家公司的售后服务太棒了，遇到问题立刻解决", time: "2023-06-15 09:32", link: "https://example.com/forum/post/23459" },
      { id: 5, content: "我已经是这个品牌的忠实粉丝了，每款产品都很优质", time: "2023-06-14 18:25", link: "https://example.com/forum/post/23460" },
      { id: 6, content: "这个产品的设计真的很人性化，使用体验非常好", time: "2023-06-13 10:15", link: "https://example.com/forum/post/23461" },
      { id: 7, content: "朋友们都说我买的这款产品特别好，都来问我在哪买的", time: "2023-06-12 14:40", link: "https://example.com/forum/post/23462" },
      { id: 8, content: "这个品牌的产品一直很稳定，质量有保证", time: "2023-06-11 16:55", link: "https://example.com/forum/post/23463" }
    ],
    platform: "评论区",
    name: "舆情方向",
    description: "展示所有正面引导，提升品牌形象的控评内容",
    color: "#82ca9d",
    icon: ThumbsUp
  },
  conversion: {
    count: 86,
    trend: "+32%",
    comments: [
      { id: 1, content: "果断下单了，这个价格真的太划算了，错过要再等一年", time: "2023-06-18 20:15", link: "https://example.com/forum/post/34567" },
      { id: 2, content: "刚收到货，比想象中的还要好，准备再入一件", time: "2023-06-17 18:30", link: "https://example.com/forum/post/34568" },
      { id: 3, content: "用了朋友推荐的优惠码，简直不要太划算，大家冲啊", time: "2023-06-16 12:25", link: "https://example.com/forum/post/34569" },
      { id: 4, content: "双十一活动价，刚好需要就入手了，体验特别好", time: "2023-06-15 15:40", link: "https://example.com/forum/post/34570" },
      { id: 5, content: "这款产品性价比真的超高，用了一个月来说说体验", time: "2023-06-14 09:18", link: "https://example.com/forum/post/34571" },
      { id: 6, content: "赶上了限时特惠，价格比平时便宜了30%，超值", time: "2023-06-13 14:32", link: "https://example.com/forum/post/34572" },
      { id: 7, content: "入手三天了，效果明显，这个钱花得值", time: "2023-06-12 11:05", link: "https://example.com/forum/post/34573" },
      { id: 8, content: "官方旗舰店买的，送了不少赠品，太划算了", time: "2023-06-11 17:22", link: "https://example.com/forum/post/34574" }
    ],
    platform: "电商平台",
    name: "成交方向",
    description: "展示所有促成购买，提高转化率的控评内容",
    color: "#ffc658",
    icon: ShoppingCart
  }
};

export default function DirectionDetails() {
  const params = useParams();
  const theme = useTheme();
  const router = useRouter();
  
  // 获取当前方向
  const direction = params.direction;
  
  // 根据URL参数获取对应的数据
  const directionData = controlCommentData[direction];
  
  // 如果没有找到对应方向的数据，显示提示信息
  if (!directionData) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" color="error" gutterBottom>
          未找到对应方向的数据
        </Typography>
        <NextLink href={`/protected/comment-analysis/${params.id}/public-opinion-report`} passHref>
          <Typography 
            variant="body1" 
            sx={{ color: theme.palette.primary.main, textDecoration: 'underline', cursor: 'pointer' }}
          >
            返回舆论引导反馈报表
          </Typography>
        </NextLink>
      </Container>
    );
  }
  
  // 获取方向对应的图标组件
  const IconComponent = directionData.icon;
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <NextLink href={`/protected/comment-analysis/${params.id}/public-opinion-report`} passHref style={{ textDecoration: 'none', color: theme.palette.text.secondary }}>
          <Typography variant="body2" display="flex" alignItems="center">
            <ArrowLeft size={16} style={{ marginRight: '4px' }} />
            返回舆论引导反馈报表
          </Typography>
        </NextLink>
        <Typography variant="body2" color="text.primary">
          {directionData.name}控评详情
        </Typography>
      </Breadcrumbs>
      
      {/* 标题 */}
      <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 4 }}>
        <IconComponent size={28} color={directionData.color} />
        <Typography variant="h4" component="h1">
          {directionData.name}控评详情
        </Typography>
      </Stack>
      
      {/* 统计卡片 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} sx={{ width: '100%' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Stack>
                <Typography variant="body2" color="text.secondary">控评总数</Typography>
                <Typography variant="h4">{directionData.count}</Typography>
              </Stack>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Stack>
                <Typography variant="body2" color="text.secondary">环比增长</Typography>
                <Typography variant="h4" color="success.main">{directionData.trend}</Typography>
              </Stack>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <Stack>
                <Typography variant="body2" color="text.secondary">主要平台</Typography>
                <Typography variant="h4">{directionData.platform}</Typography>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* 控评列表 */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{directionData.name}控评列表</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {directionData.description}
          </Typography>
          
          <List>
            {directionData.comments.map((comment) => (
              <ListItem key={comment.id} divider sx={{ py: 2 }}>
                <ListItemText
                  primary={
                    <Typography variant="body1" sx={{ mb: 1 }}>{comment.content}</Typography>
                  }
                  secondary={
                    <Chip 
                      label={comment.time} 
                      size="small" 
                      sx={{ bgcolor: 'background.paper', color: 'text.secondary' }} 
                    />
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end" component={Link} href={comment.link} target="_blank">
                    <ExternalLink size={16} />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Container>
  );
} 