"use client";

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Button,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  InputAdornment,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Search, Shield, UserPlus, UserMinus, AlertTriangle } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { captainManagementApi } from "@/api/captain-management-api";

export default function CaptainManagement() {
  const theme = useTheme();
  const dispatch = useDispatch();
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [openPromoteDialog, setOpenPromoteDialog] = useState(false);
  const [openRevokeDialog, setOpenRevokeDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // 加载用户数据
  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await captainManagementApi.queryAllUsers();
      setUsers(response.users || []);
    } catch (error) {
      console.error('加载用户数据失败:', error);
      dispatch(addAlert({ type: AlertType.ERROR, message: '加载用户数据失败' }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // 处理打开提升权限对话框
  const handleOpenPromoteDialog = (user) => {
    setSelectedUser(user);
    setOpenPromoteDialog(true);
  };

  // 处理关闭提升权限对话框
  const handleClosePromoteDialog = () => {
    setOpenPromoteDialog(false);
    setSelectedUser(null);
  };

  // 处理打开撤销权限对话框
  const handleOpenRevokeDialog = (user) => {
    setSelectedUser(user);
    setOpenRevokeDialog(true);
  };

  // 处理关闭撤销权限对话框
  const handleCloseRevokeDialog = () => {
    setOpenRevokeDialog(false);
    setSelectedUser(null);
  };

  // 处理提升为舰长
  const handlePromoteToCaptain = async () => {
    try {
      setActionLoading(true);
      await captainManagementApi.promoteToCaptain(selectedUser.id_);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: '舰长权限已授予' }));
      await loadUsers(); // 重新加载数据
      handleClosePromoteDialog();
    } catch (error) {
      console.error('提升舰长失败:', error);
      dispatch(addAlert({ type: AlertType.ERROR, message: error.message || '提升舰长失败' }));
    } finally {
      setActionLoading(false);
    }
  };

  // 处理撤销舰长权限
  const handleRevokeCaptain = async () => {
    try {
      setActionLoading(true);
      await captainManagementApi.revokeCaptain(selectedUser.id_);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: '舰长权限已撤销' }));
      await loadUsers(); // 重新加载数据
      handleCloseRevokeDialog();
    } catch (error) {
      console.error('撤销舰长权限失败:', error);
      dispatch(addAlert({ type: AlertType.ERROR, message: error.message || '撤销舰长权限失败' }));
    } finally {
      setActionLoading(false);
    }
  };

  // 过滤用户
  const filteredUsers = users.filter(user => 
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.id_?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 格式化创建时间
  const formatDate = (timestamp) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleDateString('zh-CN');
  };

  // 获取用户角色显示
  const getRoleDisplay = (roles) => {
    if (!roles || roles.length === 0) return '无角色';
    return roles.map(role => {
      const roleMap = {
        'user': '用户',
        'creator': '创作者',
        'captain': '舰长',
        'advertiser': '广告主',
        'admin': '管理员'
      };
      return roleMap[role] || role;
    }).join(', ');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: 4 }}>
        舰长管理
      </Typography>

      <Paper 
        elevation={0} 
        sx={{ 
          p: 3, 
          mb: 4, 
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Shield size={22} color={theme.palette.info.main} />
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              关于舰长管理
            </Typography>
            <Typography variant="body2" color="text.secondary">
              舰长是具有特定管理权限的高级用户角色。只有具有"用户"或"创作者"权限的账户才能被提升为舰长。
              您可以为符合条件的用户授予舰长权限，也可以撤销现有舰长的权限。
            </Typography>
          </Box>
        </Box>
      </Paper>

      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between' }}>
        <TextField
          placeholder="搜索用户名或ID"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ width: 300 }}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={18} color={theme.palette.text.secondary} />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          overflow: 'hidden'
        }}
      >
        <TableContainer>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                <TableCell>用户</TableCell>
                <TableCell>用户角色</TableCell>
                <TableCell>创建时间</TableCell>
                <TableCell>状态</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <TableRow key={user.id_}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar
                          sx={{ 
                            width: 40, 
                            height: 40,
                            bgcolor: user.is_captain ? 'primary.light' : 'grey.300',
                          }}
                        >
                          {user.username?.charAt(0)?.toUpperCase() || 'U'}
                        </Avatar>
                        <Box>
                          <Typography variant="body1">{user.username || '未知用户'}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {user.id_}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getRoleDisplay(user.roles)}
                      </Typography>
                    </TableCell>
                    <TableCell>{formatDate(user.create_at)}</TableCell>
                    <TableCell>
                      {user.is_captain ? (
                        <Chip 
                          icon={<Shield size={14} />}
                          label="舰长" 
                          size="small" 
                          color="primary" 
                          variant="filled"
                        />
                      ) : user.can_be_captain ? (
                        <Chip label="可提升" size="small" variant="outlined" color="success" />
                      ) : (
                        <Chip label="普通用户" size="small" variant="outlined" />
                      )}
                    </TableCell>
                    <TableCell align="right">
                      {user.is_captain ? (
                        <Button
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<UserMinus size={16} />}
                          onClick={() => handleOpenRevokeDialog(user)}
                        >
                          撤销权限
                        </Button>
                      ) : user.can_be_captain ? (
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<UserPlus size={16} />}
                          onClick={() => handleOpenPromoteDialog(user)}
                        >
                          授予权限
                        </Button>
                      ) : (
                        <Typography variant="caption" color="text.secondary">
                          不符合条件
                        </Typography>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    <Typography color="text.secondary">
                      没有找到匹配的用户
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* 提升权限确认对话框 */}
      <Dialog open={openPromoteDialog} onClose={handleClosePromoteDialog}>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Shield size={20} color={theme.palette.primary.main} />
          确认授予舰长权限
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            您确定要将 <b>{selectedUser?.username}</b> 提升为舰长吗？
            授予舰长权限后，该用户将获得相应的管理功能。
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleClosePromoteDialog} color="inherit" disabled={actionLoading}>
            取消
          </Button>
          <Button 
            onClick={handlePromoteToCaptain} 
            variant="contained" 
            startIcon={actionLoading ? <CircularProgress size={16} /> : <UserPlus size={16} />}
            disabled={actionLoading}
          >
            {actionLoading ? '处理中...' : '授予权限'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 撤销权限确认对话框 */}
      <Dialog open={openRevokeDialog} onClose={handleCloseRevokeDialog}>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AlertTriangle size={20} color={theme.palette.warning.main} />
          确认撤销舰长权限
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            您确定要撤销 <b>{selectedUser?.username}</b> 的舰长权限吗？
            此操作将移除该用户的舰长角色，但保留其原有的其他权限。
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleCloseRevokeDialog} color="inherit" disabled={actionLoading}>
            取消
          </Button>
          <Button 
            onClick={handleRevokeCaptain} 
            color="error" 
            variant="contained" 
            startIcon={actionLoading ? <CircularProgress size={16} /> : <UserMinus size={16} />}
            disabled={actionLoading}
          >
            {actionLoading ? '处理中...' : '撤销权限'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
} 