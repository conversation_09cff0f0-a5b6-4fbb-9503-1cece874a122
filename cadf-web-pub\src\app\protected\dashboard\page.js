"use client";

import {Ava<PERSON>, Box, <PERSON><PERSON>, Card, CardContent, Container, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Grid, LinearProgress, Paper, Stack, TextField, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {Anchor, Award, BookOpen, DollarSign, Lightbulb, ListChecks, Star, TrendingUp, UserCheck, Users} from "lucide-react";
import {useRouter} from 'next/navigation';
import {BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer} from 'recharts';
import {useState} from 'react';
import { pubCrewManagementApi } from '@/api/pub-crew-management-api';
import { useSnackbar } from 'notistack';

// 模拟数据 - 等级与收益
const levelData = [
    {level: '1级', dailyIncome: 50, monthlyIncome: 1500, description: '新手创作者'},
    {level: '2级', dailyIncome: 100, monthlyIncome: 3000, description: '活跃创作者'},
    {level: '3级', dailyIncome: 200, monthlyIncome: 6000, description: '优质创作者'},
    {level: '4级', dailyIncome: 350, monthlyIncome: 10500, description: '专业创作者'},
    {level: '5级', dailyIncome: 600, monthlyIncome: 18000, description: '顶级创作者'},
];

// 用户当前等级
const currentUserLevel = 2;
const nextLevelProgress = 65; // 百分比

export default function Dashboard() {
    const theme = useTheme();
    const router = useRouter();
    const { enqueueSnackbar } = useSnackbar();
    const [openFleetDialog, setOpenFleetDialog] = useState(false);
    const [fleetCode, setFleetCode] = useState('');

    const handleOpenFleetDialog = () => {
        setOpenFleetDialog(true);
    };

    const handleCloseFleetDialog = () => {
        setOpenFleetDialog(false);
        setFleetCode('');
    };

    const handleJoinFleet = async () => {
        console.log('尝试加入舰队代码:', fleetCode);
        try {
            const response = await pubCrewManagementApi.joinFleet(fleetCode.trim());
            console.log('加入舰队成功:', response);
            enqueueSnackbar(response.message || '成功加入舰队！', { variant: 'success' });
            handleCloseFleetDialog();
        } catch (error) {
            console.error('加入舰队失败:', error);
            enqueueSnackbar(error.message || '加入舰队失败，请检查代码或联系管理员。', { variant: 'error' });
        }
    };

    // 三个主要功能区域的数据
    const mainFeatures = [
        {
            title: "我的收益",
            description: "查看您的收益情况和支付记录",
            icon: <DollarSign size={32}/>,
            color: theme.palette.primary.main,
            path: "/protected/revenue"
        },
        {
            title: "任务中心",
            description: "浏览和接受新任务，查看进行中的任务",
            icon: <ListChecks size={32}/>,
            color: theme.palette.success.main,
            path: "/protected/tasks/account"
        },
        {
            title: "账号管理",
            description: "管理您的所有社交媒体账号",
            icon: <Users size={32}/>,
            color: theme.palette.info.main,
            path: "/protected/accounts"
        }
    ];

    // 推荐建议数据
    const recommendations = [
        {
            title: "完善您的账号信息",
            description: "增加更多账号详情可以提高任务匹配率",
            icon: <UserCheck size={24}/>
        },
        {
            title: "尝试新的任务类型",
            description: "多样化的内容创作可以增加您的收益",
            icon: <TrendingUp size={24}/>
        },
        {
            title: "定期更新您的作品集",
            description: "保持活跃度可以获得更多任务机会",
            icon: <Lightbulb size={24}/>
        }
    ];

    // 账号画像数据（示例数据）
    const userProfile = {
        completionRate: 85,
        activeAccounts: 3,
        accountTypes: ["小红书", "抖音", "微博"],
        strengths: ["美妆", "生活方式", "旅行"],
        taskPreference: "产品测评"
    };

    // 攻略教程数据
    const tutorials = [
        {
            title: "如何提高任务完成率",
            image: "https://placehold.co/300x200/e3f2fd/1565c0?text=TaskCompletion",
            description: "学习高效完成任务的技巧和方法"
        },
        {
            title: "社交媒体内容创作指南",
            image: "https://placehold.co/300x200/e8f5e9/2e7d32?text=ContentCreation",
            description: "掌握吸引人的内容创作技巧"
        },
        {
            title: "新手指引：平台使用教程",
            image: "https://placehold.co/300x200/fff3e0/e65100?text=BeginnerGuide",
            description: "从零开始了解平台功能与操作方法"
        }
    ];

    return (
        <Box sx={{py: 4}}>
            <Container maxWidth="xl">
                {/* 顶部标题 */}
                <Typography variant="h4" component="h1" gutterBottom sx={{fontWeight: 600, mb: 4}}>
                    控制台
                </Typography>

                {/* 三个主要功能卡片 */}
                <Grid container spacing={3} sx={{mb: 6}}>
                    {mainFeatures.map((feature, index) => (
                        <Grid size={{ xs: 12, md: 4 }} key={index}>
                            <Card
                                sx={{
                                    height: '100%',
                                    cursor: 'pointer',
                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
                                    }
                                }}
                                onClick={feature.onClick || (() => router.push(feature.path))}
                            >
                                <CardContent sx={{p: 3}}>
                                    <Box sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 2
                                    }}>
                                        <Avatar
                                            sx={{
                                                bgcolor: feature.color,
                                                width: 56,
                                                height: 56,
                                                mr: 2
                                            }}
                                        >
                                            {feature.icon}
                                        </Avatar>
                                        <Typography variant="h5" component="div" sx={{fontWeight: 600}}>
                                            {feature.title}
                                        </Typography>
                                    </Box>
                                    <Typography variant="body1" color="text.secondary">
                                        {feature.description}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>


                {/* 舰队信息 */}
                <Paper sx={{p: 3, mb: 4, overflow: 'hidden', position: 'relative'}}>
                    <Box sx={{
                        position: 'absolute',
                        right: -20,
                        top: -20,
                        opacity: 0.05,
                        transform: 'rotate(15deg)'
                    }}>
                        <Anchor size={150}/>
                    </Box>
                    <Typography variant="h6" component="h2" gutterBottom sx={{fontWeight: 600, display: 'flex', alignItems: 'center'}}>
                        <Anchor size={20} style={{marginRight: 8}}/>
                        舰队中心
                    </Typography>
                    <Divider sx={{mb: 2}}/>
                    <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                        <Grid size={{ xs: 12, md: 7 }}>
                            <Typography variant="body1" paragraph>
                                加入舰队，与其他创作者一起合作完成更高价值的任务。舰队合作可以提高任务完成效率和质量，获得更多收益！
                            </Typography>
                            <Box sx={{display: 'flex', gap: 2, mt: 2}}>
                                <Button
                                    variant="contained"
                                    color="warning"
                                    startIcon={<Anchor size={16}/>}
                                    onClick={handleOpenFleetDialog}
                                >
                                    输入舰队码加入
                                </Button>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, md: 5 }}>
                            <Paper elevation={2} sx={{p: 2, bgcolor: 'background.default', borderRadius: 2}}>
                                <Typography variant="subtitle2" gutterBottom>舰队优势:</Typography>
                                <Stack spacing={1}>
                                    <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                        <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                        任务收益提升15%-30%
                                    </Typography>
                                    <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                        <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                        获得专属团队任务
                                    </Typography>
                                    <Typography variant="body2" sx={{display: 'flex', alignItems: 'center'}}>
                                        <Box component="span" sx={{color: 'warning.main', mr: 1}}>•</Box>
                                        共享创作资源与素材
                                    </Typography>
                                </Stack>
                            </Paper>
                        </Grid>
                    </Grid>
                </Paper>
            </Container>

            {/* 舰队加入对话框 */}
            <Dialog
                open={openFleetDialog}
                onClose={handleCloseFleetDialog}
                maxWidth="xs"
                fullWidth
            >
                <DialogTitle sx={{
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    display: 'flex',
                    alignItems: 'center'
                }}>
                    <Anchor size={20} style={{marginRight: 10}}/>
                    加入舰队
                </DialogTitle>
                <DialogContent sx={{pt: 3, pb: 1}}>
                    <Typography variant="body2" paragraph>
                        请输入邀请人提供的舰队代码，加入后可以与团队成员一起合作完成任务，获取更高收益。
                    </Typography>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="舰队代码"
                        type="text"
                        fullWidth
                        variant="outlined"
                        value={fleetCode}
                        onChange={(e) => setFleetCode(e.target.value)}
                        placeholder="例如: FLEET-123456"
                        sx={{mb: 2}}
                    />
                </DialogContent>
                <DialogActions sx={{px: 3, py: 2}}>
                    <Button onClick={handleCloseFleetDialog} color="inherit">
                        取消
                    </Button>
                    <Button
                        onClick={handleJoinFleet}
                        variant="contained"
                        color="warning"
                        disabled={!fleetCode.trim()}
                    >
                        加入舰队
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
} 