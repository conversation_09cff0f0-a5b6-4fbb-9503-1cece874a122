import os
import asyncio
import time
import random
from io import BytesIO
from PIL import Image
from omni.log.log import olog
from common_config.common_config import LOCAL_TEMP_DIR


async def save_temp_pic(image_bytes: bytes) -> str:
    """在系统临时目录中创建PNG临时文件，支持任何图片格式转换"""
    olog.info("开始创建临时文件")
    
    # 使用配置的临时目录
    temp_dir = LOCAL_TEMP_DIR
    olog.info(f"使用配置的临时目录: {temp_dir}")
    
    # 生成文件名：pic_时间戳_6位随机数.png
    timestamp = str(int(time.time()))
    random_suffix = str(random.randint(100000, 999999))
    filename = f"pic_{timestamp}_{random_suffix}.png"
    temp_path = os.path.join(temp_dir, filename)
    
    def convert_and_save_image():
        # 使用PIL处理图片，确保转换为PNG格式
        image = Image.open(BytesIO(image_bytes))
        
        # 如果图片有透明通道(RGBA)，保持透明通道；否则转换为RGB
        if image.mode in ('RGBA', 'LA'):
            # 保持透明通道
            pass
        elif image.mode != 'RGB':
            # 转换为RGB模式
            image = image.convert('RGB')
        
        # 保存为PNG格式
        image.save(temp_path, 'PNG')
    
    await asyncio.to_thread(convert_and_save_image)
    olog.info(f"临时文件创建并保存PNG图片成功: {temp_path}, 原始大小: {len(image_bytes)} bytes")
    
    return temp_path