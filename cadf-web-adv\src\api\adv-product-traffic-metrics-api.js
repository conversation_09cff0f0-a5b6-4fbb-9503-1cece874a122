import api from "@/core/api/api";

const RESOURCE = "adv_product_traffic_metrics_api";

export const advProductTrafficMetricsApi = {

    queryAll: async ({page = 1, page_size = 5} = {}) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_all",
            data: {
                page: page,
                page_size: page_size
            }
        });
    },

    getTotalStats: async () => {
        return await api({
            resource: RESOURCE,
            method_name: "get_total_stats",
        });
    },
};
