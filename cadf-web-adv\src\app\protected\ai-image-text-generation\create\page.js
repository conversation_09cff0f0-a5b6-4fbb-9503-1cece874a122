"use client";

import {useEffect, useState} from "react";
import {Box, Button, Container, Grid, IconButton, Paper, Stack, TextField, Typography, useMediaQ<PERSON>y, Drawer, Chip, Avatar, Badge} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import {useRouter} from "next/navigation";
import {Check, ChevronLeft, Megaphone, Plus, RefreshCcw, Search, Package, Image, Send, X} from "lucide-react";
import InfiniteScrollList from "@/core/components/InfiniteScrollList";
import {advProductApi} from "@/api/adv-product-api";
import {advUserBasicMaterialApi} from "@/api/adv-user-basic-material-api";
import {advAiGenerationTaskApi} from "@/api/adv-ai-generation-task-api";

// 通用数据处理函数
const transformDataItem = (item, type) => {
    const baseUrl = type === 'product' 
        ? `https://placehold.co/300x400/757de8/ffffff?text=${item.title}`
        : `https://placehold.co/300x400/81c784/ffffff?text=${item.title}`;
    
    let imageUrl = baseUrl;
    if (type === 'product' && item.image_url) {
        imageUrl = item.image_url;
    } else if (type === 'material' && item.images?.length > 0 && item.images[0].signed_url) {
        imageUrl = item.images[0].signed_url;
    }

    return {
        ...item,
        id: item._id,
        name: item.title,
        description: type === 'product' ? (item.description || '') : (item.content || ''),
        image: imageUrl,
        selected: false
    };
};

// 通用API调用钩子
const useDataLoader = (api, type) => {
    const [items, setItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [hasMore, setHasMore] = useState(true);
    const [searchInput, setSearchInput] = useState("");
    const itemsPerPage = 5;

    const loadData = async (searchTerm = "", pageNum = 1, append = false) => {
        if (pageNum === 1) {
            setLoading(true);
            setError(null);
        }
        
        try {
            // 简化API调用
            const apiMethod = api.query_all || api.queryAll;
            let response = await apiMethod(searchTerm, pageNum, itemsPerPage);
            
            // 处理嵌套Promise
            if (response?.then) response = await response;
            
            // 获取数据
            const { results = [], total = 0 } = response?.data || response || {};
            
            // 转换数据并更新状态
            const transformedItems = results.map(item => transformDataItem(item, type));
            setItems(prev => append ? [...prev, ...transformedItems] : transformedItems);
            setTotalCount(total);
            setHasMore(pageNum < Math.ceil(total / itemsPerPage));
            setPage(pageNum + 1);
            
        } catch (err) {
            console.error(`获取${type === 'product' ? '产品' : '素材'}失败:`, err);
            setError(`获取${type === 'product' ? '产品' : '素材'}数据失败，请稍后重试`);
            if (!append) {
                setItems([]);
                setTotalCount(0);
                setHasMore(false);
            }
        } finally {
            setLoading(false);
        }
    };

    const loadMore = () => loadData(searchInput, page, true);
    const search = () => {
        setPage(1);
        loadData(searchInput, 1, false);
    };
    const refresh = () => {
        setSearchInput("");
        setPage(1);
        loadData("", 1, false);
    };

    return {
        items, setItems, loading, error, totalCount, hasMore,
        searchInput, setSearchInput, loadData, loadMore, search, refresh
    };
};

// 选择器抽屉组件
const SelectionDrawer = ({ 
    type, open, onClose, isMobile, 
    data, onToggle, onAdd, 
    theme, router 
}) => {
    const { items, loading, error, searchInput, setSearchInput, search, refresh, loadMore, hasMore } = data;
    const title = type === 'product' ? '选择产品' : '选择素材';
    const placeholder = type === 'product' ? '搜索产品名称或描述' : '搜索素材名称或描述';
    const description = type === 'product' ? '请选择要进行AI图文生成的产品' : '请选择需要对标的参考素材（可多选）';
    const addRoute = type === 'product' ? '/protected/product-management/new' : '/protected/ai-base-material-management';
    const addText = type === 'product' ? '添加新产品' : '添加新素材';
    const Icon = type === 'product' ? Package : Image;

    return (
        <Box sx={{ width: isMobile ? '100vw' : 600, height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="medium">{title}</Typography>
                    <IconButton onClick={onClose}>
                        <X size={20} />
                    </IconButton>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                    {description}
                </Typography>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 2 }}>
                    <TextField
                        fullWidth
                        placeholder={placeholder}
                        variant="outlined"
                        size="small"
                        value={searchInput}
                        onChange={(e) => setSearchInput(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && search()}
                    />
                    <IconButton onClick={search} color="primary">
                        <Search size={20}/>
                    </IconButton>
                    <IconButton onClick={refresh}>
                        <RefreshCcw size={20}/>
                    </IconButton>
                </Stack>
            </Box>
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
                {loading ? (
                    <Box sx={{ p: 4, textAlign: "center" }}>
                        <Typography color="text.secondary">正在加载{type === 'product' ? '产品' : '素材'}...</Typography>
                    </Box>
                ) : error ? (
                    <Box sx={{ p: 4, textAlign: "center" }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : (
                    <InfiniteScrollList
                        items={[
                            { id: 'add-new', isAddCard: true },
                            ...items
                        ]}
                        renderItem={(item) => {
                            if (item.isAddCard) {
                                return (
                                    <Paper
                                        onClick={() => router.push(addRoute)}
                                        sx={{
                                            p: 2,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderStyle: 'dashed',
                                            borderWidth: 2,
                                            borderColor: 'primary.main',
                                            backgroundColor: 'primary.50',
                                            cursor: 'pointer',
                                            minHeight: 80,
                                            '&:hover': { bgcolor: 'primary.100' }
                                        }}
                                    >
                                        <Plus size={20} color={theme.palette.primary.main} />
                                        <Typography variant="body2" color="primary.main" sx={{ mt: 0.5 }}>
                                            {addText}
                                        </Typography>
                                    </Paper>
                                );
                            }
                            
                            return (
                                <Paper
                                    onClick={() => {
                                        onToggle(item.id);
                                        if (type === 'product') onClose();
                                    }}
                                    sx={{
                                        p: 2,
                                        display: 'flex',
                                        alignItems: 'center',
                                        cursor: 'pointer',
                                        border: item.selected ? 2 : 1,
                                        borderColor: item.selected ? 'primary.main' : 'divider',
                                        bgcolor: item.selected ? 'primary.50' : 'background.paper',
                                        '&:hover': {
                                            bgcolor: item.selected ? 'primary.100' : 'grey.50'
                                        }
                                    }}
                                >
                                    <Avatar src={item.image} sx={{ width: 48, height: 48, mr: 2 }}>
                                        <Icon size={20} />
                                    </Avatar>
                                    <Box sx={{ flex: 1 }}>
                                        <Typography variant="subtitle1" fontWeight="medium">
                                            {item.name}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden'
                                        }}>
                                            {item.description}
                                        </Typography>
                                    </Box>
                                    {item.selected && (
                                        <Check size={20} color={theme.palette.primary.main} />
                                    )}
                                </Paper>
                            );
                        }}
                        loadMore={loadMore}
                        hasMore={hasMore}
                        gridColumns={{ xs: 12 }}
                    />
                )}
            </Box>
        </Box>
    );
};

export default function CreateTask() {
    const theme = useTheme();
    const router = useRouter();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    
    // 使用自定义钩子管理产品和素材数据
    const products = useDataLoader(advProductApi, 'product');
    const materials = useDataLoader(advUserBasicMaterialApi, 'material');
    
    // 简化后的状态管理
    const [drawers, setDrawers] = useState({ product: false, material: false });
    const [generateCount, setGenerateCount] = useState(1);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 简化的抽屉控制
    const toggleDrawer = (type) => {
        setDrawers(prev => ({ ...prev, [type]: !prev[type] }));
    };

    // 初始化数据加载
    useEffect(() => {
        products.loadData();
        materials.loadData();
    }, []);

    // 简化的任务提交
    const submitTask = async () => {
        const selectedProduct = products.items.find(p => p.selected);
        const selectedMaterialIds = materials.items.filter(m => m.selected).map(m => m.id);

        if (!selectedProduct || selectedMaterialIds.length === 0) return;

        setIsSubmitting(true);
        try {
            const response = await advAiGenerationTaskApi.create(
                selectedProduct.id, selectedMaterialIds, generateCount
            );
            // 跳转到成功页面
            router.push(`/protected/ai-image-text-generation/success`);
        } catch (error) {
            console.error("任务创建失败:", error);
            setIsSubmitting(false);
        }
    };

    // 简化的选择处理
    const handleProductToggle = (id) => {
        products.setItems(prev => prev.map(p => ({ 
            ...p, selected: p.id === id 
        })));
    };

    const handleMaterialToggle = (id) => {
        materials.setItems(prev => prev.map(m => 
            m.id === id ? { ...m, selected: !m.selected } : m
        ));
    };

    const selectedMaterialsCount = materials.items.filter(m => m.selected).length;

    // 简化的工具函数
    const canSubmit = () => {
        const selectedProduct = products.items.find(p => p.selected);
        return selectedProduct && selectedMaterialsCount > 0;
    };

    return (
        <>
            <Container maxWidth={false} sx={{ py: {xs: 2, md: 4}, px: {xs: 1, sm: 2, md: 4} }}>
                <Paper sx={{
                    p: {xs: 2, sm: 3, md: 4},
                    mb: {xs: 2, md: 3},
                    borderRadius: 3,
                    boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.06)",
                    border: `1px solid ${theme.palette.grey[100]}`,
                }}>
                    {/* 页面标题和返回按钮 */}
                    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 4 }}>
                        <Typography variant={{xs: "h4", md: "h3"}} fontWeight="bold">
                            创建AI图文生成任务
                        </Typography>
                        <Button
                            variant="outlined"
                            onClick={() => router.push("/protected/ai-image-text-generation")}
                            startIcon={<ChevronLeft size={18}/>}
                            sx={{ display: { xs: 'none', sm: 'flex' } }}
                        >
                            返回列表
                        </Button>
                        <IconButton
                            onClick={() => router.push("/protected/ai-image-text-generation")}
                            sx={{ display: { xs: 'flex', sm: 'none' } }}
                        >
                            <ChevronLeft size={20} />
                        </IconButton>
                    </Box>

                    {/* 任务引导 */}
                    <Box sx={{ mb: 4, p: 3, border: 2, borderColor: 'primary.main', borderRadius: 2, bgcolor: 'primary.50' }}>
                        <Typography variant="h5" fontWeight="bold" color="primary.main" sx={{ mb: 2 }}>
                            请先完成以下步骤
                        </Typography>
                        <Stack spacing={1} sx={{ mb: 2 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="h6" color="primary.main" sx={{ mr: 1 }}>1.</Typography>
                                <Typography variant="body1" fontWeight="medium">
                                    选择产品 - 选择要进行AI图文生成的产品
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="h6" color="primary.main" sx={{ mr: 1 }}>2.</Typography>
                                <Typography variant="body1" fontWeight="medium">
                                    选择素材 - 选择需要对标的参考素材（可多选）
                                </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="h6" color="primary.main" sx={{ mr: 1 }}>3.</Typography>
                                <Typography variant="body1" fontWeight="medium">
                                    设置生成数量 - 配置要生成的AI图文内容数量
                                </Typography>
                            </Box>
                        </Stack>
                        <Typography variant="body2" color="primary.dark">
                            完成所有步骤后即可创建AI图文生成任务
                        </Typography>
                    </Box>

                    {/* 选择区域 */}
                    <Grid container spacing={3} sx={{ mb: 4 }}>
                        {/* 产品选择区域 */}
                        <Grid size={{ xs: 12, md: 6 }}>
                            <Paper sx={{ p: 3, height: 'fit-content' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Package size={20} color={theme.palette.primary.main} />
                                        <Typography variant="h6" sx={{ ml: 1, fontWeight: 'medium' }}>
                                            1. 选择产品
                                        </Typography>
                                    </Box>
                                    <Button
                                        size="small"
                                        variant="outlined"
                                        onClick={() => toggleDrawer('product')}
                                        startIcon={<Plus size={16} />}
                                    >
                                        选择产品
                                    </Button>
                                </Box>
                                
                                {(() => {
                                    const selectedProduct = products.items.find(p => p.selected);
                                    if (selectedProduct) {
                                        return (
                                            <Paper variant="outlined" sx={{ 
                                                p: 2, 
                                                display: 'flex', 
                                                alignItems: 'center',
                                                bgcolor: 'primary.50',
                                                border: '1px solid',
                                                borderColor: 'primary.main'
                                            }}>
                                                <Avatar 
                                                    src={selectedProduct.image} 
                                                    sx={{ width: 40, height: 40, mr: 2 }}
                                                >
                                                    <Package size={16} />
                                                </Avatar>
                                                <Box sx={{ flex: 1 }}>
                                                    <Typography variant="subtitle2" fontWeight="medium">
                                                        {selectedProduct.name}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary" sx={{
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 1,
                                                        WebkitBoxOrient: 'vertical',
                                                        overflow: 'hidden'
                                                    }}>
                                                        {selectedProduct.description}
                                                    </Typography>
                                                </Box>
                                                <Chip size="small" label="已选择" color="primary" />
                                            </Paper>
                                        );
                                    } else {
                                        return (
                                            <Paper variant="outlined" sx={{ 
                                                p: 3, 
                                                textAlign: 'center',
                                                borderStyle: 'dashed',
                                                bgcolor: 'grey.50'
                                            }}>
                                                <Package size={32} color={theme.palette.text.secondary} style={{ marginBottom: 8 }} />
                                                <Typography variant="body2" color="text.secondary">
                                                    请选择要进行 AI 图文生成的产品
                                                </Typography>
                                            </Paper>
                                        );
                                    }
                                })()}
                            </Paper>
                        </Grid>

                        {/* 素材选择区域 */}
                        <Grid size={{ xs: 12, md: 6 }}>
                            <Paper sx={{ p: 3, height: 'fit-content' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Image size={20} color={theme.palette.primary.main} />
                                        <Typography variant="h6" sx={{ ml: 1, fontWeight: 'medium' }}>
                                            2. 选择素材
                                        </Typography>
                                        {selectedMaterialsCount > 0 && (
                                            <Badge badgeContent={selectedMaterialsCount} color="primary" sx={{ ml: 1 }} />
                                        )}
                                    </Box>
                                    <Button
                                        size="small"
                                        variant="outlined"
                                        onClick={() => toggleDrawer('material')}
                                        startIcon={<Plus size={16} />}
                                    >
                                        选择素材
                                    </Button>
                                </Box>
                                
                                {(() => {
                                    const selectedMaterials = materials.items.filter(m => m.selected);
                                    if (selectedMaterials.length > 0) {
                                        return (
                                            <Stack spacing={1} sx={{ maxHeight: 200, overflow: 'auto' }}>
                                                {selectedMaterials.map(material => (
                                                    <Paper 
                                                        key={material.id}
                                                        variant="outlined" 
                                                        sx={{ 
                                                            p: 1.5, 
                                                            display: 'flex', 
                                                            alignItems: 'center',
                                                            bgcolor: 'primary.50',
                                                            border: '1px solid',
                                                            borderColor: 'primary.main'
                                                        }}
                                                    >
                                                        <Avatar 
                                                            src={material.image} 
                                                            sx={{ width: 32, height: 32, mr: 1.5 }}
                                                        >
                                                            <Image size={14} />
                                                        </Avatar>
                                                        <Box sx={{ flex: 1 }}>
                                                            <Typography variant="subtitle2" fontSize="0.875rem">
                                                                {material.name}
                                                            </Typography>
                                                        </Box>
                                                        <Chip size="small" label="已选" color="primary" sx={{ fontSize: '0.75rem' }} />
                                                    </Paper>
                                                ))}
                                            </Stack>
                                        );
                                    } else {
                                        return (
                                            <Paper variant="outlined" sx={{ 
                                                p: 3, 
                                                textAlign: 'center',
                                                borderStyle: 'dashed',
                                                bgcolor: 'grey.50'
                                            }}>
                                                <Image size={32} color={theme.palette.text.secondary} style={{ marginBottom: 8 }} />
                                                <Typography variant="body2" color="text.secondary">
                                                    请选择参考素材（可多选）
                                                </Typography>
                                            </Paper>
                                        );
                                    }
                                })()}
                            </Paper>
                        </Grid>
                    </Grid>

                    {/* 任务配置区域 */}
                    <Box sx={{ mt: 8, mb: 4, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Typography variant="body2" sx={{ minWidth: 'fit-content' }}>
                                3. 生成数量：
                            </Typography>
                            <TextField
                                value={generateCount}
                                onChange={(e) => {
                                    const value = parseInt(e.target.value, 10);
                                    if (!isNaN(value) && value > 0) {
                                        setGenerateCount(value);
                                    }
                                }}
                                inputProps={{ type: 'number', min: 1, max: 10 }}
                                variant="outlined"
                                size="small"
                                sx={{ width: 100 }}
                            />
                            <Typography variant="body2" color="text.secondary">
                                份 AI 图文内容
                            </Typography>
                        </Box>
                    </Box>

                    {/* 任务预览和创建区域 */}
                    <Box sx={{ borderTop: 1, borderColor: 'divider' }}>
                        {/* 任务预览 */}
                        {canSubmit() && (
                            <Box sx={{ p: 3, bgcolor: 'grey.50' }}>
                                <Typography variant="h6" fontWeight="medium" sx={{ mb: 3 }}>
                                    任务摘要
                                </Typography>
                                
                                {(() => {
                                    const selectedProduct = products.items.find(p => p.selected);
                                    const selectedMaterials = materials.items.filter(m => m.selected);
                                    
                                    return (
                                        <Paper variant="outlined" sx={{ p: 3 }}>
                                            <Typography variant="body1" sx={{ mb: 2 }}>
                                                <strong>任务类型：</strong>AI 图文生成
                                            </Typography>
                                            <Typography variant="body1" sx={{ mb: 2 }}>
                                                <strong>选择产品：</strong>{selectedProduct.name}
                                            </Typography>
                                            <Typography variant="body1" sx={{ mb: 2 }}>
                                                <strong>参考素材：</strong>{selectedMaterials.length} 个素材
                                            </Typography>
                                            <Typography variant="body1" sx={{ mb: 3 }}>
                                                <strong>生成数量：</strong>{generateCount} 份图文内容
                                            </Typography>
                                            
                                            <Typography variant="body2" color="primary.main" sx={{ 
                                                p: 2, 
                                                bgcolor: 'primary.50', 
                                                borderRadius: 1,
                                                border: '1px solid',
                                                borderColor: 'primary.200'
                                            }}>
                                                <strong>注意事项：</strong>AI 图文生成需要一定时间处理，任务创建后请在任务列表中查看生成进度和结果。生成完成后可直接用于推广活动。
                                            </Typography>
                                        </Paper>
                                    );
                                })()}
                            </Box>
                        )}
                        
                        {/* 任务创建区域 */}
                        <Box sx={{ p: 3, textAlign: 'center' }}>
                            {canSubmit() ? (
                                <Box>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                                        ✓ 配置完成，请确认上述预览信息无误后创建任务。
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        onClick={submitTask}
                                        disabled={isSubmitting}
                                        size="large"
                                        startIcon={<Send size={18} />}
                                        sx={{ minWidth: 200 }}
                                    >
                                        {isSubmitting ? "创建中..." : "创建任务"}
                                    </Button>
                                </Box>
                            ) : (
                                <Box>
                                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                        请按照上方步骤完成配置后创建任务
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        disabled
                                        size="large"
                                        sx={{ minWidth: 200 }}
                                    >
                                        创建任务
                                    </Button>
                                </Box>
                            )}
                        </Box>
                    </Box>
                </Paper>
            </Container>

            {/* 产品选择抽屉 */}
            <Drawer
                anchor={isMobile ? 'bottom' : 'right'}
                open={drawers.product}
                onClose={() => toggleDrawer('product')}
                PaperProps={{
                    sx: {
                        height: isMobile ? '90vh' : '100vh',
                        borderTopLeftRadius: isMobile ? 16 : 0,
                        borderTopRightRadius: isMobile ? 16 : 0
                    }
                }}
            >
                <SelectionDrawer
                    type="product"
                    open={drawers.product}
                    onClose={() => toggleDrawer('product')}
                    isMobile={isMobile}
                    data={products}
                    onToggle={handleProductToggle}
                    theme={theme}
                    router={router}
                />
            </Drawer>

            {/* 素材选择抽屉 */}
            <Drawer
                anchor={isMobile ? 'bottom' : 'right'}
                open={drawers.material}
                onClose={() => toggleDrawer('material')}
                PaperProps={{
                    sx: {
                        height: isMobile ? '90vh' : '100vh',
                        borderTopLeftRadius: isMobile ? 16 : 0,
                        borderTopRightRadius: isMobile ? 16 : 0
                    }
                }}
            >
                <SelectionDrawer
                    type="material"
                    open={drawers.material}
                    onClose={() => toggleDrawer('material')}
                    isMobile={isMobile}
                    data={materials}
                    onToggle={handleMaterialToggle}
                    theme={theme}
                    router={router}
                />
            </Drawer>
        </>
    );
}