import time
from decimal import Decimal
from typing import Any, Dict

from beanie import PydanticObjectId

from models.models import CostRechargeRecord, CostUserAccount
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.log.log import olog
from utils.cost_helper import process_recharge


@register_handler('cost_recharge_record')
class CostRechargeRecordApi:
    @auth_required(['admin'])
    async def create(self, data: Dict[str, Any]):
        """创建充值记录"""
        cost_user_account_id = data.get('cost_user_account_id')
        raw_amount = data.get('amount')
        status = data.get('status', '待处理')
        recharge_method_input = data.get('recharge_method')

        # 输入验证
        if not cost_user_account_id:
            raise MException("cost_user_account_id 不能为空")
        if raw_amount is None:
            raise MException("amount 不能为空")

        amount = Decimal(str(raw_amount))

        if status == '成功':
            # process_recharge 会处理 CostUserAccount 的获取或创建，并更新余额
            process_recharge_kwargs = {
                'user_id': cost_user_account_id,
                'amount': amount
            }
            if recharge_method_input is not None:
                process_recharge_kwargs['recharge_method'] = recharge_method_input

            # 假设 process_recharge 是异步的
            result = await process_recharge(**process_recharge_kwargs)
            olog.info(
                f"通过 cost_helper.process_recharge 成功处理充值，记录ID: {result['recharge_record_id']}，用户账户ID: {cost_user_account_id}, 金额: {amount}")

        else:
            # 对于非 '成功' 状态 (如 '待处理')，按原逻辑创建记录
            # 必须确保关联的 CostUserAccount 存在
            user_account = await CostUserAccount.find_one(CostUserAccount.id == PydanticObjectId(cost_user_account_id))
            if not user_account:
                raise MException(
                    f"用户账户ID {cost_user_account_id} 不存在 (当充值状态为 '{status}' 时，账户必须已存在)")

            current_time = int(time.time())
            record_data = {
                'cost_user_account_id': cost_user_account_id,
                'amount': float(amount),
                'status': status,
                'created_at': current_time,
                'updated_at': current_time
            }
            # 如果提供了充值方式，则使用；否则，让模型使用其默认值
            if recharge_method_input is not None:
                record_data['recharge_method'] = recharge_method_input

            new_record = CostRechargeRecord(**record_data)
            await new_record.insert()
            olog.info(
                f"创建充值记录成功，ID: {new_record.id}，用户账户ID: {cost_user_account_id}, 金额: {amount}, 状态: {status}")

    @auth_required(['admin'])
    async def query_one(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """查询单个充值记录"""
        id_ = data.get('_id')

        record = await CostRechargeRecord.find_one(CostRechargeRecord.id == PydanticObjectId(id_))
        if not record:
            raise MException(f"ID为 {id_} 的充值记录不存在")
        return record.to_dict()

    @auth_required(['admin'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """查询充值记录列表 (可按用户账户ID和状态筛选)"""
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        cost_user_account_id = data.get('cost_user_account_id')  # 筛选条件
        status_filter = data.get('status')  # 筛选条件

        query_conditions = []
        if cost_user_account_id:
            query_conditions.append(CostRechargeRecord.cost_user_account_id == cost_user_account_id)
        if status_filter:
            query_conditions.append(CostRechargeRecord.status == status_filter)

        query = CostRechargeRecord.find(*query_conditions)

        records = await query.sort(-CostRechargeRecord.created_at).skip((page - 1) * page_size).limit(
            page_size).to_list()

        # 计算总条数
        total = await CostRechargeRecord.find(*query_conditions).count()

        record_dicts = [record.to_dict() for record in records]
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=record_dicts
        )
