import api from "@/core/api/api";

const RESOURCE = "adv_ai_agent_api";

export const advAiAgentApi = {
    generateDescription: async (imageUrl) => {
        return await api({
            resource: RESOURCE,
            method_name: "generate_description",
            data: {
                image_url: imageUrl,
            },
        });
    },

    identifyProductName: async (imageUrl) => {
        return await api({
            resource: RESOURCE,
            method_name: "identify_product_name", // 对应后端方法名
            data: {
                image_url: imageUrl,
            },
        });
    },

    identifyAndSaveDomains: async (imageUrl) => {
        return await api({
            resource: RESOURCE,
            method_name: "identify_and_save_domains",
            data: {
                image_url: imageUrl,
            },
        });
    },
};
