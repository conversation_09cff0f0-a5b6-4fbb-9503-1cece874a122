import api from "@/core/api/api";

const RESOURCE = "adv_cost_api";

export const advCostApi = {
    getUserBalance: async () => {
        return await api({
            resource: RESOURCE,
            method_name: "get_user_balance",
            data: {},
        });
    },

    queryRechargeRecords: async ({page = 1, page_size = 10, status} = {}) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_recharge_records",
            data: {
                page: page,
                page_size: page_size,
                status: status,
            },
        });
    },

    queryConsumptionRecords: async ({page = 1, page_size = 10} = {}) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_consumption_records",
            data: {
                page: page,
                page_size: page_size,
            },
        });
    }
};

