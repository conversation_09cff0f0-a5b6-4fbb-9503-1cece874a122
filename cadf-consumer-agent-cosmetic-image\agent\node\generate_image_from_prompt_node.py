from typing import Dict, Any

from agent.utils.openai_image_edit import OpenaiImageEditor
from agent.state import OverallState
from agent.utils.temp_file_manager import save_temp_pic
from omni.log.log import olog


async def generate_image_from_prompt(state: OverallState) -> Dict[str, Any]:
    """从提示词生成图片节点"""
    olog.info("开始执行图片生成节点")
    olog.debug(f"输入提示词: {state.generated_prompt}")
    
    generated_prompt = state.generated_prompt
    
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    image_bytes, error_type = await editor.generate_image_from_prompt(prompt=generated_prompt)
    
    temp_path = await save_temp_pic(image_bytes)
    olog.info(f"图片生成完成，保存到: {temp_path}")
    
    return {
        "processed_image_path": temp_path
    }