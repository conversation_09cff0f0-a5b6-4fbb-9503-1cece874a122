import api from "@/core/api/api";

const RESOURCE = "adv_ai_generated_material_api";

export const advAiGeneratedMaterialApi = {
    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    },

    queryByTaskId: async (taskId, page = 1, page_size = 10, imageGenerationStatus, textGenerationStatus) => {
        const data = {
            ai_generation_task_id: taskId,
            page: page,
            page_size: page_size,
        };
        
        if (imageGenerationStatus) {
            data.image_generation_status = imageGenerationStatus;
        }
        if (textGenerationStatus) {
            data.text_generation_status = textGenerationStatus;
        }
        
        return await api({
            resource: RESOURCE,
            method_name: "query_by_task_id",
            data: data,
        });
    },

    queryById: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_by_id",
            data: {
                _id: id_,
            },
        });
    },

    regenerateFailedMaterials: async (taskId) => {
        return await api({
            resource: RESOURCE,
            method_name: "regenerate_failed_materials",
            data: {
                ai_generation_task_id: taskId,
            },
        });
    },

    regenerateImage: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "regenerate_image",
            data: {
                _id: id_,
            },
        });
    },

    regenerateText: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "regenerate_text",
            data: {
                _id: id_,
            },
        });
    },
}; 