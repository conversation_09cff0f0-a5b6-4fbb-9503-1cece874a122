"use client";

import {useCallback, useEffect, useState} from 'react';
import {
    Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, 
    Divider, IconButton, Typography, Stack, Grid, TextField, InputAdornment
} from '@mui/material';
import {Plus, RefreshCw, Search, Trash2} from 'lucide-react';
import ImageCard from '@/components/ImageCard';
import {advProductApi} from '@/api/adv-product-api';
import {useSnackbar} from 'notistack';
import InfiniteScrollList from '@/core/components/InfiniteScrollList';
import {useRouter} from 'next/navigation';

export default function ProductManagement() {
    const router = useRouter();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [totalProducts, setTotalProducts] = useState(0);
    const [search, setSearch] = useState('');
    const [apiSearchTerm, setApiSearchTerm] = useState('');
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [rowsPerPage, setRowsPerPage] = useState(4);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);
    const {enqueueSnackbar} = useSnackbar();

    const handleSearchChange = (event) => {
        setSearch(event.target.value);
    };

    const handleSearchKeyDown = (event) => {
        if (event.key === 'Enter') {
            handleFilter();
        }
    };

    const handleViewProduct = (product) => {
        router.push(`/protected/product-management/${product.id}`);
    };

    const handleDeleteClick = (event, product) => {
        event.stopPropagation();
        setProductToDelete(product);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!productToDelete) return;
        try {
            await advProductApi.delete(productToDelete.id);
            setDeleteDialogOpen(false);
            setProductToDelete(null);
            resetAndFetchProducts();
            enqueueSnackbar("产品删除成功", {variant: 'success'});
        } catch (err) {
            console.error("Failed to delete product:", err);
            enqueueSnackbar(`删除失败: ${err.message || '未知错误'}`, {variant: 'error'});
        }
    };

    const handleDeleteCancel = () => {
        setDeleteDialogOpen(false);
        setProductToDelete(null);
    };

    const handleAddProduct = () => {
        router.push('/protected/product-management/new');
    };

    const fetchProducts = useCallback(async (currentPage = 1) => {
        setLoading(true);
        setError(null);
        try {
            const response = await advProductApi.query_all(apiSearchTerm, currentPage, rowsPerPage);
            const formattedProducts = (response.results || []).map(p => {
                // 适配新的数据格式，image_url替代了images数组
                const imageObjects = p.image_url ? [{
                    id: 0,
                    name: `产品图片 ${p._id}`,
                    size: 'N/A',
                    url: p.image_url,
                    oss_key: null,
                    order: 0,
                }] : [];

                return {
                    id: p._id,
                    title: p.title || `产品 ${p._id}`,
                    images: imageObjects,
                    domain: p.domain?.join(',') || '无',
                    description: p.description || '暂无描述',
                    date: p.create_at ? new Date(p.create_at * 1000).toLocaleDateString() : '未知日期',
                };
            });
            
            if (currentPage === 1) {
                setProducts(formattedProducts);
            } else {
                setProducts(prev => [...prev, ...formattedProducts]);
            }
            
            setTotalProducts(response.total || 0);
            // 使用返回的分页信息来判断是否还有更多数据
            const totalPages = Math.ceil((response.total || 0) / (response.page_size || rowsPerPage));
            setHasMore((response.page || currentPage) < totalPages);
            setPage(response.page || currentPage);
        } catch (err) {
            console.error("Failed to fetch products:", err);
            setError(err.message || '获取产品数据失败');
            if (currentPage === 1) {
                setProducts([]);
            }
            setHasMore(false);
            setTotalProducts(0);
            enqueueSnackbar(`加载产品失败: ${err.message || '未知错误'}`, {variant: 'error'});
        } finally {
            setLoading(false);
        }
    }, [apiSearchTerm, rowsPerPage, enqueueSnackbar]);

    const loadMoreProducts = useCallback(async () => {
        if (loading) return;
        
        try {
            const nextPage = page + 1;
            await fetchProducts(nextPage);
        } catch (err) {
            console.error("加载更多产品失败:", err);
        }
    }, [page, fetchProducts, loading]);

    const resetAndFetchProducts = useCallback(() => {
        setPage(1);
        setHasMore(true);
        fetchProducts(1);
    }, [fetchProducts]);

    useEffect(() => {
        resetAndFetchProducts();
    }, [apiSearchTerm, resetAndFetchProducts]);

    const handleFilter = () => {
        setApiSearchTerm(search);
    };

    const renderProduct = (product) => {
        return (
            <ImageCard
                image={product.images?.[0]?.url}
                onClick={() => handleViewProduct(product)}
            >
                <Stack sx={{p: 2, height: '100%'}} spacing={1}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Typography variant="subtitle1" sx={{fontWeight: 600, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flexGrow: 1, mr: 1}}>
                            {product.title}
                        </Typography>
                        <IconButton
                            size="small"
                            onClick={(event) => handleDeleteClick(event, product)}
                            sx={{color: 'error.main'}}
                            aria-label="delete product"
                        >
                            <Trash2 size={16}/>
                        </IconButton>
                    </Stack>

                    {product.domain && (
                        <Typography variant="body2" color="text.secondary">
                            <strong>领域:</strong> {product.domain}
                        </Typography>
                    )}

                    {product.description && (
                        <Typography
                            variant="body2"
                            sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                height: 40,
                                lineHeight: '20px'
                            }}
                        >
                            {product.description}
                        </Typography>
                    )}

                    <Box sx={{mt: 'auto'}}>
                        <Typography variant="caption" color="text.secondary">
                            {product.date}
                        </Typography>
                    </Box>
                </Stack>
            </ImageCard>
        );
    };

    return (
        <Box sx={{py: 3}}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h4" component="h1" sx={{fontWeight: 600}}>
                    产品管理
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<Plus size={18}/>}
                    onClick={handleAddProduct}
                >
                    添加产品
                </Button>
            </Stack>

            <Box sx={{mb: 4}}>
                <Stack direction={{xs: 'column', sm: 'row'}} spacing={2} alignItems={{xs: 'stretch', sm: 'center'}} mb={2}>
                    <TextField
                        placeholder="搜索产品标题或描述"
                        size="small"
                        value={search}
                        onChange={handleSearchChange}
                        onKeyDown={handleSearchKeyDown}
                        sx={{flexGrow: 1, maxWidth: {sm: 300}}}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton
                                        edge="end"
                                        onClick={handleFilter}
                                        size="small"
                                    >
                                        <Search size={18}/>
                                    </IconButton>
                                </InputAdornment>
                            )
                        }}
                    />
                    <IconButton
                        onClick={resetAndFetchProducts}
                        size="small"
                        sx={{
                            border: `1px solid #e0e0e0`,
                            borderRadius: 1,
                        }}
                    >
                        <RefreshCw size={18}/>
                    </IconButton>
                </Stack>
                <Divider />
            </Box>

            {loading && page === 1 ? (
                <Typography sx={{textAlign: 'center', my: 4}}>加载中...</Typography>
            ) : error && page === 1 ? (
                <Typography color="error" sx={{textAlign: 'center', my: 4}}>错误: {error}</Typography>
            ) : products.length === 0 && !loading ? (
                <Typography sx={{textAlign: 'center', my: 4}}>没有找到产品。</Typography>
            ) : (
                <InfiniteScrollList 
                    items={products}
                    renderItem={renderProduct}
                    loadMore={loadMoreProducts}
                    hasMore={hasMore}
                />
            )}

            <Dialog
                open={deleteDialogOpen}
                onClose={handleDeleteCancel}
                aria-labelledby="alert-dialog-title"
            >
                <DialogTitle id="alert-dialog-title">
                    确定要删除此产品吗？
                </DialogTitle>
                <DialogContent>
                    <Typography variant="body2">
                        删除后将无法恢复，请确认您的操作。
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleDeleteCancel} color="primary">
                        取消
                    </Button>
                    <Button onClick={handleDeleteConfirm} color="error" autoFocus>
                        删除
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}
