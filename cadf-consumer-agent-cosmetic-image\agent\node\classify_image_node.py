from typing import Dict, Any
from typing import Literal

from omni.llm.output_agent import structured_output_handler
from pydantic import BaseModel, Field

from agent.state import OverallState
from omni.log.log import olog


class ImageCategoryResult(BaseModel):
    category: Literal["美妆产品", "人像", "其他"] = Field(..., description="图片类别：美妆产品/人像/其他")
    description: str = Field(..., description="图片主要内容描述")


async def classify_image(state: OverallState) -> Dict[str, Any]:
    """分类图片类别节点"""
    olog.info("开始执行图片分类节点")
    
    image_path = state.scene_image_path
    
    prompt = """
# 角色
你是一位专注于电商图片分析的AI视觉专家。

# 任务
请判断输入图片的主要内容类别，只能从以下三类中选择：
1. 美妆产品: 图片主要展示为化妆品、护肤品等美妆相关产品实物，且必须有明显的瓶子、包装或其他容器。如果仅展示液体（如洗发膏的液体）或无包装，则判断为其他。
2. 人像: 图片主要为人物面部、半身或全身，且不是以产品为主。
3. 其他: 不属于上述两类的图片。

# 返回值
- category: 只能为"美妆产品"、"人像"或"其他"
- description: 用一句话描述图片主要内容
"""

    agent_structured_output = await structured_output_handler(
        prompt_template=prompt,
        params={},
        output_model=ImageCategoryResult,
        llm_name="QWEN_VL_MAX",
        image_path=image_path
    )
    
    category = agent_structured_output.category
    olog.info(f"图片分类结果: {category}")
    
    return {
        "image_category": category
    }