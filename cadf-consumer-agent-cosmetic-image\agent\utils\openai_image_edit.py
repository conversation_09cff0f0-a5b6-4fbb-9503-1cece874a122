import asyncio
import base64
import io

from openai import AsyncOpenAI, RateLimitError, InternalServerError, BadRequestError

from omni.config.config_loader import config_dict
from omni.log.log import olog


class OpenaiImageEditor:
    def __init__(self, llm_config_key: str):
        olog.info(f"初始化 OpenaiImageEditor，配置键: '{llm_config_key}'")
        self.llm_config = config_dict["llm"][llm_config_key]
        self.api_key = self.llm_config["api_key"]
        self.model_name = self.llm_config["model_name"]
        
        api_base = self.llm_config["api_base"]
        if not api_base.endswith("/"):
            api_base += "/"
            
        self.client = AsyncOpenAI(api_key=self.api_key, base_url=api_base)
        olog.info(f"OpenAI 客户端初始化成功，模型: {self.model_name}")

    async def _call_with_retry(self, api_func) -> tuple[list[bytes], str]:
        while True:
            try:
                result = await api_func()
                results = [base64.b64decode(image_item.b64_json) for image_item in result.data]
                return results, "success"
            except RateLimitError:
                olog.debug("速率限制，等待 1 秒后重试")
                await asyncio.sleep(1)
            except InternalServerError:
                olog.debug("服务器内部错误，等待 1 秒后重试")
                await asyncio.sleep(1)
            except BadRequestError as e:
                if "moderation_blocked" in str(e):
                    olog.warning("请求被安全机制拒绝")
                    return [], "security_blocked"
                olog.exception("请求错误")
                return [], "unknown_error"
            except Exception:
                olog.exception("未知异常")
                return [], "unknown_error"

    async def generate_image_from_image(
            self, image_bytes_list: list[bytes], prompt: str, size: str = "1024x1536"
    ) -> tuple[bytes, str]:
        olog.info(f"开始批量编辑 {len(image_bytes_list)} 张图片")
        image_file_objs = [io.BytesIO(img_bytes) for img_bytes in image_bytes_list]
        results, error_type = await self._call_with_retry(
            lambda: self.client.images.edit(
                model=self.model_name,
                image=image_file_objs,
                prompt=prompt,
                size=size,
                n=1,
                quality="auto",
            )
        )
        result = results[0] if results else None
        olog.info("图片编辑完成")
        return result, error_type

    async def generate_image_from_prompt(self, prompt: str, size: str = "1024x1536") -> tuple[bytes, str]:
        olog.info("开始生成图片")
        results, error_type = await self._call_with_retry(
            lambda: self.client.images.generate(
                model=self.model_name,
                prompt=prompt,
                size=size,
                n=1,
                quality="auto",
            )
        )
        result = results[0] if results else None
        olog.info("图片生成完成")
        return result, error_type
