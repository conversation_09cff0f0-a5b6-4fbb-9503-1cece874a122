from typing import Dict, Any
import asyncio

from agent.utils.openai_image_edit import OpenaiImageEditor
from agent.state import OverallState
from agent.utils.temp_file_manager import save_temp_pic
from omni.log.log import olog


async def clone_image(state: OverallState) -> Dict[str, Any]:
    """克隆图片节点"""
    olog.info("开始执行图片克隆节点")
    
    
    image_path = state.scene_image_path
    
    prompt_clean_scene = """
    移除图片中所有艺术字体，表情，帖纸，表情包，图标，插画，装饰图案等非自然元素，确保场景干净。
    """
    
    # 读取图片文件为bytes
    async def read_image_file(path: str) -> bytes:
        def _read():
            with open(path, "rb") as f:
                return f.read()
        return await asyncio.to_thread(_read)
    
    image_bytes = await read_image_file(image_path)
    
    editor = OpenaiImageEditor(llm_config_key="GPT_IMAGE")
    edited_image_data, error_type = editor.generate_image_from_image(
        image_bytes_list=[image_bytes],
        prompt=prompt_clean_scene,
    )

    # 保存编辑后的图片到临时文件
    temp_path = await save_temp_pic(edited_image_data)
    olog.info(f"图片克隆完成，保存到: {temp_path}")
    
    return {
        "processed_image_path": temp_path
    }