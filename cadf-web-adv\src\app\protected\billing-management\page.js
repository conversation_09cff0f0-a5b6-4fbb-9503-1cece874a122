"use client";

import {advCostApi} from "@/api/adv-cost-api";
import {useSnackbar} from "notistack";
import {Box, Button, Card, CardContent, Chip, CircularProgress, Container, Grid, Paper, Stack, Tab, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Typography,} from "@mui/material";
import {useTheme} from "@mui/material/styles";
import dayjs from "dayjs";
import {CreditCard, FileText, RefreshCw} from "lucide-react";
import {useCallback, useEffect, useState} from "react";
import InfiniteScrollList from "@/core/components/InfiniteScrollList";

const RechargeRecordStatusColors = {
    成功: "success",
    待处理: "warning",
    失败: "error",
};

export default function BillingManagementPage() {
    const theme = useTheme();
    const {enqueueSnackbar} = useSnackbar();

    const [currentBalance, setCurrentBalance] = useState(0);
    const [loadingBalance, setLoadingBalance] = useState(true);
    const [tabValue, setTabValue] = useState(0);

    const [allRechargeRecords, setAllRechargeRecords] = useState([]);
    const [rechargeNextPage, setRechargeNextPage] = useState(1);
    const [rechargeHasMore, setRechargeHasMore] = useState(true);
    const [rechargeInitialLoad, setRechargeInitialLoad] = useState(false);

    const [allConsumptionRecords, setAllConsumptionRecords] = useState([]);
    const [consumptionNextPage, setConsumptionNextPage] = useState(1);
    const [consumptionHasMore, setConsumptionHasMore] = useState(true);
    const [consumptionInitialLoad, setConsumptionInitialLoad] = useState(false);

    const rowsPerPage = 10;

    const fetchBalance = useCallback(async () => {
        setLoadingBalance(true);
        try {
            const response = await advCostApi.getUserBalance();
            setCurrentBalance(response.balance || 0);
        } catch (err) {
            console.error("Error fetching balance:", err);
            enqueueSnackbar(err.message || "获取余额失败", {variant: "error"});
            setCurrentBalance(0);
        } finally {
            setLoadingBalance(false);
        }
    }, [enqueueSnackbar]);

    const loadMoreRechargeRecords = useCallback(async () => {
        try {
            const response = await advCostApi.queryRechargeRecords({
                page: rechargeNextPage,
                page_size: rowsPerPage,
            });
            const newRecords = response.results || [];
            setAllRechargeRecords(prev => [...prev, ...newRecords]);
            setRechargeNextPage(prev => prev + 1);
            setRechargeHasMore(newRecords.length === rowsPerPage);
        } catch (err) {
            console.error("Error loading more recharge records:", err);
            enqueueSnackbar(err.message || "加载充值记录失败", {variant: "error"});
            throw err;
        }
    }, [enqueueSnackbar, rechargeNextPage]);

    const resetRechargeRecords = useCallback(() => {
        setAllRechargeRecords([]);
        setRechargeNextPage(1);
        setRechargeHasMore(true);
        setRechargeInitialLoad(false);
    }, []);

    const loadMoreConsumptionRecords = useCallback(async () => {
        try {
            const response = await advCostApi.queryConsumptionRecords({
                page: consumptionNextPage,
                page_size: rowsPerPage,
            });
            const newRecords = response.results || [];
            setAllConsumptionRecords(prev => [...prev, ...newRecords]);
            setConsumptionNextPage(prev => prev + 1);
            setConsumptionHasMore(newRecords.length === rowsPerPage);
        } catch (err) {
            console.error("Error loading more consumption records:", err);
            enqueueSnackbar(err.message || "加载消费记录失败", {variant: "error"});
            throw err;
        }
    }, [enqueueSnackbar, consumptionNextPage]);

    const resetConsumptionRecords = useCallback(() => {
        setAllConsumptionRecords([]);
        setConsumptionNextPage(1);
        setConsumptionHasMore(true);
        setConsumptionInitialLoad(false);
    }, []);

    useEffect(() => {
        fetchBalance();
    }, [fetchBalance]);

    useEffect(() => {
        if (tabValue === 0 && !rechargeInitialLoad) {
            setRechargeInitialLoad(true);
            loadMoreRechargeRecords();
        } else if (tabValue === 1 && !consumptionInitialLoad) {
            setConsumptionInitialLoad(true);
            loadMoreConsumptionRecords();
        }
    }, [tabValue, rechargeInitialLoad, consumptionInitialLoad, loadMoreRechargeRecords, loadMoreConsumptionRecords]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return "N/A";
        return dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm:ss");
    };

    const RechargeRecordItem = ({ record }) => (
        <Card 
            elevation={0} 
            sx={{ 
                borderRadius: 3, 
                border: `1px solid ${theme.palette.grey[200]}`, 
                boxShadow: theme.shadows[1],
                mb: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                    boxShadow: theme.shadows[2],
                    borderColor: theme.palette.primary.light,
                }
            }}
        >
            <CardContent sx={{ p: 3 }}>
                <Grid container spacing={2} alignItems="center" sx={{ width: '100%' }}>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            时间
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {formatTimestamp(record.created_at)}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            金额 (元)
                        </Typography>
                        <Typography variant="body1" sx={{ color: theme.palette.success.main, fontWeight: 600 }}>
                            +{parseFloat(record.amount).toFixed(2)}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            方式
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.recharge_method}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            状态
                        </Typography>
                        <Chip
                            label={record.status}
                            size="small"
                            color={RechargeRecordStatusColors[record.status] || "default"}
                            sx={{ mt: 0.5 }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            ID
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500, wordBreak: 'break-all' }}>
                            {record.id_}
                        </Typography>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );

    const renderRechargeList = () => (
        <InfiniteScrollList
            items={allRechargeRecords}
            renderItem={(record) => <RechargeRecordItem key={record.id_} record={record} />}
            loadMore={loadMoreRechargeRecords}
            hasMore={rechargeHasMore}
            gridColumns={{ xs: 12 }}
        />
    );

    const ConsumptionRecordItem = ({ record }) => (
        <Card 
            elevation={0} 
            sx={{ 
                borderRadius: 3, 
                border: `1px solid ${theme.palette.grey[200]}`, 
                boxShadow: theme.shadows[1],
                mb: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                    boxShadow: theme.shadows[2],
                    borderColor: theme.palette.primary.light,
                }
            }}
        >
            <CardContent sx={{ p: 3 }}>
                <Grid container spacing={2} alignItems="center" sx={{ width: '100%' }}>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            时间
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {formatTimestamp(record.created_at)}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            项目
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.project_name}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            金额 (元)
                        </Typography>
                        <Typography variant="body1" sx={{ color: theme.palette.error.main, fontWeight: 600 }}>
                            -{parseFloat(record.amount).toFixed(2)}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            描述
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.description}
                        </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                            ID
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500, wordBreak: 'break-all' }}>
                            {record.id_}
                        </Typography>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );

    const renderConsumptionList = () => (
        <InfiniteScrollList
            items={allConsumptionRecords}
            renderItem={(record) => <ConsumptionRecordItem key={record.id_} record={record} />}
            loadMore={loadMoreConsumptionRecords}
            hasMore={consumptionHasMore}
            gridColumns={{ xs: 12 }}
        />
    );

    return (
        <Container maxWidth="lg" sx={{py: 3}}>
            <Stack spacing={3}>
                <Paper elevation={0} sx={{borderRadius: 3, border: `1px solid ${theme.palette.grey[200]}`, boxShadow: theme.shadows[1]}}>
                    <Grid container alignItems="center" sx={{p: 4, width: '100%'}}>
                        <Grid size={12}>
                            <Typography variant="h5" component="h1" gutterBottom sx={{fontWeight: 500, color: theme.palette.text.primary}}>
                                费用与账单
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 400 }}>
                                管理您的账户余额、查看充值与消费记录。
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>

                <Card elevation={0} sx={{borderRadius: 3, border: `1px solid ${theme.palette.grey[200]}`, boxShadow: theme.shadows[1]}}>
                    <CardContent sx={{p: 4}}>
                        <Grid container spacing={4} alignItems="center" sx={{width: '100%'}}>
                            <Grid size={{xs: 12, sm: 6}}>
                                <Typography variant="h6" gutterBottom sx={{ fontWeight: 500 }}>
                                    账户余额
                                </Typography>
                                {loadingBalance ? (
                                    <CircularProgress size={24}/>
                                ) : (
                                    <Typography variant="h3" component="div" sx={{fontWeight: 500, color: theme.palette.primary.main}}>
                                        ¥ {parseFloat(currentBalance).toFixed(2)}
                                    </Typography>
                                )}
                            </Grid>
                            <Grid size={{xs: 12, sm: 6}} sx={{display: "flex", justifyContent: "flex-end"}}>
                                <Button
                                    startIcon={<RefreshCw size={16}/>}
                                    onClick={fetchBalance}
                                    disabled={loadingBalance}
                                    variant="outlined"
                                    sx={{
                                        borderRadius: 2,
                                        textTransform: 'none',
                                        fontWeight: 500,
                                        px: 3,
                                    }}
                                >
                                    刷新余额
                                </Button>
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Paper elevation={0} sx={{borderRadius: 3, border: `1px solid ${theme.palette.grey[200]}`, boxShadow: theme.shadows[1]}}>
                    <Tabs
                        value={tabValue}
                        onChange={handleTabChange}
                        indicatorColor="primary"
                        textColor="primary"
                        variant="fullWidth"
                        sx={{borderBottom: 1, borderColor: "divider"}}
                    >
                        <Tab 
                            icon={<CreditCard/>} 
                            iconPosition="start" 
                            label={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <span>充值记录</span>
                                    <Button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            resetRechargeRecords();
                                        }}
                                        variant="text"
                                        size="small"
                                        sx={{
                                            minWidth: 'auto',
                                            width: 24,
                                            height: 24,
                                            color: theme.palette.text.secondary,
                                            ml: 1,
                                            p: 0,
                                            '&:hover': {
                                                color: theme.palette.primary.main,
                                                backgroundColor: theme.palette.action.hover,
                                            }
                                        }}
                                    >
                                        <RefreshCw size={14}/>
                                    </Button>
                                </Box>
                            }
                        />
                        <Tab 
                            icon={<FileText/>} 
                            iconPosition="start" 
                            label={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <span>消费记录</span>
                                    <Button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            resetConsumptionRecords();
                                        }}
                                        variant="text"
                                        size="small"
                                        sx={{
                                            minWidth: 'auto',
                                            width: 24,
                                            height: 24,
                                            color: theme.palette.text.secondary,
                                            ml: 1,
                                            p: 0,
                                            '&:hover': {
                                                color: theme.palette.primary.main,
                                                backgroundColor: theme.palette.action.hover,
                                            }
                                        }}
                                    >
                                        <RefreshCw size={14}/>
                                    </Button>
                                </Box>
                            }
                        />
                    </Tabs>
                    <Box sx={{p: 3}}>
                        {tabValue === 0 && renderRechargeList()}
                        {tabValue === 1 && renderConsumptionList()}
                    </Box>
                </Paper>
            </Stack>
        </Container>
    );
} 