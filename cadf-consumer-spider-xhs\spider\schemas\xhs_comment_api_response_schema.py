from typing import List, Optional

from pydantic import BaseModel


class UserInfo(BaseModel):
    user_id: str
    nickname: str
    image: str


class TargetComment(BaseModel):
    id: str
    user_info: UserInfo


class SubComment(BaseModel):
    id: str
    note_id: str
    content: str
    create_time: int
    ip_location: Optional[str] = None
    like_count: str
    liked: bool
    user_info: UserInfo
    target_comment: Optional[TargetComment] = None


class Comment(BaseModel):
    id: str
    note_id: str
    content: str
    create_time: int
    ip_location: Optional[str] = None
    like_count: str
    liked: bool
    user_info: UserInfo
    sub_comment_count: str
    sub_comments: List[SubComment]
    sub_comment_has_more: bool
    sub_comment_cursor: Optional[str] = None


class CommentData(BaseModel):
    cursor: Optional[str] = None
    has_more: bool
    comments: List[Comment]


class CommentApiResponse(BaseModel):
    code: int
    success: bool
    msg: str
    data: CommentData
