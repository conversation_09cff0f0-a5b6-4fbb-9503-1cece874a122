"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import {
  Box,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  FormHelperText,
  Paper,
  Stack,
} from "@mui/material";
import { ArrowLeft } from "lucide-react";
import { userApi } from "@/api/user-api";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const roleOptions = [
  { value: "user", label: "普通用户" },
  { value: "creator", label: "流量主" },
  { value: "captain", label: "舰长" },
  { value: "advertiser", label: "广告主" },
  { value: "admin", label: "超级管理员" },
];

export default function CreateUser() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    roles: [],
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 处理表单输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // 清除错误
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  // 处理角色选择变化
  const handleRolesChange = (event) => {
    const {
      target: { value },
    } = event;
    setFormData({
      ...formData,
      roles: typeof value === "string" ? value.split(",") : value,
    });
    if (errors.roles) {
      setErrors({
        ...errors,
        roles: null,
      });
    }
  };

  // 表单验证
  const validateForm = () => {
    const newErrors = {};
    if (!formData.username) {
      newErrors.username = "用户名不能为空";
    }
    if (!formData.password) {
      newErrors.password = "密码不能为空";
    }
    if (formData.roles.length === 0) {
      newErrors.roles = "请至少选择一个角色";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 保存用户
  const handleSaveUser = async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      await userApi.create(formData);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.CREATE }));
      router.push("/protected/user-management");
    } catch (error) {
      console.error("创建用户失败", error);
      dispatch(addAlert({ type: AlertType.ERROR, message: error.message || "创建用户失败" }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 返回用户列表
  const handleBack = () => {
    router.push("/protected/user-management");
  };

  return (
    <Box sx={{ 
      p: 3, 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center',
      minHeight: '100vh'
    }}>
      <Box sx={{ width: '100%', maxWidth: 600 }}>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3, cursor: "pointer" }} onClick={handleBack}>
          <ArrowLeft size={20} />
          <Typography variant="subtitle1">返回用户列表</Typography>
        </Stack>

        <Typography variant="h4" component="h1" sx={{ mb: 4, textAlign: 'center' }}>
          添加用户
        </Typography>

        <Paper sx={{ p: 3 }}>
        <Box component="form" sx={{ mt: 1 }}>
          <TextField
            margin="normal"
            fullWidth
            label="用户名"
            name="username"
            value={formData.username}
            onChange={handleInputChange}
            error={!!errors.username}
            helperText={errors.username}
          />
          <TextField
            margin="normal"
            fullWidth
            label="密码"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            error={!!errors.password}
            helperText={errors.password}
          />
          <FormControl fullWidth margin="normal" error={!!errors.roles}>
            <InputLabel id="roles-label">角色</InputLabel>
            <Select
              labelId="roles-label"
              multiple
              value={formData.roles}
              onChange={handleRolesChange}
              input={<OutlinedInput label="角色" />}
              renderValue={(selected) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip 
                      key={value} 
                      label={roleOptions.find(option => option.value === value)?.label || value} 
                    />
                  ))}
                </Box>
              )}
              MenuProps={MenuProps}
            >
              {roleOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {errors.roles && <FormHelperText>{errors.roles}</FormHelperText>}
          </FormControl>

          <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
            <Button 
              onClick={handleBack} 
              sx={{ mr: 1 }}
            >
              取消
            </Button>
            <Button 
              onClick={handleSaveUser} 
              variant="contained"
              disabled={isSubmitting}
            >
              保存
            </Button>
          </Box>
        </Box>
        </Paper>
      </Box>
    </Box>
  );
} 