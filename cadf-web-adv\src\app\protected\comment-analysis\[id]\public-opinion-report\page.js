"use client"

import React, { useState } from 'react';
import { 
  Container, 
  Typo<PERSON>, 
  Card, 
  CardContent, 
  Stack, 
  Grid, 
  Box, 
  Chip, 
  Divider,
  Paper,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Link,
  Avatar,
  Breadcrumbs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  ToggleButtonGroup,
  ToggleButton,
  Modal,
  TextField
} from '@mui/material';
import { 
  BarChart3, 
  TrendingUp, 
  MessageCircle, 
  ThumbsUp, 
  ShoppingCart,
  ChevronRight,
  Trash2,
  AlertCircle,
  ExternalLink,
  Check,
  ArrowLeft,
  X,
  Brain,
  Sliders,
  Settings,
  Eye,
  Bookmark
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts';
import { useSnackbar } from 'notistack';
import { useParams, useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import NextLink from 'next/link';

// 模拟数据
const directionData = [
  { name: 'AI获客方向', value: 40, color: '#8884d8' },
  { name: 'AI舆情方向', value: 45, color: '#82ca9d' },
  { name: 'AI成交方向', value: 15, color: '#ffc658' },
];

const deletedCommentsData = [
  { date: '1月', 数量: 25 },
  { date: '2月', 数量: 18 },
  { date: '3月', 数量: 22 },
  { date: '4月', 数量: 15 },
  { date: '5月', 数量: 12 },
  { date: '6月', 数量: 8 },
];

// 模拟每个月的删评详情数据
const monthlyDeletedComments = {
  '1月': [
    { id: 1, content: "这个产品质量太差了，不推荐购买", time: "2023-01-15 14:23", reason: "负面评价" },
    { id: 2, content: "用了两天就坏了，客服态度还很差", time: "2023-01-14 09:45", reason: "负面评价" },
    { id: 3, content: "竞品的功能更好，价格还便宜", time: "2023-01-13 16:30", reason: "竞品宣传" },
    { id: 4, content: "收到的和图片不一样，差评", time: "2023-01-12 11:20", reason: "负面评价" },
    { id: 5, content: "这是诈骗产品，大家不要上当", time: "2023-01-11 18:05", reason: "恶意评价" },
  ],
  '2月': [
    { id: 1, content: "产品设计不合理，很难用", time: "2023-02-15 14:23", reason: "负面评价" },
    { id: 2, content: "材质很差，一点也不耐用", time: "2023-02-12 09:45", reason: "负面评价" },
    { id: 3, content: "不值这个价格，太贵了", time: "2023-02-10 16:30", reason: "价格投诉" },
  ],
  '3月': [
    { id: 1, content: "这个产品有安全隐患，大家小心", time: "2023-03-22 14:23", reason: "安全问题" },
    { id: 2, content: "使用后出现过敏，完全没有提示", time: "2023-03-19 09:45", reason: "负面评价" },
    { id: 3, content: "广告宣传与实际效果差距太大", time: "2023-03-15 16:30", reason: "虚假宣传" },
    { id: 4, content: "其他品牌的同类产品好用多了", time: "2023-03-05 11:20", reason: "竞品宣传" },
  ],
  '4月': [
    { id: 1, content: "这款产品完全是智商税，没用", time: "2023-04-18 14:23", reason: "负面评价" },
    { id: 2, content: "服务态度差，遇到问题不解决", time: "2023-04-10 09:45", reason: "服务投诉" },
    { id: 3, content: "我朋友用了这个产品后很后悔", time: "2023-04-05 16:30", reason: "负面评价" },
  ],
  '5月': [
    { id: 1, content: "包装很简陋，像假货一样", time: "2023-05-25 14:23", reason: "负面评价" },
    { id: 2, content: "退款流程太复杂，故意刁难顾客", time: "2023-05-18 09:45", reason: "服务投诉" },
  ],
  '6月': [
    { id: 1, content: "产品更新后变得更差了", time: "2023-06-15 14:23", reason: "负面评价" },
    { id: 2, content: "物流太慢，客服还找借口", time: "2023-06-08 09:45", reason: "服务投诉" },
  ],
};

const deletedCommentsList = [
  { id: 1, content: "这个产品质量太差了，不推荐购买", time: "2023-06-15 14:23", reason: "负面评价" },
  { id: 2, content: "用了两天就坏了，客服态度还很差", time: "2023-06-14 09:45", reason: "负面评价" },
  { id: 3, content: "竞品的功能更好，价格还便宜", time: "2023-06-13 16:30", reason: "竞品宣传" },
  { id: 4, content: "收到的和图片不一样，差评", time: "2023-06-12 11:20", reason: "负面评价" },
  { id: 5, content: "这是诈骗产品，大家不要上当", time: "2023-06-11 18:05", reason: "恶意评价" },
];

// 模拟三个方向的AI控评数据
const controlCommentData = {
  acquisition: {
    count: 125,
    trend: "+15%",
    comments: [
      { id: 1, content: "这个产品真的太好用了，极力推荐！", time: "2023-06-18 10:23", link: "https://example.com/forum/post/12345" },
      { id: 2, content: "有没有大佬知道这个产品在哪里能买到？看起来很不错", time: "2023-06-17 15:42", link: "https://example.com/forum/post/12346" },
      { id: 3, content: "朋友推荐的，前来了解一下，看评论都说很好", time: "2023-06-16 09:18", link: "https://example.com/forum/post/12347" }
    ]
  },
  reputation: {
    count: 189,
    trend: "+24%",
    comments: [
      { id: 1, content: "用了一个月了，效果非常明显，比市面上同类产品好太多", time: "2023-06-18 14:35", link: "https://example.com/forum/post/23456" },
      { id: 2, content: "客服很专业，解答了我所有疑问，很满意", time: "2023-06-17 11:20", link: "https://example.com/forum/post/23457" },
      { id: 3, content: "包装很精美，产品质量也很好，值得推荐", time: "2023-06-16 16:48", link: "https://example.com/forum/post/23458" }
    ]
  },
  conversion: {
    count: 86,
    trend: "+32%",
    comments: [
      { id: 1, content: "果断下单了，这个价格真的太划算了，错过要再等一年", time: "2023-06-18 20:15", link: "https://example.com/forum/post/34567" },
      { id: 2, content: "刚收到货，比想象中的还要好，准备再入一件", time: "2023-06-17 18:30", link: "https://example.com/forum/post/34568" },
      { id: 3, content: "用了朋友推荐的优惠码，简直不要太划算，大家冲啊", time: "2023-06-16 12:25", link: "https://example.com/forum/post/34569" }
    ]
  }
};

export default function PublicOpinionReport() {
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  const params = useParams();
  const theme = useTheme();
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  // 添加控评模式相关状态
  const [controlMode, setControlMode] = useState('smart'); // 'smart' 或 'manual'
  const [openSettingsModal, setOpenSettingsModal] = useState(false);
  
  // 各种阈值设置
  const [exposureThreshold, setExposureThreshold] = useState(3000);
  const [likesThreshold, setLikesThreshold] = useState(100);
  const [favoritesThreshold, setFavoritesThreshold] = useState(50);
  const [commentsThreshold, setCommentsThreshold] = useState(200);
  
  const handleDirectionChange = (direction) => {
    enqueueSnackbar(`已切换到${direction}引导方向`, { variant: 'success' });
  };

  const router = useRouter();
  
  const handleNavigateToDetails = (direction) => {
    router.push(`/protected/comment-analysis/${params.id}/public-opinion-report/${direction}`);
  };

  const handleBarClick = (data) => {
    // 获取月份并跳转到删评详情页面
    const month = data.date;
    router.push(`/protected/comment-analysis/${params.id}/public-opinion-report/deleted-comments/${month}`);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };
  
  // 添加控评模式相关处理函数
  const handleControlModeChange = (event, newMode) => {
    if (newMode !== null) {
      setControlMode(newMode);
    }
  };
  
  const handleOpenSettings = () => {
    setOpenSettingsModal(true);
  };
  
  const handleCloseSettings = () => {
    setOpenSettingsModal(false);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <NextLink href={`/protected/comment-analysis/${params.id}`} passHref style={{ textDecoration: 'none', color: theme.palette.text.secondary }}>
          <Typography variant="body2" display="flex" alignItems="center">
            <ArrowLeft size={16} style={{ marginRight: '4px' }} />
            返回评论分析
          </Typography>
        </NextLink>
        <Typography variant="body2" color="text.primary">
          AI控评报表
        </Typography>
      </Breadcrumbs>
      
      <Typography variant="h4" component="h1" gutterBottom>
        AI控评报表
      </Typography>
      
      {/* 控评模式设置 */}
      <Card sx={{ 
        mb: 4, 
        pb: 7,
        borderRadius: 3,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden'
      }}>
        <CardContent sx={{ p: 3 }}>
          <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 3 }}>
            <Box sx={{ 
              width: 40, 
              height: 40, 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme.palette.primary.light,
              borderRadius: 2
            }}>
              <Settings size={22} color={theme.palette.primary.main} />
            </Box>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              AI控评配置
            </Typography>
          </Stack>
          
          {/* 控评模式和方向配置 */}
          <Grid container spacing={4} sx={{ width: '100%' }}>
            {/* 控评模式设置部分 */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper elevation={0} sx={{ 
                p: 3, 
                height: '100%',
                borderRadius: 2, 
                background: '#FFFFFF',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                border: '1px solid',
                borderColor: 'divider'
              }}>
                <Typography variant="subtitle1" sx={{ 
                  mb: 3, 
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  color: 'text.primary',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <Brain size={18} style={{ marginRight: '8px' }} />
                  控评模式
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box 
                    onClick={() => setControlMode('smart')}
                    sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      backgroundColor: controlMode === 'smart' ? 'rgba(136, 132, 216, 0.1)' : 'transparent',
                      border: '1px solid',
                      borderColor: controlMode === 'smart' ? 'primary.main' : 'divider',
                      boxShadow: controlMode === 'smart' ? '0 2px 8px rgba(136, 132, 216, 0.15)' : 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(136, 132, 216, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ 
                      width: 40, 
                      height: 40, 
                      borderRadius: '50%', 
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(136, 132, 216, 0.1)',
                      mr: 2
                    }}>
                      <Brain size={20} color="#8884d8" />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">智能控评</Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI自动控评，智能策略调整
                      </Typography>
                    </Box>
                    {controlMode === 'smart' && (
                      <Box sx={{ ml: 1 }}>
                        <Avatar sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: 'primary.main',
                        }}>
                          <Check size={14} color="white" />
                        </Avatar>
                      </Box>
                    )}
                  </Box>
                  
                  <Box 
                    onClick={() => setControlMode('manual')}
                    sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      backgroundColor: controlMode === 'manual' ? 'rgba(130, 202, 157, 0.1)' : 'transparent',
                      border: '1px solid',
                      borderColor: controlMode === 'manual' ? 'primary.main' : 'divider',
                      boxShadow: controlMode === 'manual' ? '0 2px 8px rgba(130, 202, 157, 0.15)' : 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(130, 202, 157, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ 
                      width: 40, 
                      height: 40, 
                      borderRadius: '50%', 
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(130, 202, 157, 0.1)',
                      mr: 2
                    }}>
                      <Sliders size={20} color="#82ca9d" />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">手动控评</Typography>
                      <Typography variant="body2" color="text.secondary">
                        自定义控评参数，精准设置
                      </Typography>
                    </Box>
                    {controlMode === 'manual' && (
                      <Box sx={{ ml: 1 }}>
                        <Avatar sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: 'primary.main',
                        }}>
                          <Check size={14} color="white" />
                        </Avatar>
                      </Box>
                    )}
                  </Box>
                  
                  {controlMode === 'manual' && (
                    <Button
                      variant="outlined"
                      startIcon={<Settings size={16} />}
                      onClick={handleOpenSettings}
                      sx={{ 
                        mt: 1,
                        borderRadius: 2,
                        alignSelf: 'flex-end',
                        textTransform: 'none',
                        fontWeight: 500,
                        px: 2,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.05)',
                        '&:hover': {
                          boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      高级设置
                    </Button>
                  )}
                </Box>
              </Paper>
            </Grid>
            
            {/* 控评方向部分 */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper elevation={0} sx={{ 
                p: 3, 
                height: '100%',
                borderRadius: 2, 
                background: '#FFFFFF',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                border: '1px solid',
                borderColor: 'divider'
              }}>
                <Typography variant="subtitle1" sx={{ 
                  mb: 3, 
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  color: 'text.primary',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <BarChart3 size={18} style={{ marginRight: '8px' }} />
                  控评方向
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box 
                    onClick={() => handleDirectionChange('获客')}
                    sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      backgroundColor: tabValue === 0 ? 'rgba(136, 132, 216, 0.1)' : 'transparent',
                      border: '1px solid',
                      borderColor: tabValue === 0 ? '#8884d8' : 'divider',
                      boxShadow: tabValue === 0 ? '0 2px 8px rgba(136, 132, 216, 0.15)' : 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(136, 132, 216, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ 
                      width: 40, 
                      height: 40, 
                      borderRadius: '50%', 
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(136, 132, 216, 0.1)',
                      mr: 2
                    }}>
                      <MessageCircle size={20} color="#8884d8" />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">获客方向</Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI自动控评，增加用户互动
                      </Typography>
                    </Box>
                    {tabValue === 0 && (
                      <Box sx={{ ml: 1 }}>
                        <Avatar sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: '#8884d8',
                        }}>
                          <Check size={14} color="white" />
                        </Avatar>
                      </Box>
                    )}
                  </Box>
                  
                  <Box 
                    onClick={() => handleDirectionChange('舆情')}
                    sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      backgroundColor: tabValue === 1 ? 'rgba(130, 202, 157, 0.1)' : 'transparent',
                      border: '1px solid',
                      borderColor: tabValue === 1 ? '#82ca9d' : 'divider',
                      boxShadow: tabValue === 1 ? '0 2px 8px rgba(130, 202, 157, 0.15)' : 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(130, 202, 157, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ 
                      width: 40, 
                      height: 40, 
                      borderRadius: '50%', 
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(130, 202, 157, 0.1)',
                      mr: 2
                    }}>
                      <ThumbsUp size={20} color="#82ca9d" />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">舆情方向</Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI正面控评，提升品牌形象
                      </Typography>
                    </Box>
                    {tabValue === 1 && (
                      <Box sx={{ ml: 1 }}>
                        <Avatar sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: '#82ca9d',
                        }}>
                          <Check size={14} color="white" />
                        </Avatar>
                      </Box>
                    )}
                  </Box>
                  
                  <Box 
                    onClick={() => handleDirectionChange('成交')}
                    sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      backgroundColor: tabValue === 2 ? 'rgba(255, 198, 88, 0.1)' : 'transparent',
                      border: '1px solid',
                      borderColor: tabValue === 2 ? '#ffc658' : 'divider',
                      boxShadow: tabValue === 2 ? '0 2px 8px rgba(255, 198, 88, 0.15)' : 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 198, 88, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ 
                      width: 40, 
                      height: 40, 
                      borderRadius: '50%', 
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(255, 198, 88, 0.1)',
                      mr: 2
                    }}>
                      <ShoppingCart size={20} color="#ffc658" />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">成交方向</Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI促成购买，提高转化率
                      </Typography>
                    </Box>
                    {tabValue === 2 && (
                      <Box sx={{ ml: 1 }}>
                        <Avatar sx={{ 
                          width: 20, 
                          height: 20, 
                          bgcolor: '#ffc658',
                        }}>
                          <Check size={14} color="white" />
                        </Avatar>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* 手动控评设置弹窗 */}
      <Modal
        open={openSettingsModal}
        onClose={handleCloseSettings}
        aria-labelledby="manual-control-settings"
      >
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: {xs: '90%', sm: 600},
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 4
        }}>
          <Typography variant="h6" component="h2" gutterBottom>
            手动控评设置
          </Typography>
          
          <Grid container spacing={3} sx={{mt: 1}}>
            <Grid size={12}>
              <Typography variant="subtitle2" gutterBottom>
                触发阈值设置
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{mb: 2}}>
                当所有阈值都达到时，系统将自动触发控评机制
              </Typography>
            </Grid>
            
            <Grid size={12} sx={{mb: 2}}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Eye size={18} />
                <Typography variant="body1" sx={{width: 60}}>曝光量:</Typography>
                <TextField
                  type="number"
                  value={exposureThreshold}
                  onChange={(e) => setExposureThreshold(parseInt(e.target.value) || 0)}
                  size="small"
                  sx={{width: 150}}
                  inputProps={{min: 0}}
                />
              </Stack>
            </Grid>
            
            <Grid size={12} sx={{mb: 2}}>
              <Stack direction="row" spacing={2} alignItems="center">
                <ThumbsUp size={18} />
                <Typography variant="body1" sx={{width: 60}}>点赞数:</Typography>
                <TextField
                  type="number"
                  value={likesThreshold}
                  onChange={(e) => setLikesThreshold(parseInt(e.target.value) || 0)}
                  size="small"
                  sx={{width: 150}}
                  inputProps={{min: 0}}
                />
              </Stack>
            </Grid>
            
            <Grid size={12} sx={{mb: 2}}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Bookmark size={18} />
                <Typography variant="body1" sx={{width: 60}}>收藏数:</Typography>
                <TextField
                  type="number"
                  value={favoritesThreshold}
                  onChange={(e) => setFavoritesThreshold(parseInt(e.target.value) || 0)}
                  size="small"
                  sx={{width: 150}}
                  inputProps={{min: 0}}
                />
              </Stack>
            </Grid>
            
            <Grid size={12} sx={{mb: 2}}>
              <Stack direction="row" spacing={2} alignItems="center">
                <MessageCircle size={18} />
                <Typography variant="body1" sx={{width: 60}}>评论数:</Typography>
                <TextField
                  type="number"
                  value={commentsThreshold}
                  onChange={(e) => setCommentsThreshold(parseInt(e.target.value) || 0)}
                  size="small"
                  sx={{width: 150}}
                  inputProps={{min: 0}}
                />
              </Stack>
            </Grid>
          </Grid>
          
          <Box sx={{mt: 4, display: 'flex', justifyContent: 'flex-end'}}>
            <Button onClick={handleCloseSettings} sx={{mr: 2}}>
              取消
            </Button>
            <Button onClick={handleCloseSettings} variant="contained">
              确定
            </Button>
          </Box>
        </Box>
      </Modal>
      
      {/* 控评数据展示 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 3 }}>
            <TrendingUp size={24} />
            <Typography variant="h6">AI控评数据概览</Typography>
          </Stack>
          
          <Grid container spacing={3} sx={{ width: '100%' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  bgcolor: 'background.paper', 
                  border: '1px solid', 
                  borderColor: 'divider', 
                  borderRadius: 2,
                  cursor: 'pointer',
                  '&:hover': { borderColor: 'primary.main' }
                }}
                onClick={() => handleNavigateToDetails('acquisition')}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MessageCircle size={20} color="#8884d8" />
                  <Typography variant="subtitle2" color="text.secondary">AI获客控评数</Typography>
                </Stack>
                <Typography variant="h4" sx={{ my: 1 }}>{controlCommentData.acquisition.count}</Typography>
                <Chip label={controlCommentData.acquisition.trend + " 较上月"} size="small" color="success" />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  点击查看详情
                </Typography>
              </Paper>
            </Grid>
            
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  bgcolor: 'background.paper', 
                  border: '1px solid', 
                  borderColor: 'divider', 
                  borderRadius: 2,
                  cursor: 'pointer',
                  '&:hover': { borderColor: 'primary.main' }
                }}
                onClick={() => handleNavigateToDetails('reputation')}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <ThumbsUp size={20} color="#82ca9d" />
                  <Typography variant="subtitle2" color="text.secondary">AI舆情控评数</Typography>
                </Stack>
                <Typography variant="h4" sx={{ my: 1 }}>{controlCommentData.reputation.count}</Typography>
                <Chip label={controlCommentData.reputation.trend + " 较上月"} size="small" color="success" />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  点击查看详情
                </Typography>
              </Paper>
            </Grid>
            
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  bgcolor: 'background.paper', 
                  border: '1px solid', 
                  borderColor: 'divider', 
                  borderRadius: 2,
                  cursor: 'pointer',
                  '&:hover': { borderColor: 'primary.main' }
                }}
                onClick={() => handleNavigateToDetails('conversion')}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <ShoppingCart size={20} color="#ffc658" />
                  <Typography variant="subtitle2" color="text.secondary">AI成交控评数</Typography>
                </Stack>
                <Typography variant="h4" sx={{ my: 1 }}>{controlCommentData.conversion.count}</Typography>
                <Chip label={controlCommentData.conversion.trend + " 较上月"} size="small" color="success" />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  点击查看详情
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* 图表分析 */}
      <Grid container spacing={4} sx={{ width: '100%' }}>
        {/* 引导方向分布 */}
        <Grid size={{ xs: 12 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>当前AI控评方向分布</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                各控评方向占比情况
              </Typography>
              <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={directionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {directionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* 删评趋势 */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                <Trash2 size={24} />
                <Typography variant="h6">删评趋势</Typography>
              </Stack>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                近6个月删除评论数量趋势（点击柱状图查看详情）
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={deletedCommentsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="数量" fill="#ff6b6b" onClick={handleBarClick} cursor="pointer" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
