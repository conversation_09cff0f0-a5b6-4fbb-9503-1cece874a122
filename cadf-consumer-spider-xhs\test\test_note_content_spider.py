"""
小红书笔记内容爬虫测试例子
"""
import asyncio
import json

from models.models import Account
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from spider.schemas.note_content_schema import XHSNoteContentSchema
from spider.xhs.xhs_note_content_spider_from_url import crawl_note_from_url


async def test_note_content_spider():
    """
    测试小红书笔记内容爬虫
    """
    try:
        await init_models()
        # 从数据库获取指定ID的账户cookie
        account = await Account.get("68724c33435ce73710ab0bc8")
        if not account or not account.cookie:
            olog.error("未找到ID为123123的账户或该账户没有cookie")
            return

        cookies = account.cookie
        olog.info(f"使用账户 {account.name} 的cookie进行测试")

        # 测试用的小红书笔记URL - 请替换为实际的URL
        test_url = "http://xhslink.com/m/5hXDOGNpflu"
        olog.info(f"测试URL: {test_url}")

        # 调用爬虫函数
        note_data: XHSNoteContentSchema
        is_logged_in: bool
        note_data, is_logged_in = await crawl_note_from_url(
            url=test_url,
            cookies=cookies
        )

        # 打印结果
        olog.info(f"登录状态: {'已登录' if is_logged_in else '未登录'}")
        olog.info("笔记内容爬取结果:")
        
        # 使用model_dump将note_data转化为JSON
        note_dict = note_data.model_dump()
        formatted_json = json.dumps(note_dict, ensure_ascii=False, indent=2, default=str)
        olog.info(formatted_json)

    except Exception as e:
        olog.exception(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_note_content_spider())
