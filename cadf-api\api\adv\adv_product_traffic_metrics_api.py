from typing import Dict, Any

from models.models import Product
from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client


@register_handler('adv_product_traffic_metrics_api')
class AdvProductTrafficMetricsApi:
    @auth_required(['advertiser', 'admin'])
    async def get_total_stats(self, data: Dict[str, Any]) -> Dict[str, int]:
        user_id = data.get('user_id')

        pipeline = [
            {
                "$match": {"user_id": user_id, "is_deleted": {"$ne": True}}
            },
            {
                "$lookup": {
                    "from": "product_traffic_metrics",
                    "localField": "_id",
                    "foreignField": "product_id",
                    "as": "traffic"
                }
            },
            {
                "$unwind": {"path": "$traffic", "preserveNullAndEmptyArrays": True}
            },
            {
                "$group": {
                    "_id": "$user_id",
                    "total_view_count": {"$sum": {"$ifNull": ["$traffic.total_view_count", 0]}},
                    "total_like_count": {"$sum": {"$ifNull": ["$traffic.total_like_count", 0]}},
                    "total_comment_count": {"$sum": {"$ifNull": ["$traffic.total_comment_count", 0]}},
                    "total_favorite_count": {"$sum": {"$ifNull": ["$traffic.total_favorite_count", 0]}},
                    "total_share_count": {"$sum": {"$ifNull": ["$traffic.total_share_count", 0]}}
                }
            }
        ]

        stats_result = await Product.aggregate(pipeline).to_list()

        if stats_result:
            result = stats_result[0]
            result.pop('_id', None)
            return result
        else:
            return {
                "total_view_count": 0,
                "total_like_count": 0,
                "total_comment_count": 0,
                "total_favorite_count": 0,
                "total_share_count": 0,
            }

    @auth_required(['advertiser', 'admin'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        pipeline = [
            {"$match": {"user_id": user_id, "is_deleted": {"$ne": True}}},
            {"$sort": {"create_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {
                "$lookup": {
                    "from": "product_traffic_metrics",
                    "let": {"product_id_str": {"$toString": "$_id"}},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$product_id", "$$product_id_str"]},
                                        {"$eq": ["$user_id", user_id]}
                                    ]
                                }
                            }
                        },
                        {
                            "$project": {
                                "platform": 1,
                                "total_view_count": 1,
                                "total_like_count": 1,
                                "total_comment_count": 1,
                                "total_favorite_count": 1,
                                "total_share_count": 1,
                                "last_updated_at": 1
                            }
                        }
                    ],
                    "as": "traffic_info"
                }
            },
            {
                "$project": {
                    "product_id": {"$toString": "$_id"},
                    "title": 1,
                    "images": 1,
                    "traffic_data": {"$arrayElemAt": ["$traffic_info", 0]},
                    "_id": 0
                }
            }
        ]

        product_list = await Product.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [
            {"$match": {"user_id": user_id, "is_deleted": {"$ne": True}}},
            {"$count": "total"}
        ]
        total_result = await Product.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        for item in product_list:
            images = item.get('images', [])
            if images:
                images.sort(key=lambda img: img.get('order', 0))
                first_image = images[0] if images else None
                if first_image and first_image.get('oss_key'):
                    signed_url = await oss_client.signed_get_url(first_image['oss_key'])
                    item['images'] = [{'signed_url': signed_url, 'order': first_image.get('order', 0)}]
                else:
                    item['images'] = []

            traffic_data = item.pop('traffic_data', None)
            if traffic_data:
                item.update({
                    "platform": traffic_data.get("platform", ""),
                    "total_view_count": traffic_data.get("total_view_count", 0),
                    "total_like_count": traffic_data.get("total_like_count", 0),
                    "total_comment_count": traffic_data.get("total_comment_count", 0),
                    "total_favorite_count": traffic_data.get("total_favorite_count", 0),
                    "total_share_count": traffic_data.get("total_share_count", 0)
                })
            else:
                item.update({
                    "platform": "",
                    "total_view_count": 0,
                    "total_like_count": 0,
                    "total_comment_count": 0,
                    "total_favorite_count": 0,
                    "total_share_count": 0
                })

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=product_list
        )
