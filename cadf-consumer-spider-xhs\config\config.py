# 爬虫相关配置
import sys
import os
from env import process_env

class SpiderConfig:
    # 代理类型配置 ("qgdl": 青果代理, "kdl": 快代理)
    CURRENT_PROXY_TYPE = "kdl"
    
    # 青果代理
    QGDL_PROXY_API_URL = "https://share.proxy.qg.net/get?key=37O6NQ24"
    QGDL_PROXY_USERNAME = "37O6NQ24"
    QGDL_PROXY_PASSWORD = "0C0437B5DA33"
    # 快代理
    KDL_PROXY_API_URL = "https://dps.kdlapi.com/api/getdps/?secret_id=o7ywzb1batdjn9cretqd&num=1&signature=m9zasbhhtokmtrbhsj6me4a1ydpzgz1u&sep=1"
    KDL_PROXY_USERNAME = "d2615606580"
    KDL_PROXY_PASSWORD = "v3jsajbq"

    USE_PROXY = True if process_env == "prod" else False
    HEADLESS = True
    NO_IMGS = True
