from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.api.exception import MException
from beanie import PydanticObjectId
from typing import Optional, List, Dict, Any
from omni.api.pageable import PageResponse
import time

# 导入数据模型
from models.models import CostUserAccount, CostRechargeRecord, CostConsumptionRecord


@register_handler('adv_cost_api')
class AdvCostApi:
    
    @auth_required(['user', 'admin'])
    async def get_user_balance(self, data):
        """获取用户账户余额"""
        user_id = data.get('user_id')
        
        # 根据user_id查找账户
        account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
        
        if not account:
            # 如果账户不存在，创建一个新的账户
            account = CostUserAccount(
                user_id=user_id,
                balance=0.0,
                updated_at=int(time.time())
            )
            await account.insert()
        
        return {"balance": account.balance}
    
    @auth_required(['user', 'admin'])
    async def query_recharge_records(self, data: Dict[str, Any]) -> PageResponse[CostRechargeRecord]:
        """分页查询充值记录"""
        user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        
        # 根据user_id查找用户账户
        account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
        if not account:
            return PageResponse(page=page, page_size=page_size, total=0, results=[])
        
        match_condition = {"cost_user_account_id": str(account.id)}
        
        match_stage = {"$match": match_condition}
        
        pipeline = [
            match_stage,
            {"$sort": {"created_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {"$addFields": {"_id": {"$toString": "$_id"}}},
            {"$unset": "id"}
        ]
        
        records: List[CostRechargeRecord] = await CostRechargeRecord.aggregate(pipeline).to_list()
        
        # 获取总数
        total_pipeline = [
            match_stage,
            {"$count": "total"}
        ]
        total_result = await CostRechargeRecord.aggregate(total_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0
        
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=records
        )
    
    @auth_required(['user', 'admin'])
    async def query_consumption_records(self, data: Dict[str, Any]) -> PageResponse[CostConsumptionRecord]:
        """分页查询消费记录"""
        user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)
        
        # 根据user_id查找用户账户
        account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
        if not account:
            return PageResponse(page=page, page_size=page_size, total=0, results=[])
        
        match_condition = {"cost_user_account_id": str(account.id)}
        
        match_stage = {"$match": match_condition}
        
        pipeline = [
            match_stage,
            {"$sort": {"created_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {"$addFields": {"_id": {"$toString": "$_id"}}},
            {"$unset": "id"}
        ]
        
        records: List[CostConsumptionRecord] = await CostConsumptionRecord.aggregate(pipeline).to_list()
        
        # 获取总数
        total_pipeline = [
            match_stage,
            {"$count": "total"}
        ]
        total_result = await CostConsumptionRecord.aggregate(total_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0
        
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=records
        )