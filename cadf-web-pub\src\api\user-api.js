import api from "@/core/api/api";

const RESOURCE = "user_api";

export const userApi = {
    login: async (username, password) => {
        return await api({
            resource: RESOURCE,
            method_name: "login",
            data: {
                username,
                password,
            },
        });
    },

    register: async (username, password, crewInviteCode) => {
        return await api({
            resource: RESOURCE,
            method_name: "creator_register",
            data: {
                username,
                password,
                crew_invite_code: crewInviteCode,
            },
        });
    },

    getRoles: async () => {
        return await api({
            resource: RESOURCE,
            method_name: "query_roles",
            data: {},
        });
    },
};