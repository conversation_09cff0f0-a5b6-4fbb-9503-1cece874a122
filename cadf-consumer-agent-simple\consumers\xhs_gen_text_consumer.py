from agent.xhs_gen_text_agent import XhsOutput, gen_xhs_text
from common_config.common_config import RedisKeyConfig, CostConfig
from cost.cost_helper import check_user_balance, deduct_user_balance
from models.models import AiGeneratedMaterial, Product, UserBasicMaterial
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set


@consume_redis_set(redis_key=RedisKeyConfig.XHS_TEXT_GEN_SET, num_tasks=10)
async def handle_task(message_data):
    ai_generated_material_id = message_data.get("id_")
    
    if not ai_generated_material_id or ai_generated_material_id == 'None':
        olog.error(f"无效的任务ID: {ai_generated_material_id}")
        return
    
    olog.info(f"开始处理小红书文本生成任务: {ai_generated_material_id}")

    try:
        gen_material = await AiGeneratedMaterial.get(ai_generated_material_id)
        
        # 先检查余额是否充足
        balance_check = await check_user_balance(
            user_id=gen_material.user_id,
            amount=CostConfig.TEXT_GENERATION_COST
        )
        
        if not balance_check:
            # 余额不足时设置为待重试状态
            gen_material.text_generation_status = '失败'
            gen_material.generation_fail_reason = "余额不足，请充值后系统将自动重试"
            await gen_material.save()
            olog.warning(f"任务 {ai_generated_material_id} 因余额不足标记为待重试")
            return
        
        gen_material.text_generation_status = '生成中'
        
        product = await Product.get(gen_material.product_id)
        basic_material = await UserBasicMaterial.get(gen_material.ai_basic_material_id)
        
        # 调用AI文本生成服务
        try:
            olog.debug(f"开始为任务 {ai_generated_material_id} 调用文本生成服务")
            generated_result: XhsOutput = await gen_xhs_text(
                product_info=product.description,
                benchmark_title=basic_material.title,
                benchmark_content=basic_material.content
            )
            
            gen_material.title = generated_result.title
            gen_material.content = generated_result.content
            gen_material.text_generation_status = '已完成'
            
        except Exception as ai_error:
            # gen_xhs_text 报错时设置为待重试状态
            gen_material.text_generation_status = '失败'
            gen_material.generation_fail_reason = "AI服务暂时不可用，系统将自动重试"
            await gen_material.save()
            olog.error(f"AI文本生成服务调用失败，任务 {ai_generated_material_id} 标记为待重试: {ai_error}")
            return

        # 生成成功后扣除余额
        consumption_success = await deduct_user_balance(
            user_id=gen_material.user_id,
            project_name="文本生成",
            amount=CostConfig.TEXT_GENERATION_COST,
            description=f"小红书文本生成任务 {ai_generated_material_id} 费用"
        )
        
        if not consumption_success:
            olog.error(f"任务 {ai_generated_material_id} 扣费失败")
        
        olog.debug(f"任务 {ai_generated_material_id} 计费处理完成")

        await gen_material.save()
        olog.info(f"成功完成小红书文本生成任务: {ai_generated_material_id}")

    except Exception as e:
        olog.exception(f"小红书文本生成任务 {ai_generated_material_id} 处理失败")
        gen_material = await AiGeneratedMaterial.get(ai_generated_material_id)
        gen_material.text_generation_status = '失败'
        gen_material.generation_fail_reason = str(e)
        await gen_material.save()
