"use client";

import React, {useCallback, useEffect, useState} from 'react';
import {Box, Button, Card, CardContent, Chip, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, IconButton, InputLabel, MenuItem, Paper, Select, Stack, TextField, Tooltip, Typography, useMediaQuery, useTheme, Container, Fade, Grow} from '@mui/material';
import {AlertCircle, BookOpen, CheckCircle, Edit, Instagram, LayoutGrid, Link, Plus, RefreshCw, Trash2, Video, XCircle, Filter, Search, MessageCircle, FileText} from 'lucide-react';
import {pubAccountApi} from '@/api/pub-account-api';
import {dataDictionaryApi} from '@/api/data-dictionary-api';
import {handleGetCookiesAndClose, handleOpenLoginWindow} from '@/api/electron-api';
import { useSnackbar } from 'notistack';

// 平台配置 (id 使用中文名称) - 目前只支持小红书
const platforms = [
    {id: '小红书', name: '小红书', icon: <BookOpen size={18}/>, color: '#ffffff', bgColor: '#ff2442', isSupported: true},
    {id: '抖音', name: '抖音', icon: <Video size={18}/>, color: '#ffffff', bgColor: '#000000', isSupported: false},
    {id: '公众号', name: '公众号', icon: <MessageCircle size={18}/>, color: '#ffffff', bgColor: '#07c160', isSupported: false},
    {id: '今日头条', name: '今日头条', icon: <FileText size={18}/>, color: '#ffffff', bgColor: '#ff4500', isSupported: false},
    {id: '知乎', name: '知乎', icon: <LayoutGrid size={18}/>, color: '#ffffff', bgColor: '#0066ff', isSupported: false}
];

// 获取平台按钮样式 - 优化为苹果风格
const getPlatformButtonStyle = (platform, isSelected = false, isDouyin = false) => {
    const baseStyle = {
        minWidth: '88px',
        height: '32px',
        borderRadius: '16px',
        fontSize: '0.75rem',
        fontWeight: 500,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        textTransform: 'none',
        border: '1px solid',
        borderColor: isSelected ? (isDouyin ? '#000000' : platform.bgColor) : 'rgba(0, 0, 0, 0.12)',
    };

    const colorStyle = isSelected ? {
        backgroundColor: isDouyin ? '#000000' : platform.bgColor,
        color: isDouyin ? '#ffffff' : platform.color,
        borderColor: isDouyin ? '#000000' : platform.bgColor,
        boxShadow: `0 2px 8px ${isDouyin ? 'rgba(0, 0, 0, 0.2)' : platform.bgColor + '40'}`,
        '&:hover': {
            backgroundColor: isDouyin ? '#000000' : platform.bgColor,
            transform: 'translateY(-1px)',
            boxShadow: `0 4px 12px ${isDouyin ? 'rgba(0, 0, 0, 0.25)' : platform.bgColor + '50'}`,
        }
    } : {
        backgroundColor: 'transparent',
        color: 'rgba(0, 0, 0, 0.6)',
        '&:hover': {
            borderColor: isDouyin ? '#000000' : platform.bgColor,
            backgroundColor: isDouyin ? 'rgba(0, 0, 0, 0.04)' : `${platform.bgColor}08`,
            color: isDouyin ? '#000000' : platform.bgColor,
            transform: 'translateY(-1px)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        }
    };

    return {...baseStyle, ...colorStyle};
};

export default function AccountsPage() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    const { enqueueSnackbar } = useSnackbar();

    const [selectedPlatform, setSelectedPlatform] = useState(null);
    const [accounts, setAccounts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [openPlatformDialog, setOpenPlatformDialog] = useState(false);
    const [isCheckingLogin, setIsCheckingLogin] = useState(false);
    const [openXhsConfirmDialog, setOpenXhsConfirmDialog] = useState(false);
    const [platformToAdd, setPlatformToAdd] = useState(null);
    const [isConfirmingLogin, setIsConfirmingLogin] = useState(false);

    const [openDeleteConfirmDialog, setOpenDeleteConfirmDialog] = useState(false);
    const [accountToDeleteId, setAccountToDeleteId] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // 编辑账号相关状态
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [accountToEdit, setAccountToEdit] = useState(null);
    const [editingAccountName, setEditingAccountName] = useState("");
    const [editingAccountDomain, setEditingAccountDomain] = useState("");
    const [availableDomains, setAvailableDomains] = useState([]);
    const [isSavingEdit, setIsSavingEdit] = useState(false);

    const [totalAccounts, setTotalAccounts] = useState(0);

    const [openXhsLoginInstructionDialog, setOpenXhsLoginInstructionDialog] = useState(false);

    const loadDomains = useCallback(async () => {
        try {
            const response = await dataDictionaryApi.queryAll('domain');
            if (response && Array.isArray(response.results)) {
                setAvailableDomains(response.results.map(d => d.value).filter(Boolean));
            } else {
                console.error('加载领域列表失败: API 返回格式不正确', response);
                enqueueSnackbar('加载领域选项失败', { variant: 'error' });
            }
        } catch (error) {
            console.error('加载领域列表失败:', error);
            enqueueSnackbar(`加载领域选项失败: ${error.message || '请稍后重试'}`, { variant: 'error' });
        }
    }, []);

    const loadAccounts = useCallback(async (currentPlatform) => {
        setIsLoading(true);
        try {
            const response = await pubAccountApi.queryAll(currentPlatform);
            if (response && Array.isArray(response.results)) {
                setAccounts(response.results.map(account => ({
                    ...account,
                    id_: account._id
                })));
                setTotalAccounts(response.total || response.results.length || 0);
            } else {
                console.error('加载账号失败: API 返回格式不正确', response);
                setAccounts([]);
                setTotalAccounts(0);
                enqueueSnackbar('加载账号列表失败: 数据格式错误', { variant: 'error' });
            }
        } catch (error) {
            console.error('加载账号失败:', error);
            setAccounts([]);
            setTotalAccounts(0);
            enqueueSnackbar(`加载账号列表失败: ${error.message || '请稍后重试'}`, { variant: 'error' });
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        loadAccounts(selectedPlatform);
        loadDomains();
    }, [selectedPlatform, loadAccounts, loadDomains]);

    const checkAllLoginStatus = async () => {
        enqueueSnackbar('状态由后端实时更新，此按钮功能待定', { variant: 'info' });
    };

    const handlePlatformSelect = async (platformId) => {
        setOpenPlatformDialog(false);
        
        const selectedPlatformConfig = platforms.find(p => p.id === platformId);

        if (!selectedPlatformConfig) {
            console.error(`[AccountsPage] Platform configuration not found for ID: ${platformId}`);
            enqueueSnackbar('无效的平台选择', { variant: 'error' });
            return;
        }

        if (!selectedPlatformConfig.isSupported) {
            enqueueSnackbar('该平台暂不支持添加账号，敬请期待！', { variant: 'warning' });
            return;
        }
        
        setPlatformToAdd(platformId);
        
        if (selectedPlatformConfig.id === '小红书') {
            setOpenXhsLoginInstructionDialog(true);
        }
    };

    const handleOpenXhsLoginWindow = async () => {
        setOpenXhsLoginInstructionDialog(false);
        const result = await handleOpenLoginWindow('https://www.xiaohongshu.com/');
        if (result && result.success) {
            setOpenXhsConfirmDialog(true);
        } else {
            if (result && result.message) {
                enqueueSnackbar(result.message, { variant: 'error' });
            }
            setPlatformToAdd(null);
        }
    };

    const handleConfirmXhsLogin = async () => {
        if (!platformToAdd) {
            console.error('未指定要添加的平台');
            enqueueSnackbar('添加账号失败：未知的平台', { variant: 'error' });
            return;
        }
        setIsConfirmingLogin(true);
        try {
            const cookies = await handleGetCookiesAndClose();
            if (cookies && cookies.length > 0) {
                await pubAccountApi.create(platformToAdd, cookies);
                enqueueSnackbar(`${platformToAdd}账号添加成功！`, { variant: 'success' });
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
                setSelectedPlatform(null);
                loadAccounts(null);
            } else {
                enqueueSnackbar(`未能获取${platformToAdd} Cookie，请确保已成功登录并重试。`, { variant: 'warning' });
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
            }
        } catch (error) {
            console.error(`确认${platformToAdd}登录或创建账号时发生错误:`, error);
            enqueueSnackbar(`添加${platformToAdd}账号失败: ${error.message || '请检查网络或联系管理员'}`, { variant: 'error' });
            setOpenXhsConfirmDialog(false);
            setPlatformToAdd(null);
        } finally {
            setIsConfirmingLogin(false);
        }
    };

    const handleDeleteAccount = (accountId) => {
        setAccountToDeleteId(accountId);
        setOpenDeleteConfirmDialog(true);
    };

    const handleCloseDeleteConfirmDialog = () => {
        if (isDeleting) return;
        setOpenDeleteConfirmDialog(false);
        setAccountToDeleteId(null);
    };

    const confirmDeleteAccount = async () => {
        if (!accountToDeleteId) return;

        setIsDeleting(true);
        try {
            await pubAccountApi.delete(accountToDeleteId);
            enqueueSnackbar('账号删除成功', { variant: 'success' });
            handleCloseDeleteConfirmDialog();
            
            loadAccounts(selectedPlatform);

        } catch (error) {
            console.error('删除账号失败:', error);
            enqueueSnackbar(`删除账号失败: ${error.message || '请稍后重试'}`, { variant: 'error' });
        } finally {
            setIsDeleting(false);
        }
    };

    const handleOpenEditDialog = (account) => {
        setAccountToEdit(account);
        setEditingAccountName(account.name || "");
        setEditingAccountDomain(account.domain || "");
        setOpenEditDialog(true);
    };

    const handleCloseEditDialog = () => {
        if (isSavingEdit) return;
        setOpenEditDialog(false);
        setAccountToEdit(null);
        setEditingAccountName("");
        setEditingAccountDomain("");
    };

    const handleSaveEdit = async () => {
        if (!accountToEdit) return;

        const trimmedName = editingAccountName.trim();
        const selectedDomain = editingAccountDomain;

        if (!trimmedName) {
            enqueueSnackbar('账号名称不能为空', { variant: 'warning' });
            return;
        }

        if (trimmedName === accountToEdit.name && selectedDomain === (accountToEdit.domain || "")) {
            enqueueSnackbar('未检测到任何更改', { variant: 'info' });
            handleCloseEditDialog();
            return;
        }

        setIsSavingEdit(true);
        try {
            await pubAccountApi.modify(
                accountToEdit.id_,
                trimmedName,
                selectedDomain
            );
            enqueueSnackbar('账号信息更新成功', { variant: 'success' });
            handleCloseEditDialog();
            loadAccounts(selectedPlatform);
        } catch (error) {
            console.error('更新账号信息失败:', error);
            enqueueSnackbar(`更新账号信息失败: ${error.message || '请稍后重试'}`, { variant: 'error' });
        } finally {
            setIsSavingEdit(false);
        }
    };

    const getPlatformInfo = (platformId) => {
        return platforms.find(p => p.id === platformId) || {id: platformId, name: platformId, icon: <Link size={20}/>, color: '#cccccc', bgColor: '#f0f0f0'};
    };

    const handlePlatformFilterChange = (platformId) => {
        setSelectedPlatform(platformId);
    };

    const handleRefresh = () => {
        setSelectedPlatform(null);
        loadAccounts(null);
    };


    const getLoginStatusDisplay = (status) => {
        if (status === '在线') {
            return (
                <Tooltip title="在线/已登录">
                    <Chip size="small" icon={<CheckCircle size={14}/>} label="在线" color="success" variant="outlined"/>
                </Tooltip>
            );
        } else if (status === '离线') {
            return (
                <Tooltip title="离线/未登录">
                    <Chip size="small" icon={<XCircle size={14}/>} label="离线" color="warning" variant="outlined"/>
                </Tooltip>
            );
        } else if (status === '封禁') {
            return (
                <Tooltip title="账号异常/被封禁">
                    <Chip size="small" icon={<XCircle size={14}/>} label="封禁" color="error" variant="outlined"/>
                </Tooltip>
            );
        } else {
            return (
                <Tooltip title={`状态未知 ${status ? `(${status})` : ''}`}>
                    <Chip
                        size="small"
                        icon={<AlertCircle size={14}/>}
                        label={status || "未知"}
                        variant="outlined"
                        sx={{borderColor: theme.palette.grey[400], color: theme.palette.grey[600]}}
                    />
                </Tooltip>
            );
        }
    };

    return (
        <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
            {/* 页面标题和操作区域 */}
            <Box sx={{ mb: 4 }}>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', md: 'center' },
                    mb: 3,
                    gap: { xs: 2, md: 0 }
                }}>
                    <Box>
                        <Typography
                            variant="h4"
                            component="h1"
                            sx={{
                                fontWeight: 700,
                                fontSize: { xs: '1.75rem', md: '2rem' },
                                color: 'text.primary',
                                mb: 0.5
                            }}
                        >
                            平台账号管理
                        </Typography>
                        <Typography
                            variant="body1"
                            sx={{
                                color: 'text.secondary',
                                fontSize: '0.875rem'
                            }}
                        >
                            管理您在各个平台的账号，查看状态和编辑信息
                        </Typography>
                    </Box>
                    <Button
                        variant="contained"
                        startIcon={<Plus size={18}/>}
                        onClick={() => setOpenPlatformDialog(true)}
                        size="medium"
                        sx={{
                            borderRadius: '24px',
                            px: 3,
                            py: 1.5,
                            fontSize: '0.875rem',
                            fontWeight: 500,
                            minWidth: { xs: '100%', md: 'auto' }
                        }}
                    >
                        添加账号
                    </Button>
                </Box>
            </Box>

            {/* 筛选和统计区域 */}
            <Card variant="content" sx={{ mb: 3, overflow: 'hidden' }}>
                <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                    <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'space-between',
                        mb: 2
                    }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Filter size={20} color="rgba(0, 0, 0, 0.6)" />
                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                平台筛选
                            </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                                共 {accounts.length} 个账号
                            </Typography>
                            <Tooltip title="刷新列表">
                                <span>
                                    <IconButton 
                                        size="small" 
                                        onClick={handleRefresh} 
                                        disabled={isLoading}
                                        sx={{ 
                                            borderRadius: '8px',
                                            bgcolor: 'action.hover',
                                            '&:hover': { bgcolor: 'action.selected' }
                                        }}
                                    >
                                        {isLoading ? <CircularProgress size={16}/> : <RefreshCw size={16}/>}
                                    </IconButton>
                                </span>
                            </Tooltip>
                        </Box>
                    </Box>
                    
                    <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1.5,
                        alignItems: 'center'
                    }}>
                        <Button
                            variant={selectedPlatform === null ? "contained" : "outlined"}
                            size="small"
                            onClick={() => handlePlatformFilterChange(null)}
                            sx={{
                                ...getPlatformButtonStyle(
                                    { bgColor: '#007AFF', color: '#ffffff' },
                                    selectedPlatform === null,
                                    false
                                ),
                                '&.MuiButton-contained': {
                                    backgroundColor: '#007AFF',
                                    color: '#ffffff',
                                    borderColor: '#007AFF',
                                    '&:hover': {
                                        backgroundColor: '#007AFF',
                                    }
                                }
                            }}
                        >
                            全部账号
                        </Button>
                        {platforms.map((platform) => (
                            <Button
                                key={platform.id}
                                variant={selectedPlatform === platform.id ? "contained" : "outlined"}
                                size="small"
                                startIcon={platform.icon}
                                onClick={platform.isSupported ? () => handlePlatformFilterChange(platform.id) : undefined}
                                disabled={!platform.isSupported}
                                sx={{
                                    ...getPlatformButtonStyle(
                                        platform,
                                        selectedPlatform === platform.id,
                                        platform.id === '抖音'
                                    ),
                                    ...(platform.isSupported ? {} : {
                                        opacity: 0.6,
                                        cursor: 'not-allowed',
                                        '&.Mui-disabled': {
                                            borderColor: 'rgba(0, 0, 0, 0.12)',
                                            color: 'rgba(0, 0, 0, 0.38)'
                                        }
                                    })
                                }}
                            >
                                {platform.name}
                                {!platform.isSupported && (
                                    <Typography variant="caption" sx={{ ml: 0.5, fontSize: '0.6rem' }}>
                                        (即将上线)
                                    </Typography>
                                )}
                            </Button>
                        ))}
                    </Box>
                </Box>

                {/* 账号列表区域 */}
                {isLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                        <Box sx={{ textAlign: 'center' }}>
                            <CircularProgress size={40} sx={{ mb: 2 }} />
                            <Typography variant="body2" color="text.secondary">
                                加载中...
                            </Typography>
                        </Box>
                    </Box>
                ) : accounts.length === 0 ? (
                    <Box sx={{ py: 8, textAlign: 'center' }}>
                        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                            暂无账号信息
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            {selectedPlatform ? `当前筛选条件下没有${selectedPlatform}账号` : '点击上方"添加账号"按钮开始添加'}
                        </Typography>
                    </Box>
                ) : (
                    <Box sx={{ p: 3 }}>
                        <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: {
                                xs: '1fr',
                                sm: 'repeat(2, 1fr)',
                                md: 'repeat(4, 1fr)',
                                lg: 'repeat(4, 1fr)',
                                xl: 'repeat(4, 1fr)'
                            },
                            gap: 3,
                            gridAutoRows: 'min-content'
                        }}>
                            {accounts.map((account, index) => {
                                const platformInfo = getPlatformInfo(account.platform);
                                const isDouyin = account.platform === '抖音';

                                return (
                                    <Grow
                                        key={account.id_}
                                        in={true}
                                        timeout={300 + index * 100}
                                        style={{ transformOrigin: '0 0 0' }}
                                    >
                                        <Card 
                                            variant="interactive" 
                                            sx={{
                                                height: '100%',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                position: 'relative',
                                                overflow: 'visible',
                                                '&:hover': {
                                                    '& .account-actions': {
                                                        opacity: 1,
                                                        transform: 'translateY(0)'
                                                    }
                                                }
                                            }}
                                        >
                                            {/* 状态指示器 */}
                                            <Box sx={{
                                                position: 'absolute',
                                                top: 16,
                                                right: 16,
                                                zIndex: 1
                                            }}>
                                                {getLoginStatusDisplay(account.status)}
                                            </Box>

                                            <CardContent sx={{
                                                p: 3,
                                                flex: 1,
                                                display: 'flex',
                                                flexDirection: 'column',
                                                '&:last-child': { pb: 3 }
                                            }}>
                                                {/* 账号头部信息 */}
                                                <Box sx={{ mb: 3 }}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: 2,
                                                        mb: 2
                                                    }}>
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            width: 56,
                                                            height: 56,
                                                            borderRadius: '16px',
                                                            backgroundColor: platformInfo.bgColor,
                                                            color: platformInfo.color,
                                                            flexShrink: 0,
                                                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                                                        }}>
                                                            {React.cloneElement(platformInfo.icon, { size: 24 })}
                                                        </Box>
                                                        <Box sx={{ flex: 1, minWidth: 0 }}>
                                                            <Typography 
                                                                variant="h6" 
                                                                sx={{
                                                                    fontWeight: 600,
                                                                    fontSize: '1.125rem',
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis',
                                                                    whiteSpace: 'nowrap',
                                                                    mb: 0.5,
                                                                    color: 'text.primary'
                                                                }}
                                                            >
                                                                {account.name || '未命名账号'}
                                                            </Typography>
                                                            <Chip
                                                                icon={platformInfo.icon}
                                                                label={platformInfo.name}
                                                                size="small"
                                                                onClick={() => handlePlatformFilterChange(account.platform)}
                                                                sx={{
                                                                    height: 24,
                                                                    fontSize: '0.6875rem',
                                                                    fontWeight: 500,
                                                                    borderRadius: '12px',
                                                                    bgcolor: isDouyin ? 'rgba(0, 0, 0, 0.08)' : `${platformInfo.bgColor}15`,
                                                                    color: isDouyin ? 'rgba(0, 0, 0, 0.7)' : platformInfo.bgColor,
                                                                    border: 'none',
                                                                    cursor: 'pointer',
                                                                    transition: 'all 0.2s ease',
                                                                    '&:hover': {
                                                                        bgcolor: isDouyin ? 'rgba(0, 0, 0, 0.12)' : `${platformInfo.bgColor}25`,
                                                                        transform: 'translateY(-1px)'
                                                                    }
                                                                }}
                                                            />
                                                        </Box>
                                                    </Box>
                                                </Box>

                                                {/* 账号详细信息 */}
                                                <Box sx={{ mb: 3, flex: 1 }}>
                                                    <Stack spacing={2}>
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                            <Typography variant="body2" sx={{
                                                                color: 'text.secondary',
                                                                fontWeight: 500,
                                                                minWidth: 60
                                                            }}>
                                                                领域
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: account.domain ? 'text.primary' : 'text.secondary',
                                                                fontWeight: account.domain ? 500 : 400,
                                                                fontStyle: account.domain ? 'normal' : 'italic'
                                                            }}>
                                                                {account.domain || '未设置'}
                                                            </Typography>
                                                        </Box>
                                                        
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                            <Typography variant="body2" sx={{
                                                                color: 'text.secondary',
                                                                fontWeight: 500,
                                                                minWidth: 60
                                                            }}>
                                                                添加时间
                                                            </Typography>
                                                            <Typography variant="body2" sx={{
                                                                color: 'text.secondary',
                                                                fontWeight: 400
                                                            }}>
                                                                {account.create_at ? new Date(account.create_at * 1000).toLocaleDateString('zh-CN') : '-'}
                                                            </Typography>
                                                        </Box>
                                                    </Stack>
                                                </Box>

                                                {/* 操作按钮 */}
                                                <Box 
                                                    className="account-actions"
                                                    sx={{
                                                        display: 'flex',
                                                        gap: 1,
                                                        mt: 'auto',
                                                        pt: 2,
                                                        borderTop: '1px solid',
                                                        borderColor: 'divider',
                                                        opacity: 0.7,
                                                        transform: 'translateY(4px)',
                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                                                    }}
                                                >
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        startIcon={<Edit size={16}/>}
                                                        onClick={() => handleOpenEditDialog(account)}
                                                        sx={{
                                                            flex: 1,
                                                            borderRadius: '8px',
                                                            py: 0.75,
                                                            fontSize: '0.75rem',
                                                            fontWeight: 500
                                                        }}
                                                    >
                                                        编辑
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        color="error"
                                                        startIcon={<Trash2 size={16}/>}
                                                        onClick={() => handleDeleteAccount(account.id_)}
                                                        sx={{
                                                            flex: 1,
                                                            borderRadius: '8px',
                                                            py: 0.75,
                                                            fontSize: '0.75rem',
                                                            fontWeight: 500
                                                        }}
                                                    >
                                                        删除
                                                    </Button>
                                                </Box>
                                            </CardContent>
                                        </Card>
                                    </Grow>
                                );
                            })}
                        </Box>

                    </Box>
                )}
            </Card>

            {/* 选择平台对话框 */}
            <Dialog 
                open={openPlatformDialog} 
                onClose={() => setOpenPlatformDialog(false)} 
                maxWidth="sm" 
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: '16px',
                        p: 1
                    }
                }}
            >
                <DialogTitle sx={{ 
                    pb: 2, 
                    fontSize: '1.25rem',
                    fontWeight: 600,
                    textAlign: 'center'
                }}>
                    选择要添加的平台
                </DialogTitle>
                <DialogContent sx={{ px: 3, pb: 2 }}>
                    <Box sx={{
                        display: 'grid', 
                        gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' }, 
                        gap: 2
                    }}>
                        {platforms.map((platform) => {
                            const isDouyin = platform.id === '抖音';
                            const isDisabled = !platform.isSupported;
                            
                            return (
                                <Card
                                    key={platform.id}
                                    variant={isDisabled ? "empty" : "interactive"}
                                    onClick={isDisabled ? undefined : () => handlePlatformSelect(platform.id)}
                                    sx={{
                                        cursor: isDisabled ? 'not-allowed' : 'pointer',
                                        opacity: isDisabled ? 0.6 : 1,
                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                        '&:hover': isDisabled ? {} : {
                                            transform: 'translateY(-2px)',
                                            boxShadow: `0 8px 24px ${isDouyin ? 'rgba(0, 0, 0, 0.15)' : platform.bgColor + '40'}`
                                        }
                                    }}
                                >
                                    <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                                        <Box sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: 56,
                                            height: 56,
                                            borderRadius: '16px',
                                            backgroundColor: isDisabled ? 'rgba(0, 0, 0, 0.05)' : (isDouyin ? '#f5f5f5' : platform.bgColor),
                                            color: isDisabled ? 'rgba(0, 0, 0, 0.3)' : (isDouyin ? '#000000' : platform.color),
                                            mx: 'auto',
                                            mb: 2,
                                            boxShadow: isDisabled ? 'none' : '0 4px 12px rgba(0, 0, 0, 0.1)'
                                        }}>
                                            {React.cloneElement(platform.icon, { size: 24 })}
                                        </Box>
                                        
                                        <Typography 
                                            variant="subtitle1" 
                                            sx={{ 
                                                fontWeight: 600,
                                                mb: 0.5,
                                                color: isDisabled ? 'text.disabled' : 'text.primary'
                                            }}
                                        >
                                            {platform.name}
                                        </Typography>
                                        
                                        {!platform.isSupported && (
                                            <Chip 
                                                label="即将上线" 
                                                size="small" 
                                                sx={{
                                                    fontSize: '0.6875rem',
                                                    height: 20,
                                                    borderRadius: '10px',
                                                    bgcolor: 'rgba(0, 0, 0, 0.08)',
                                                    color: 'rgba(0, 0, 0, 0.6)'
                                                }}
                                            />
                                        )}
                                    </CardContent>
                                </Card>
                            );
                        })}
                    </Box>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3, justifyContent: 'center' }}>
                    <Button 
                        onClick={() => setOpenPlatformDialog(false)}
                        variant="outlined"
                        sx={{
                            minWidth: 120,
                            borderRadius: '24px'
                        }}
                    >
                        取消
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openXhsLoginInstructionDialog} onClose={() => {
                setOpenXhsLoginInstructionDialog(false);
                setPlatformToAdd(null);
            }} maxWidth="sm" fullWidth>
                <DialogTitle sx={{ 
                    pb: 1, 
                    display: 'flex', 
                    alignItems: 'center',
                    gap: 1
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: theme => theme.palette.primary.main,
                        color: '#ffffff',
                        flexShrink: 0
                    }}>
                        <BookOpen size={16}/>
                    </Box>
                    平台账号登录指引
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    <Paper 
                        elevation={0} 
                        sx={{ 
                            p: 2, 
                            mb: 2, 
                            bgcolor: theme => `${theme.palette.primary.main}08`,
                            border: theme => `1px solid ${theme.palette.primary.main}20`,
                            borderRadius: 2
                        }}
                    >
                        <Typography sx={{ fontWeight: 500, mb: 1.5, color: 'text.primary' }}>
                            为确保成功添加{platformToAdd}账号，请严格按照以下步骤操作：
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>1</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        点击下方按钮，在新窗口中完成{platformToAdd}账号登录
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        请使用{platformToAdd}创作者中心账号登录
                                    </Typography>
                                </Box>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>2</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        登录成功后<strong>请勿关闭{platformToAdd}页面</strong>
                                    </Typography>
                                    <Typography variant="caption" sx={{ color: theme => theme.palette.error.main, display: 'block' }}>
                                        系统需要从{platformToAdd}页面获取授权信息，提前关闭将导致添加失败
                                    </Typography>
                                </Box>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                                <Box sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    borderRadius: '50%',
                                    bgcolor: theme => theme.palette.primary.main,
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>3</Box>
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        回到此窗口，点击"下一步：确认已登录"按钮
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        系统将自动获取您的授权信息并完成账号添加
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Paper>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button 
                        onClick={() => {
                            setOpenXhsLoginInstructionDialog(false);
                            setPlatformToAdd(null);
                        }}
                        variant="outlined"
                        sx={{ borderColor: '#d0d0d0', color: 'text.secondary' }}
                    >
                        取消
                    </Button>
                    <Button 
                        variant="contained" 
                        onClick={handleOpenXhsLoginWindow}
                        color="primary"
                    >
                        开始登录{platformToAdd}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openXhsConfirmDialog} onClose={(event, reason) => {
                if (reason && reason === 'backdropClick') return;
                setOpenXhsConfirmDialog(false);
                setPlatformToAdd(null);
            }} disableEscapeKeyDown maxWidth="sm" fullWidth>
                <DialogTitle sx={{ 
                    pb: 1, 
                    display: 'flex', 
                    alignItems: 'center',
                    gap: 1
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: theme => theme.palette.primary.main,
                        color: '#ffffff',
                        flexShrink: 0
                    }}>
                        <BookOpen size={16}/>
                    </Box>
                    确认添加{platformToAdd}账号
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                            请确认您已在{platformToAdd}创作者中心完成登录并保持页面打开状态。
                        </Typography>
                        
                        <Box sx={{ 
                            bgcolor: '#f5f5f5', 
                            borderRadius: 1, 
                            p: 2, 
                            mb: 2,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1.5
                        }}>
                            <Box sx={{ color: theme => theme.palette.warning.main }}>
                                <AlertCircle size={24} />
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                                <strong>重要提示：</strong>点击"确认已登录"后，系统将从{platformToAdd}页面获取授权信息。如果您已关闭{platformToAdd}页面或未登录成功，将无法添加账号。
                            </Typography>
                        </Box>
                    </Box>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button 
                        onClick={() => {
                            setOpenXhsConfirmDialog(false);
                            setPlatformToAdd(null);
                        }} 
                        disabled={isConfirmingLogin}
                        variant="outlined"
                        sx={{ borderColor: '#d0d0d0', color: 'text.secondary' }}
                    >
                        取消
                    </Button>
                    <Button 
                        variant="contained" 
                        color="primary"
                        onClick={handleConfirmXhsLogin} 
                        disabled={isConfirmingLogin} 
                        startIcon={isConfirmingLogin ? <CircularProgress size={16} color="inherit"/> : null}
                    >
                        {isConfirmingLogin ? '正在添加账号...' : '确认已登录'}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openDeleteConfirmDialog} onClose={handleCloseDeleteConfirmDialog} aria-labelledby="delete-confirm-dialog-title" aria-describedby="delete-confirm-dialog-description">
                <DialogTitle id="delete-confirm-dialog-title">确认删除账号</DialogTitle>
                <DialogContent>
                    <Typography id="delete-confirm-dialog-description">确定要删除此账号吗？此操作不可恢复。</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDeleteConfirmDialog} disabled={isDeleting}>取消</Button>
                    <Button onClick={confirmDeleteAccount} color="error" variant="contained" disabled={isDeleting} startIcon={isDeleting ? <CircularProgress size={16} color="inherit"/> : null}>
                        {isDeleting ? '删除中...' : '确认删除'}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={openEditDialog} onClose={handleCloseEditDialog} aria-labelledby="edit-dialog-title" fullWidth maxWidth="xs">
                <DialogTitle id="edit-dialog-title">编辑账号信息</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        id="name"
                        label="账号名称"
                        type="text"
                        fullWidth
                        value={editingAccountName}
                        onChange={(e) => setEditingAccountName(e.target.value)}
                        sx={{mb: 2}}
                    />
                    <FormControl fullWidth margin="dense">
                        <InputLabel id="domain-select-label">领域</InputLabel>
                        <Select
                            labelId="domain-select-label"
                            id="domain-select"
                            value={editingAccountDomain}
                            label="领域"
                            onChange={(e) => setEditingAccountDomain(e.target.value)}
                        >
                            <MenuItem value="">
                                <em>无 (或清除领域)</em>
                            </MenuItem>
                            {availableDomains.map((domainValue) => (
                                <MenuItem key={domainValue} value={domainValue}>
                                    {domainValue}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseEditDialog} disabled={isSavingEdit}>取消</Button>
                    <Button onClick={handleSaveEdit} variant="contained" color="primary" disabled={isSavingEdit} startIcon={isSavingEdit ? <CircularProgress size={16} color="inherit"/> : null}>
                        {isSavingEdit ? '保存中...' : '保存'}
                    </Button>
                </DialogActions>
            </Dialog>

        </Container>
    );
}
