import time
from typing import Any, Dict

from beanie import PydanticObjectId

from models.models import Product
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client


@register_handler('adv_product_api')
class AdvProductApi:
    @auth_required(['admin', 'advertiser'])
    async def create(self, data: Dict[str, Any]) -> None:
        user_id = data.get('user_id')
        title = data.get('title')
        description = data.get('description')
        domain = data.get('domain')
        images = data.get('images')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not title:
            raise MException("title 不能为空")
        if not description:
            raise MException("description 不能为空")
        if not domain:
            raise MException("domain 不能为空")

        new_product = Product(
            user_id=user_id,
            title=title,
            description=description,
            domain=domain,
            images=images,
            create_at=int(time.time()),
            is_deleted=False
        )
        await new_product.insert()

    @auth_required(['admin', 'advertiser'])
    async def modify(self, data: Dict[str, Any]) -> None:
        id_ = data.pop('_id', None)
        user_id = data.get('user_id')

        # 基本验证
        if not id_:
            raise MException("请提供有效的产品ID")
        if not user_id:
            raise MException("用户身份验证失败，请重新登录")

        # 字段验证
        title = data.get('title')
        description = data.get('description')
        domain = data.get('domain')
        images = data.get('images')

        if 'title' in data and not title:
            raise MException("产品标题不能为空，请输入有效的产品标题")
        if 'description' in data and not description:
            raise MException("产品描述不能为空，请详细描述您的产品")
        if 'domain' in data and not domain:
            raise MException("产品领域不能为空，请选择产品所属领域")

        # 提取允许修改的字段
        allowed_fields = ['title', 'description', 'domain', 'images']
        update_data = {k: v for k, v in data.items() if k in allowed_fields}

        if not update_data:
            raise MException("未检测到任何修改内容，请至少修改一个字段后重试")

        # 检查产品是否存在并更新
        product = await Product.find_one(Product.id == PydanticObjectId(id_), Product.user_id == user_id)
        if not product:
            raise MException("找不到该产品或您没有修改权限，请确认产品ID是否正确")

        await product.update({"$set": update_data})

    @auth_required(['admin', 'advertiser'])
    async def delete(self, data: Dict[str, Any]) -> None:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        await Product.find_one(Product.id == PydanticObjectId(id_), Product.user_id == user_id).delete()

    @auth_required(['admin', 'advertiser'])
    async def query_one(self, data: Dict[str, Any]) -> Dict[str, Any]:
        id_ = data.get('_id')

        product = await Product.get(PydanticObjectId(id_))
        product_dict = product.to_dict()
        if product_dict.get('images'):
            for img_info in product_dict['images']:
                if img_info.get('oss_key'):
                    img_info['signed_url'] = await oss_client.signed_get_url(img_info['oss_key'])
                else:
                    img_info['signed_url'] = None

        return product_dict

    @auth_required(['admin', 'advertiser'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        page = data.get('page', 1)
        page_size = data.get('page_size', 5)
        search_filter = data.get('search')
        user_id = data.get('user_id')

        match_stage = {
            "user_id": user_id,
            "is_deleted": False
        }
        if search_filter:
            match_stage["title"] = {"$regex": search_filter, "$options": "i"}

        pipeline = [
            {"$match": match_stage},
            {"$sort": {"create_at": -1}},
            {"$skip": (page - 1) * page_size},
            {"$limit": page_size},
            {"$project": {
                "_id": {"$toString": "$_id"},
                "title": 1,
                "images": 1,
                "domain": 1,
                "description": 1,
                "create_at": 1
            }}
        ]

        paginated_products = await Product.aggregate(pipeline).to_list()

        # 计算总条数
        count_pipeline = [{"$match": match_stage}, {"$count": "total"}]
        total_result = await Product.aggregate(count_pipeline).to_list()
        total = total_result[0]["total"] if total_result else 0

        products_list = []
        for p_dict in paginated_products:
            if p_dict.get('images') and len(p_dict['images']) > 0:
                # 只处理第一张图片
                first_img = p_dict['images'][0]
                if first_img.get('oss_key'):
                    p_dict['image_url'] = await oss_client.signed_get_url(first_img['oss_key'])
                else:
                    p_dict['image_url'] = None
            else:
                p_dict['image_url'] = None
            # 删除原来的 images 字段
            p_dict.pop('images', None)
            products_list.append(p_dict)

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=products_list
        )
