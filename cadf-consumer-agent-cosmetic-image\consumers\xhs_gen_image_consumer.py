import asyncio
import random
import string
import time
from typing import Dict, Any

from agent.graph import graph
from common_config.common_config import RedisKeyConfig, OSS_PIC_DIR, CostConfig, LOCAL_TEMP_DIR
from models.models import AiGeneratedMaterial, Product, UserBasicMaterial, ImageInfo
from cost.cost_helper import check_user_balance, deduct_user_balance
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from beanie import PydanticObjectId


async def _process_scene_image(scene_img, product_image_path: str, product_intro: str,
                               temp_dir: str, material_doc, index: int) -> str:
    """处理单张场景图片"""
    try:
        # 先检查用户余额是否足够
        has_sufficient_balance = await check_user_balance(material_doc.user_id, CostConfig.IMAGE_GENERATION_COST)
        if not has_sufficient_balance:
            olog.warning(f"用户 {material_doc.user_id} 余额不足，第{index + 1}张图片无法生成")
            raise Exception(f"账户余额不足，请充值后重试")

        scene_image_path = await oss_client.download_file(scene_img.oss_key, temp_dir)
        olog.debug(f"场景图片{index + 1}下载完成")

        result = await graph.ainvoke({
            "scene_image_path": scene_image_path,
            "product_image_path": product_image_path,
            "product_intro": product_intro
        })

        final_image_path = result['final_image_path']

        # 生成唯一的OSS密钥
        timestamp = str(int(time.time()))
        random_suffix = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        oss_key = f"{OSS_PIC_DIR}/{timestamp}_{random_suffix}.png"
        final_oss_key = await oss_client.upload_file(oss_key, final_image_path)
        olog.debug(f"图片{index + 1}上传成功: {final_oss_key}")

        # 只有在图片成功生成并上传后才计费
        success = await deduct_user_balance(
            user_id=material_doc.user_id,
            project_name="图片生成",
            amount=CostConfig.IMAGE_GENERATION_COST,
            description="AI生成图片计费"
        )
        if not success:
            olog.error(f"用户 {material_doc.user_id} 计费失败")
            raise Exception("计费失败")

        return final_oss_key
    
    except Exception as e:
        olog.exception(f"场景图片{index + 1}处理失败: {e}")
        raise


@consume_redis_set(redis_key=RedisKeyConfig.XHS_IMAGE_GEN_SET, num_tasks=10)
async def handle_task(message_data: Dict[str, Any]) -> None:
    """处理图片生成任务"""
    task_id = message_data.get("id_")
    olog.info(f"开始处理图片生成任务: {task_id}")

    material_doc = await AiGeneratedMaterial.get(task_id)
    
    # 验证 AiGeneratedMaterial 记录是否有效
    if not material_doc or material_doc.is_deleted:
        olog.warning(f"AiGeneratedMaterial 记录不存在或已被删除: {task_id}")
        return
    
    material_doc.image_generation_status = "生成中"
    await material_doc.save()

    try:
        # 验证关联的 Product 记录
        product_doc = await Product.find_one(Product.id == PydanticObjectId(material_doc.product_id), Product.is_deleted == False)
        if not product_doc:
            olog.warning(f"关联的 Product 记录不存在或已被删除: {material_doc.product_id}")
            material_doc.image_generation_status = "失败"
            material_doc.generation_fail_reason = "产品信息已不存在，请重新选择产品后再试"
            await material_doc.save()
            return
        
        # 验证关联的 UserBasicMaterial 记录
        basic_material_doc = await UserBasicMaterial.find_one(UserBasicMaterial.id == PydanticObjectId(material_doc.ai_basic_material_id),
                                                            UserBasicMaterial.is_deleted == False)
        if not basic_material_doc:
            olog.warning(f"关联的 UserBasicMaterial 记录不存在或已被删除: {material_doc.ai_basic_material_id}")
            material_doc.image_generation_status = "失败"
            material_doc.generation_fail_reason = "素材模板已不存在，请重新选择素材模板后再试"
            await material_doc.save()
            return

        temp_dir = LOCAL_TEMP_DIR
        product_image_path = await oss_client.download_file(product_doc.images[0].oss_key, temp_dir)
        olog.info(f"产品图片下载完成，开始处理{len(basic_material_doc.images)}张场景图片")

        tasks = [
            _process_scene_image(
                scene_img, product_image_path, product_doc.description,
                temp_dir, material_doc, i
            )
            for i, scene_img in enumerate(basic_material_doc.images)
        ]

        uploaded_keys = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        successful_keys = [key for key in uploaded_keys if isinstance(key, str)]
        balance_errors = [key for key in uploaded_keys if isinstance(key, Exception) and "账户余额不足" in str(key)]
        other_failed_exceptions = [key for key in uploaded_keys if isinstance(key, Exception) and "账户余额不足" not in str(key)]
        total_images = len(basic_material_doc.images)
        
        olog.info(f"图片生成结果: 成功 {len(successful_keys)} 张，余额不足 {len(balance_errors)} 张，其他失败 {len(other_failed_exceptions)} 张，总共 {total_images} 张")
        
        # 优先处理余额不足的情况
        if balance_errors:
            olog.error(f"检测到余额不足: {[str(e) for e in balance_errors]}")
            material_doc.image_generation_status = "失败"
            material_doc.generation_fail_reason = "账户余额不足，请充值后重试"
            await material_doc.save()
            return
        
        # 检查是否有其他图片生成失败
        if other_failed_exceptions:
            material_doc.image_generation_status = "失败"
            material_doc.generation_fail_reason = f"图片生成失败（{len(other_failed_exceptions)}/{total_images}），请重新尝试"
            await material_doc.save()
            olog.error(f"检测到 {len(other_failed_exceptions)} 张图片生成失败，任务标记为失败")
            return
        
        # 所有图片都成功生成
        if successful_keys:
            material_doc.images = [ImageInfo(oss_key=key, order=i) for i, key in enumerate(successful_keys)]
            material_doc.image_generation_status = "已完成"
            await material_doc.save()
            olog.info(f"图片生成任务完成，成功生成{len(successful_keys)}张图片")
        else:
            # 没有成功的图片（这种情况理论上不会发生，因为上面已经检查过失败情况）
            material_doc.image_generation_status = "失败"
            material_doc.generation_fail_reason = "图片生成失败，请检查素材模板和产品信息后重新尝试"
            await material_doc.save()
            olog.error("没有成功生成任何图片")

    except Exception as e:
        olog.exception(f"图片生成过程出现错误: {e}")
        material_doc.image_generation_status = "失败"
        material_doc.generation_fail_reason = "系统繁忙，请稍后重试或联系客服"
        await material_doc.save()
