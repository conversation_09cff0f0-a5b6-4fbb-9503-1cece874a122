"use client";

import {useCallback, useEffect, useState} from 'react';
import {Box, Button, Card, CardActionArea, CardContent, CardMedia, Chip, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Divider, Fade, Grid, IconButton, InputAdornment, Paper, Slide, Stack, TextField, Tooltip, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {RefreshCw, Search, Trash2, Upload, X, RotateCcw} from 'lucide-react';
import {useRouter} from 'next/navigation';
import ImageCard from '@/components/ImageCard';
import InfiniteScrollList from '@/core/components/InfiniteScrollList';
import {advUserBasicMaterialApi} from '@/api/adv-user-basic-material-api';
import dayjs from 'dayjs';
import {useSnackbar} from 'notistack';

// 状态颜色映射函数
const getFetchStatusColor = (status) => {
    switch (status) {
        case '待爬取': return 'warning';
        case '爬取中': return 'info';
        case '已完成': return 'success';
        case '失败': return 'error';
        default: return 'default';
    }
};

// 搜索工具栏组件
const SearchToolbar = ({ searchInput, setSearchInput, onSearchClick, onRefresh, loading }) => {
    const handleSearchKeyDown = (event) => {
        if (event.key === 'Enter') {
            onSearchClick();
        }
    };

    return (
        <Stack direction="row" spacing={1} alignItems="center">
            <TextField
                placeholder="搜索标题或内容"
                size="small"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <Search size={18}/>
                        </InputAdornment>
                    ),
                    sx: { borderRadius: 2 }
                }}
                sx={{ width: 240 }}
            />
            <Tooltip title="搜索">
                <IconButton
                    onClick={onSearchClick}
                    size="small"
                    sx={{ borderRadius: 2 }}
                    disabled={loading}
                >
                    <Search size={18}/>
                </IconButton>
            </Tooltip>
            <Tooltip title="刷新数据">
                <IconButton
                    onClick={onRefresh}
                    size="small"
                    sx={{ 
                        borderRadius: 2,
                        '@keyframes spin': {
                            from: { transform: 'rotate(0deg)' },
                            to: { transform: 'rotate(360deg)' }
                        },
                        '& .lucide-refresh-cw': {
                            animation: loading ? 'spin 1s linear infinite' : 'none'
                        }
                    }}
                    disabled={loading}
                >
                    <RefreshCw size={18}/>
                </IconButton>
            </Tooltip>
        </Stack>
    );
};

// 素材卡片组件
const MaterialCard = ({ material, onViewDetail, onDelete, onImagePreview, onRefetch, isRefetching }) => {
    const theme = useTheme();
    
    const truncateContent = (content, limit = 80) => {
        if (content?.length <= limit) return content;
        return content?.substring(0, limit) + '.';
    };

    const formatTimestamp = (timestamp) => {
        return timestamp ? dayjs.unix(timestamp).format('YYYY-MM-DD') : 'N/A';
    };

    // 检查是否为待爬取或爬取中状态
    const isPending = material.fetch_status === '待爬取' || material.fetch_status === '爬取中';
    const isFetching = material.fetch_status === '爬取中';
    
    // 待爬取状态的占位图片和内容
    const getPlaceholderImage = () => {
        if (isPending) {
            return `data:image/svg+xml,${encodeURIComponent(`
                <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f5f5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#eeeeee;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grad)"/>
                    <circle cx="150" cy="80" r="25" fill="#bdbdbd" opacity="0.6"/>
                    <path d="M135 70 L165 70 L165 90 L135 90 Z M140 75 L145 82 L155 72 L160 77 L160 85 L140 85 Z" fill="white"/>
                    <rect x="50" y="120" width="200" height="8" rx="4" fill="#bdbdbd" opacity="0.3"/>
                    <rect x="50" y="140" width="150" height="6" rx="3" fill="#bdbdbd" opacity="0.2"/>
                    <rect x="50" y="160" width="180" height="6" rx="3" fill="#bdbdbd" opacity="0.2"/>
                </svg>
            `)}`;
        }
        return material.images?.[0]?.signed_url || 'https://placehold.co/300x200/E5E5EA/8E8E93?text=NoImage';
    };

    const getDisplayTitle = () => {
        if (isPending && (!material.title || material.title.trim() === '')) {
            return '正在获取内容标题...';
        }
        return material.title || '无标题';
    };

    const getDisplayContent = () => {
        if (isPending && (!material.content || material.content.trim() === '')) {
            return '系统正在从平台获取完整内容，请稍候...';
        }
        return truncateContent(material.content) || '暂无内容';
    };

    return (
        <ImageCard
            image={getPlaceholderImage()}
            onClick={() => onViewDetail(material._id)}
        >
            <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column', position: 'relative' }}>

                <Stack direction="row" spacing={1} alignItems="flex-start" sx={{ mb: 1 }}>
                    <Typography
                        variant="subtitle1"
                        title={material.title}
                        sx={{
                            fontWeight: 600,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: 1.3,
                            minHeight: '2.6em',
                            flex: 1,
                            color: isPending ? theme.palette.text.secondary : theme.palette.text.primary,
                            fontStyle: isPending && (!material.title || material.title.trim() === '') ? 'italic' : 'normal'
                        }}
                    >
                        {getDisplayTitle()}
                    </Typography>
                    <Stack direction="row" spacing={0.5}>
                        <Tooltip title="重新获取">
                            <IconButton
                                size="small"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRefetch(e, material);
                                }}
                                sx={{
                                    color: theme.palette.primary.main,
                                    '&:hover': { backgroundColor: theme.palette.primary.light + '20' },
                                    '@keyframes rotate': {
                                        from: { transform: 'rotate(0deg)' },
                                        to: { transform: 'rotate(360deg)' }
                                    },
                                    '& svg': {
                                        animation: isRefetching ? 'rotate 1s linear infinite' : 'none'
                                    }
                                }}
                                disabled={isRefetching}
                            >
                                <RotateCcw size={16}/>
                            </IconButton>
                        </Tooltip>
                        <IconButton
                            size="small"
                            onClick={(e) => {
                                e.stopPropagation();
                                onDelete(e, material);
                            }}
                            sx={{
                                color: theme.palette.error.main,
                                '&:hover': { backgroundColor: theme.palette.error.light }
                            }}
                        >
                            <Trash2 size={16}/>
                        </IconButton>
                    </Stack>
                </Stack>

                <Typography
                    variant="body2"
                    color="text.secondary"
                    title={material.content}
                    sx={{
                        mb: 1.5,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        minHeight: '3.6em',
                        fontStyle: isPending && (!material.content || material.content.trim() === '') ? 'italic' : 'normal',
                        opacity: isPending && (!material.content || material.content.trim() === '') ? 0.7 : 1
                    }}
                >
                    {getDisplayContent()}
                </Typography>

                <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{
                        mt: 2,
                        pt: 1,
                        borderTop: `1px solid ${theme.palette.divider}`
                    }}
                >
                    <Stack direction="row" alignItems="center" spacing={0.5}>
                        <Typography variant="caption" color="text.secondary">
                            {material.platform || '未知来源'}
                        </Typography>
                        {isPending && (
                            <Typography variant="caption" sx={{ color: theme.palette.warning.main }}>
                                • 获取中
                            </Typography>
                        )}
                    </Stack>
                    <Chip
                        label={material.fetch_status || '未知'}
                        size="small"
                        color={getFetchStatusColor(material.fetch_status)}
                        sx={{ 
                            fontSize: '0.7rem',
                            animation: isFetching ? 'pulse 2s infinite' : 'none',
                            '@keyframes pulse': {
                                '0%, 100%': { opacity: 1 },
                                '50%': { opacity: 0.6 }
                            }
                        }}
                    />
                    <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(material.create_at)}
                    </Typography>
                </Stack>
            </Box>
        </ImageCard>
    );
};

// 图片预览对话框组件
const ImagePreviewDialog = ({ open, onClose, images }) => {
    const theme = useTheme();
    
    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="md"
            fullWidth
            onClick={(e) => e.stopPropagation()}
        >
            <DialogTitle>
                <Typography variant="h6">素材图片</Typography>
                <IconButton
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: theme.palette.grey[500]
                    }}
                >
                    <X size={20}/>
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Grid container spacing={2} justifyContent="center">
                    {images.map((image, index) => (
                        <Grid key={`preview-image-${index}`} size="auto">
                            <CardMedia
                                component="img"
                                src={image}
                                alt={`图片 ${index + 1}`}
                                sx={{
                                    maxHeight: { xs: 200, sm: 300, md: 400 },
                                    maxWidth: '100%',
                                    width: 'auto',
                                    objectFit: 'contain',
                                    borderRadius: 1,
                                    border: `1px solid ${theme.palette.divider}`
                                }}
                            />
                        </Grid>
                    ))}
                    {images.length === 0 && (
                        <Grid size={12}>
                            <Typography textAlign="center">没有可预览的图片</Typography>
                        </Grid>
                    )}
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>关闭</Button>
            </DialogActions>
        </Dialog>
    );
};

// 删除确认对话框组件
const DeleteConfirmDialog = ({ open, onClose, onConfirm, material, isDeleting }) => {
    return (
        <Dialog open={open} onClose={onClose}>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
                <Typography>
                    您确定要删除素材"{material?.title || '未命名素材'}"吗？此操作不可恢复。
                </Typography>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} disabled={isDeleting}>
                    取消
                </Button>
                <Button
                    onClick={onConfirm}
                    color="error"
                    disabled={isDeleting}
                    startIcon={isDeleting ? <CircularProgress size={16}/> : null}
                >
                    {isDeleting ? '删除中...' : '删除'}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

// 平台选项
const PLATFORM_OPTIONS = [
    { 
        value: '小红书', 
        label: '小红书',
        icon: '📱',
        color: '#FF2442',
        gradient: 'linear-gradient(135deg, #FF2442 0%, #FF6B7D 100%)',
        enabled: true
    },
    { 
        value: '抖音', 
        label: '抖音',
        icon: '🎵',
        color: '#000000',
        gradient: 'linear-gradient(135deg, #000000 0%, #434343 100%)',
        enabled: false
    },
    { 
        value: '公众号', 
        label: '公众号',
        icon: '💬',
        color: '#07C160',
        gradient: 'linear-gradient(135deg, #07C160 0%, #00D68F 100%)',
        enabled: false
    },
    { 
        value: '今日头条', 
        label: '今日头条',
        icon: '📰',
        color: '#FF6B35',
        gradient: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
        enabled: false
    },
    { 
        value: '知乎', 
        label: '知乎',
        icon: '🧠',
        color: '#0084FF',
        gradient: 'linear-gradient(135deg, #0084FF 0%, #44A3FF 100%)',
        enabled: false
    }
];

// 平台选择按钮组件
const PlatformButton = ({ platform, selected, onSelect, disabled }) => {
    const theme = useTheme();
    const isDisabled = disabled || !platform.enabled;
    
    return (
        <Button
            onClick={() => !isDisabled && onSelect(platform.value)}
            disabled={isDisabled}
            variant={selected ? 'contained' : 'outlined'}
            sx={{
                borderRadius: 3,
                px: { xs: 2, sm: 3 },
                py: { xs: 1.5, sm: 2 },
                minHeight: { xs: 48, sm: 56 },
                background: selected ? platform.gradient : 'transparent',
                borderColor: selected ? 'transparent' : theme.palette.divider,
                color: selected ? '#fff' : isDisabled ? theme.palette.text.disabled : theme.palette.text.primary,
                fontWeight: 600,
                fontSize: { xs: '0.875rem', sm: '1rem' },
                textTransform: 'none',
                boxShadow: selected ? theme.shadows[8] : 'none',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                opacity: isDisabled && !selected ? 0.5 : 1,
                '&:hover': isDisabled ? {} : {
                    background: selected ? platform.gradient : theme.palette.action.hover,
                    borderColor: selected ? 'transparent' : platform.color,
                    transform: 'translateY(-1px)',
                    boxShadow: selected ? theme.shadows[12] : theme.shadows[4]
                },
                '&:active': {
                    transform: 'translateY(0)'
                }
            }}
            startIcon={
                <Typography sx={{ fontSize: { xs: '1.2rem', sm: '1.4rem' } }}>
                    {platform.icon}
                </Typography>
            }
        >
            {platform.label}
            {!platform.enabled && (
                <Typography component="span" sx={{ ml: 1, fontSize: '0.75rem', opacity: 0.7 }}>
                    (即将上线)
                </Typography>
            )}
        </Button>
    );
};

// 底部滑出动画组件 (手机端)
const SlideUpTransition = ({ children, in: inProp, ...other }) => {
    return (
        <Slide direction="up" in={inProp} {...other}>
            <div>{children}</div>
        </Slide>
    );
};

// 桌面端弹出框内容
const DesktopDialogContent = ({ theme, platform, setPlatform, url, setUrl, isAdding, onClose, onConfirm }) => (
    <Paper
        elevation={24}
        sx={{
            borderRadius: 4,
            overflow: 'hidden',
            background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${theme.palette.divider}`,
            maxWidth: 480,
            width: '100%'
        }}
    >
        {/* 头部 */}
        <Box sx={{ 
            p: 4, 
            textAlign: 'center',
            background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.primary.light}08 100%)`
        }}>
            <Typography variant="h5" sx={{ 
                fontWeight: 700, 
                mb: 1,
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
            }}>
                添加新素材
            </Typography>
            <Typography variant="body2" color="text.secondary">
                选择平台并输入分享链接
            </Typography>
        </Box>
        
        {/* 内容 */}
        <Box sx={{ p: 4, pt: 3 }}>
            <Stack spacing={4}>
                {/* 平台选择 */}
                <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        选择平台
                    </Typography>
                    <Stack direction="row" spacing={1.5} flexWrap="wrap" useFlexGap>
                        {PLATFORM_OPTIONS.map((option) => (
                            <PlatformButton
                                key={option.value}
                                platform={option}
                                selected={platform === option.value}
                                onSelect={setPlatform}
                                disabled={isAdding}
                            />
                        ))}
                    </Stack>
                </Box>
                
                {/* 链接输入 */}
                <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        分享链接
                    </Typography>
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder="请输入内容分享链接"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        disabled={isAdding}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 3,
                                transition: 'all 0.2s ease',
                                '&:hover fieldset': {
                                    borderColor: theme.palette.primary.main
                                },
                                '&.Mui-focused fieldset': {
                                    borderWidth: 2
                                }
                            }
                        }}
                    />
                </Box>
            </Stack>
        </Box>
        
        {/* 底部按钮 */}
        <Box sx={{ p: 4, pt: 2 }}>
            <Stack direction="row" spacing={2}>
                <Button
                    fullWidth
                    onClick={onClose}
                    disabled={isAdding}
                    sx={{ 
                        borderRadius: 3, 
                        py: 1.5,
                        fontWeight: 600
                    }}
                >
                    取消
                </Button>
                <Button
                    fullWidth
                    variant="contained"
                    onClick={onConfirm}
                    disabled={isAdding || !platform || !url.trim()}
                    startIcon={isAdding ? <CircularProgress size={18} color="inherit"/> : null}
                    sx={{ 
                        borderRadius: 3, 
                        py: 1.5,
                        fontWeight: 600,
                        background: platform ? PLATFORM_OPTIONS.find(p => p.value === platform)?.gradient : `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                        '&:hover': {
                            boxShadow: theme.shadows[8]
                        }
                    }}
                >
                    {isAdding ? '提交中...' : '开始获取'}
                </Button>
            </Stack>
        </Box>
    </Paper>
);

// 手机端底部弹出内容
const MobileBottomSheet = ({ theme, platform, setPlatform, url, setUrl, isAdding, onClose, onConfirm }) => (
    <Paper
        sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            borderTopLeftRadius: 24,
            borderTopRightRadius: 24,
            overflow: 'hidden',
            maxHeight: '85vh',
            background: theme.palette.background.paper,
            zIndex: theme.zIndex.modal
        }}
    >
        {/* 拖拽指示器 */}
        <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            py: 1.5
        }}>
            <Box sx={{
                width: 40,
                height: 4,
                borderRadius: 2,
                backgroundColor: theme.palette.grey[300]
            }} />
        </Box>
        
        {/* 头部 */}
        <Box sx={{ px: 3, pb: 3, textAlign: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                添加新素材
            </Typography>
            <Typography variant="body2" color="text.secondary">
                选择平台并输入分享链接
            </Typography>
        </Box>
        
        {/* 内容 */}
        <Box sx={{ px: 3, pb: 3, maxHeight: '60vh', overflowY: 'auto' }}>
            <Stack spacing={3}>
                {/* 平台选择 */}
                <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        选择平台
                    </Typography>
                    <Stack spacing={1.5}>
                        {PLATFORM_OPTIONS.map((option) => (
                            <PlatformButton
                                key={option.value}
                                platform={option}
                                selected={platform === option.value}
                                onSelect={setPlatform}
                                disabled={isAdding}
                            />
                        ))}
                    </Stack>
                </Box>
                
                {/* 链接输入 */}
                <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        分享链接
                    </Typography>
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder="请输入内容分享链接"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        disabled={isAdding}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 3
                            }
                        }}
                    />
                </Box>
            </Stack>
        </Box>
        
        {/* 底部按钮 */}
        <Box sx={{ 
            p: 3, 
            pt: 2,
            borderTop: `1px solid ${theme.palette.divider}`,
            background: theme.palette.background.default
        }}>
            <Stack spacing={2}>
                <Button
                    fullWidth
                    variant="contained"
                    onClick={onConfirm}
                    disabled={isAdding || !platform || !url.trim()}
                    startIcon={isAdding ? <CircularProgress size={18} color="inherit"/> : null}
                    sx={{ 
                        borderRadius: 3, 
                        py: 1.5,
                        fontWeight: 600,
                        background: platform ? PLATFORM_OPTIONS.find(p => p.value === platform)?.gradient : `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`
                    }}
                >
                    {isAdding ? '提交中...' : '开始获取'}
                </Button>
                <Button
                    fullWidth
                    onClick={onClose}
                    disabled={isAdding}
                    sx={{ 
                        borderRadius: 3, 
                        py: 1.5,
                        fontWeight: 600,
                        color: theme.palette.text.secondary
                    }}
                >
                    取消
                </Button>
            </Stack>
        </Box>
    </Paper>
);

// 主要的添加素材对话框组件
const AddMaterialDialog = ({ open, onClose, onConfirm, url, setUrl, platform, setPlatform, isAdding }) => {
    const theme = useTheme();
    const isMobile = theme.breakpoints.down('sm');
    
    if (typeof window !== 'undefined' && window.innerWidth < 600) {
        // 手机端 - 底部弹出
        return (
            <>
                {/* 背景遮罩 */}
                <Fade in={open}>
                    <Box
                        sx={{
                            position: 'fixed',
                            inset: 0,
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            backdropFilter: 'blur(8px)',
                            zIndex: theme.zIndex.modal - 1,
                            display: open ? 'block' : 'none'
                        }}
                        onClick={onClose}
                    />
                </Fade>
                
                {/* 底部弹出内容 */}
                <SlideUpTransition in={open}>
                    <MobileBottomSheet
                        theme={theme}
                        platform={platform}
                        setPlatform={setPlatform}
                        url={url}
                        setUrl={setUrl}
                        isAdding={isAdding}
                        onClose={onClose}
                        onConfirm={onConfirm}
                    />
                </SlideUpTransition>
            </>
        );
    }
    
    // 桌面端 - 居中弹出
    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth={false}
            PaperProps={{ style: { background: 'transparent', boxShadow: 'none' } }}
            BackdropProps={{
                sx: {
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    backdropFilter: 'blur(12px)'
                }
            }}
        >
            <DesktopDialogContent
                theme={theme}
                platform={platform}
                setPlatform={setPlatform}
                url={url}
                setUrl={setUrl}
                isAdding={isAdding}
                onClose={onClose}
                onConfirm={onConfirm}
            />
        </Dialog>
    );
};

export default function MaterialManagement() {
    const theme = useTheme();
    const router = useRouter();
    const {enqueueSnackbar} = useSnackbar();
    const [search, setSearch] = useState('');
    const [searchInput, setSearchInput] = useState('');
    const [page, setPage] = useState(1);
    const [pageSize] = useState(4);
    const [hasMore, setHasMore] = useState(true);
    const [imagePreviewDialogOpen, setImagePreviewDialogOpen] = useState(false);
    const [previewImages, setPreviewImages] = useState([]);
    const [isAddMaterialDialogOpen, setIsAddMaterialDialogOpen] = useState(false);
    const [shareUrl, setShareUrl] = useState('');
    const [selectedPlatform, setSelectedPlatform] = useState('');
    const [isAddingMaterial, setIsAddingMaterial] = useState(false);

    const [materialsData, setMaterialsData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [materialToDelete, setMaterialToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [refetchingMaterials, setRefetchingMaterials] = useState(new Set());

    const fetchData = useCallback(async (resetData = false) => {
        setLoading(true);
        try {
            const currentPage = resetData ? 1 : page;
            const response = await advUserBasicMaterialApi.queryAll(search, currentPage, pageSize);
            if (response && response.results) {
                const newData = response.results;
                if (resetData) {
                    setMaterialsData(newData);
                    setPage(2);
                } else {
                    setMaterialsData(prev => [...prev, ...newData]);
                    setPage(prev => prev + 1);
                }
                setTotalCount(response.total || 0);
                setHasMore(newData.length === pageSize);
            } else {
                if (resetData) {
                    setMaterialsData([]);
                    setTotalCount(0);
                }
                setHasMore(false);
            }
        } catch (err) {
            console.error("Error fetching materials:", err);
            enqueueSnackbar(err.message || '获取素材数据失败', {variant: 'error'});
            if (resetData) {
                setMaterialsData([]);
                setTotalCount(0);
            }
            setHasMore(false);
        } finally {
            setLoading(false);
        }
    }, [search, page, pageSize]);

    useEffect(() => {
        fetchData(true);
    }, [search]);

    const handleSearchChange = (event) => {
        const value = event.target.value;
        setSearchInput(value);
    };

    const handleSearchKeyDown = (event) => {
        if (event.key === 'Enter') {
            handleSearchClick();
        }
    };

    const handleSearchClick = () => {
        setSearch(searchInput);
        setPage(1);
    };

    const loadMore = useCallback(async () => {
        if (!hasMore || loading) return;
        await fetchData(false);
    }, [hasMore, loading, fetchData]);

    const handleUploadMaterials = () => {
        setShareUrl('');
        setSelectedPlatform('');
        setIsAddMaterialDialogOpen(true);
    };

    const handleViewDetail = (id) => {
        const materialId = id;

        if (materialId) {
            router.push(`/protected/user-base-material-management/${materialId}`);
        } else {
            console.error("Invalid material ID for navigation:", id);
        }
    };

    const handleImagePreview = (event, images) => {
        event.stopPropagation();
        const previewUrls = images.map(img => img.signed_url || 'https://placehold.co/600x400?text=ImageNotFound');
        setPreviewImages(previewUrls);
        setImagePreviewDialogOpen(true);
    };

    const handleCloseImagePreview = () => {
        setImagePreviewDialogOpen(false);
    };

    const truncateContent = (content, limit = 80) => {
        if (!content) return '';
        if (content.length <= limit) return content;
        return content.substring(0, limit) + '.';
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';
        return dayjs.unix(timestamp).format('YYYY-MM-DD');
    };

    const handleDeleteClick = (event, material) => {
        if (event && event.stopPropagation) {
            event.stopPropagation();
        }
        setMaterialToDelete(material);
        setDeleteDialogOpen(true);
    };

    const handleCloseDeleteDialog = () => {
        setDeleteDialogOpen(false);
        setMaterialToDelete(null);
    };

    const handleConfirmDelete = async () => {
        if (!materialToDelete) return;

        setIsDeleting(true);
        try {
            console.log('删除的素材对象:', materialToDelete);

            const materialId = materialToDelete._id;

            if (!materialId) {
                throw new Error('素材ID不存在');
            }

            console.log('准备删除素材，ID:', materialId);

            await advUserBasicMaterialApi.delete(materialId);

            const newData = materialsData.filter(item => item._id !== materialId);
            setMaterialsData(newData);
            setTotalCount(prevCount => prevCount - 1);

            enqueueSnackbar('删除成功', {variant: 'success'});
        } catch (err) {
            console.error("删除素材失败:", err);
            enqueueSnackbar(err.message || '删除素材失败', {variant: 'error'});
        } finally {
            setIsDeleting(false);
            handleCloseDeleteDialog();
        }
    };

    const handleCloseAddMaterialDialog = () => {
        setIsAddMaterialDialogOpen(false);
        setShareUrl('');
        setSelectedPlatform('');
    };

    const handleConfirmAddMaterial = async () => {
        if (!selectedPlatform) {
            enqueueSnackbar('请选择平台', {variant: 'warning'});
            return;
        }
        if (!shareUrl.trim()) {
            enqueueSnackbar('请输入分享链接', {variant: 'warning'});
            return;
        }
        setIsAddingMaterial(true);
        try {
            console.log("提交的平台:", selectedPlatform, "分享链接:", shareUrl);
            // 调用 API 创建素材
            await advUserBasicMaterialApi.create(shareUrl, selectedPlatform);

            enqueueSnackbar('素材添加任务已提交，正在获取内容...', {variant: 'success'});
            handleCloseAddMaterialDialog();
            // 成功后刷新数据
            handleRefresh();
        } catch (err) {
            console.error("添加素材失败:", err);
            enqueueSnackbar(err.message || '添加素材失败', {variant: 'error'});
        } finally {
            setIsAddingMaterial(false);
        }
    };

    const handleRefresh = () => {
        setPage(1);
        fetchData(true);
    };

    const handleRefetch = async (event, material) => {
        if (event && event.stopPropagation) {
            event.stopPropagation();
        }
        
        const materialId = material._id;
        setRefetchingMaterials(prev => new Set([...prev, materialId]));
        
        try {
            console.log('重新获取素材:', material);
            await advUserBasicMaterialApi.refetch(materialId);
            
            enqueueSnackbar('重新获取任务已提交，正在更新内容...', {variant: 'success'});
            // 刷新数据以显示最新状态
            handleRefresh();
        } catch (err) {
            console.error("重新获取素材失败:", err);
            enqueueSnackbar(err.message || '重新获取素材失败', {variant: 'error'});
        } finally {
            setRefetchingMaterials(prev => {
                const newSet = new Set(prev);
                newSet.delete(materialId);
                return newSet;
            });
        }
    };

    return (
        <Stack spacing={3} sx={{ py: 3, width: '100%', px: { xs: 2, sm: 3 } }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                    用户素材库
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<Upload size={18}/>}
                    onClick={handleUploadMaterials}
                >
                    添加素材
                </Button>
            </Stack>

            <Stack spacing={2}>
                <SearchToolbar
                    searchInput={searchInput}
                    setSearchInput={setSearchInput}
                    onSearchClick={handleSearchClick}
                    onRefresh={handleRefresh}
                    loading={loading}
                />
                <Divider/>
            </Stack>

            {loading && materialsData.length === 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '300px' }}>
                    <CircularProgress/>
                </Box>
            )}

            {(materialsData.length > 0 || (!loading && materialsData.length === 0)) && (
                <InfiniteScrollList
                    items={materialsData}
                    loadMore={loadMore}
                    hasMore={hasMore}
                    gridColumns={{ xs: 12, sm: 6, md: 6, lg: 3 }}
                    renderItem={(material, materialIndex) => (
                        <MaterialCard
                            key={material._id || `material-${materialIndex}`}
                            material={material}
                            onViewDetail={handleViewDetail}
                            onDelete={handleDeleteClick}
                            onImagePreview={handleImagePreview}
                            onRefetch={handleRefetch}
                            isRefetching={refetchingMaterials.has(material._id)}
                        />
                    )}
                />
            )}

            <ImagePreviewDialog
                open={imagePreviewDialogOpen}
                onClose={handleCloseImagePreview}
                images={previewImages}
            />

            <DeleteConfirmDialog
                open={deleteDialogOpen}
                onClose={handleCloseDeleteDialog}
                onConfirm={handleConfirmDelete}
                material={materialToDelete}
                isDeleting={isDeleting}
            />

            <AddMaterialDialog
                open={isAddMaterialDialogOpen}
                onClose={handleCloseAddMaterialDialog}
                onConfirm={handleConfirmAddMaterial}
                url={shareUrl}
                setUrl={setShareUrl}
                platform={selectedPlatform}
                setPlatform={setSelectedPlatform}
                isAdding={isAddingMaterial}
            />
        </Stack>
    );
}