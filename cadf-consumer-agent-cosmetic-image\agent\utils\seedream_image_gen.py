import asyncio
import time

import httpx
from openai import AsyncOpenAI

from omni.config.config_loader import config_dict
from omni.log.log import olog


class SeedreamImageGenerator:
    def __init__(self, llm_config_key: str) -> None:
        llm_config = config_dict.get("llm", {}).get(llm_config_key)
        self.api_key = llm_config.get("api_key")
        self.api_base = llm_config.get("api_base")
        self.model_name = llm_config.get("model_name")
        self.client = AsyncOpenAI(api_key=self.api_key, base_url=self.api_base)

    async def gen_image(self, prompt: str, size: str = "1024x1792", max_retries: int = 10, retry_delay: float = 1.0) -> bytes | None:
        olog.info(f"开始生成图片，提示: {prompt}")
        
        for attempt in range(max_retries):
            try:
                response = await self.client.images.generate(
                    model=self.model_name, prompt=prompt, n=1, size=size
                )
                image_url = response.data[0].url
                
                async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
                    image_response = await client.get(image_url)
                    image_response.raise_for_status()
                    olog.info("图片生成成功")
                    return image_response.content
                    
            except Exception as e:
                if 'Post Img Risk Not Pass' in str(e):
                    olog.error(f"图片生成触发风控: {e}")
                    return None
                    
                olog.error(f"第{attempt + 1}次图片生成失败: {e}")
                if attempt < max_retries - 1:
                    olog.debug(f"{retry_delay}秒后重试")
                    await asyncio.sleep(retry_delay)
        
        olog.error(f"图片生成重试{max_retries}次均失败")
        return None
