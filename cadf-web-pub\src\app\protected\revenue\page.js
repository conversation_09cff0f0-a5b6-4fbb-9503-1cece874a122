"use client";

import {<PERSON><PERSON>, Box, Card, CardContent, Grid, Pagination, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, useMediaQuery, useTheme,} from '@mui/material';
import {AlertCircle, DollarSign} from 'lucide-react';
import {useEffect, useState} from 'react';
import {pubUserDailyRevenueApi} from '@/api/pub-user-daily-revenue-api';
import { useSnackbar } from 'notistack';

export default function Revenue() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { enqueueSnackbar } = useSnackbar();

    const [dailyRevenueData, setDailyRevenueData] = useState([]);
    const [totalItems, setTotalItems] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    const [page, setPage] = useState(1);
    const rowsPerPage = 10;

    useEffect(() => {
        const fetchRevenueData = async () => {
            setIsLoading(true);
            try {
                const response = await pubUserDailyRevenueApi.queryDailySummary(page, rowsPerPage);
                if (response && response.items) {
                    setDailyRevenueData(response.items);
                    setTotalItems(response.total_items || 0);
                } else {
                    setDailyRevenueData([]);
                    setTotalItems(0);
                }
            } catch (error) {
                console.error("获取收益数据失败:", error);
                enqueueSnackbar('获取收益数据失败', { variant: 'error' });
                setDailyRevenueData([]);
                setTotalItems(0);
            }
            setIsLoading(false);
        };

        fetchRevenueData();
    }, [page, rowsPerPage]);

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const currentPageData = dailyRevenueData;

    const totalRevenue = 0;
    const withdrawableRevenue = 0;
    const pendingRevenue = 0;


    return (
        <Box sx={{pt: 2, px: {xs: 1, sm: 2}}}>
            <Typography variant="h4" gutterBottom sx={{
                fontWeight: 'medium',
                mb: 3,
                fontSize: {xs: '1.5rem', sm: '2rem', md: '2.25rem'}
            }}>
                我的收益
            </Typography>

            <Typography variant="body2" sx={{mb: 3, color: theme.palette.text.secondary}}>
                我的收益包含本周期内已结算可提现的收益和未到结算周期的收益
            </Typography>

            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                收益概览
            </Typography>

            <Grid container spacing={2} sx={{mb: 4}}>
                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.primary.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    历史总收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.primary.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{totalRevenue.toFixed(2)}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                历史所有总收益
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.warning.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    我的收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.warning.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{pendingRevenue.toFixed(2)}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                未结算和已结算的收益总和
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                    <Card>
                        <CardContent sx={{p: {xs: 2, sm: 3}}}>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                                <DollarSign size={isMobile ? 20 : 24} color={theme.palette.success.main}/>
                                <Typography variant="h6" sx={{
                                    ml: 1,
                                    fontSize: {xs: '0.9rem', sm: '1.1rem', md: '1.25rem'}
                                }}>
                                    可提现收益
                                </Typography>
                            </Box>
                            <Typography variant="h3" sx={{
                                my: 1,
                                fontWeight: 'medium',
                                color: theme.palette.success.main,
                                fontSize: {xs: '1.75rem', sm: '2.25rem', md: '3rem'}
                            }}>
                                ¥{withdrawableRevenue.toFixed(2)}
                            </Typography>
                            <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                本周期内已经结算且可以提现的收益
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                收益规则
            </Typography>

            <Alert
                severity="info"
                icon={<AlertCircle size={isMobile ? 20 : 24}/>}
                sx={{
                    mb: 4,
                    '& .MuiAlert-message': {width: '100%'},
                    px: {xs: 1, sm: 2}
                }}
            >
                <Typography variant="body2" sx={{color: 'error.main', fontWeight: 'medium'}}>
                    • 当任务奖金池中的所有收益派发完毕后，将不再产生收益
                </Typography>
                <Typography variant="body2">
                    • 作品浏览量低于10万，无收益
                </Typography>
                <Typography variant="body2">
                    • 作品浏览量超过10万，每万次浏览获得1元收益
                </Typography>
                <Typography variant="body2">
                    • 单个作品最高收益为200元
                </Typography>
                <Typography variant="body2">
                    • 收益结算日期为每周四
                </Typography>
            </Alert>

            <Typography variant="h6" gutterBottom sx={{mb: 2}}>
                每日收益
            </Typography>

            {isLoading ? (
                <Typography>加载中...</Typography>
            ) : currentPageData.length === 0 ? (
                <Typography>暂无收益数据</Typography>
            ) : isMobile ? (
                <Box>
                    {currentPageData.map((dayData) => (
                        <Card
                            key={dayData.date}
                            sx={{
                                mb: 2,
                                borderRadius: 2,
                                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                                overflow: 'hidden',
                            }}
                        >
                            <Box sx={{
                                height: '8px',
                                width: '100%',
                                bgcolor: dayData.status === "已结算"
                                    ? theme.palette.success.main
                                    : theme.palette.warning.main
                            }}/>
                            <CardContent sx={{p: 2.5}}>
                                <Box sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    mb: 2
                                }}>
                                    <Typography variant="h6" fontWeight="medium">
                                        {dayData.date}
                                    </Typography>
                                </Box>

                                <Box sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: 2,
                                    mb: 2
                                }}>
                                    <Box sx={{
                                        borderRadius: 2,
                                        border: '1px solid #f0f0f0',
                                        overflow: 'hidden',
                                        mb: 2,
                                    }}>
                                        <Box sx={{
                                            display: 'flex',
                                            borderBottom: '1px solid #f0f0f0',
                                        }}>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2,
                                                bgcolor: 'rgba(0,0,0,0.01)',
                                                borderRight: '1px solid #f0f0f0'
                                            }}>
                                                <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                    出账日期
                                                </Typography>
                                            </Box>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2,
                                                bgcolor: 'rgba(0,0,0,0.01)'
                                            }}>
                                                <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                    结算日期
                                                </Typography>
                                            </Box>
                                        </Box>
                                        <Box sx={{
                                            display: 'flex',
                                        }}>
                                            <Box
                                                key={dayData.date}
                                                sx={{
                                                    flex: 1,
                                                    p: 2,
                                                    borderRight: '1px solid #f0f0f0'
                                                }}>
                                                    <Typography variant="body1" sx={{textAlign: 'center'}}>
                                                        {dayData.date} {/* 出账日期仍然是当天的日期 */}
                                                    </Typography>
                                                </Box>
                                                <Box sx={{
                                                    flex: 1,
                                                    p: 2
                                                }}>
                                                    <Typography variant="body1" sx={{textAlign: 'center'}}>
                                                        {dayData.settlementDate || '未结算'}
                                                    </Typography>
                                                </Box>
                                        </Box>
                                    </Box>

                                    <Box sx={{
                                        borderRadius: 2,
                                        border: '1px solid #f0f0f0',
                                        overflow: 'hidden',
                                        mb: 2,
                                    }}>
                                        <Box sx={{
                                            display: 'flex',
                                            borderBottom: '1px solid #f0f0f0',
                                        }}>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2,
                                                bgcolor: 'rgba(0,0,0,0.01)',
                                                borderRight: '1px solid #f0f0f0'
                                            }}>
                                                <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                    总浏览量
                                                </Typography>
                                            </Box>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2,
                                                bgcolor: 'rgba(0,0,0,0.01)'
                                            }}>
                                                <Typography variant="subtitle2" color="textSecondary" sx={{textAlign: 'center'}}>
                                                    收益金额 (元)
                                                </Typography>
                                            </Box>
                                        </Box>
                                        <Box sx={{
                                            display: 'flex',
                                        }}>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2,
                                                borderRight: '1px solid #f0f0f0'
                                            }}>
                                                <Typography variant="h6" fontWeight="medium" sx={{textAlign: 'center'}}>
                                                    {dayData.totalViews.toLocaleString()}
                                                </Typography>
                                            </Box>
                                            <Box sx={{
                                                flex: 1,
                                                p: 2
                                            }}>
                                                <Typography variant="h6" fontWeight="medium" color={theme.palette.success.main} sx={{textAlign: 'center'}}>
                                                    {dayData.totalRevenue.toFixed(2)}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </Box>
                                </Box>

                                <Box>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        状态
                                    </Typography>
                                    <Box
                                        component="span"
                                        sx={{
                                            px: 1.5,
                                            py: 0.5,
                                            borderRadius: 10,
                                            fontSize: '0.75rem',
                                            display: 'inline-block',
                                            fontWeight: 'medium',
                                            backgroundColor: dayData.status === "已结算"
                                                ? `rgba(46, 125, 50, 0.1)`
                                                : `rgba(237, 108, 2, 0.1)`,
                                            color: dayData.status === "已结算"
                                                ? 'success.main'
                                                : 'warning.main',
                                        }}
                                    >
                                        {dayData.status}
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    ))}

                    <Stack spacing={2} sx={{mt: 3, mb: 4, alignItems: 'center'}}>
                        <Pagination
                            count={Math.ceil(totalItems / rowsPerPage)}
                            page={page}
                            onChange={handlePageChange}
                            color="primary"
                            size={isMobile ? "small" : "medium"}
                        />
                    </Stack>
                </Box>
            ) : (
                <Box>
                    <TableContainer component={Paper} sx={{mb: 2}}>
                        <Table>
                            <TableHead>
                                <TableRow sx={{backgroundColor: theme.palette.action.hover}}>
                                    <TableCell>出账日期</TableCell>
                                    <TableCell>结算日期</TableCell>
                                    <TableCell align="right">总浏览量</TableCell>
                                    <TableCell align="right">收益金额 (元)</TableCell>
                                    <TableCell>状态</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {currentPageData.map((dayData) => (
                                    <TableRow
                                        key={dayData.date}
                                    >
                                        <TableCell>{dayData.date}</TableCell>
                                        <TableCell>{dayData.settlementDate || '未结算'}</TableCell>
                                        <TableCell align="right">{dayData.totalViews.toLocaleString()}</TableCell>
                                        <TableCell align="right">{dayData.totalRevenue.toFixed(2)}</TableCell>
                                        <TableCell>
                                            <Box
                                                component="span"
                                                sx={{
                                                    px: 1.5,
                                                    py: 0.5,
                                                    borderRadius: 1,
                                                    fontSize: '0.75rem',
                                                    backgroundColor: dayData.status === "已结算"
                                                        ? `rgba(${theme.palette.success.main}, 0.1)`
                                                        : `rgba(${theme.palette.warning.main}, 0.1)`,
                                                    color: dayData.status === "已结算"
                                                        ? theme.palette.success.main
                                                        : theme.palette.warning.main,
                                                }}
                                            >
                                                {dayData.status}
                                            </Box>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>

                    <Stack spacing={2} sx={{mt: 2, mb: 4, alignItems: 'center'}}>
                        <Pagination
                            count={Math.ceil(totalItems / rowsPerPage)}
                            page={page}
                            onChange={handlePageChange}
                            color="primary"
                            size="medium"
                            showFirstButton
                            showLastButton
                        />
                    </Stack>
                </Box>
            )}
        </Box>
    );
} 