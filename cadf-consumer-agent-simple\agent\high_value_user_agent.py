from typing import List

from omni.llm.output_agent import structured_output_handler
from pydantic import BaseModel, Field


class HighValueUser(BaseModel):
    """
    高价值用户信息
    """
    user_name: str = Field(description="用户名")
    reason: str = Field(description="被认为是高价值用户的原因")


class HighValueUserList(BaseModel):
    """
    高价值用户列表
    """
    users: List[HighValueUser]


async def identify_high_value_users(article: str, comments: str) -> HighValueUserList:
    """
    根据文章和评论识别高价值用户

    Args:
        article (str): 文章原文
        comments (str): 评论区文字信息

    Returns:
        HighValueUserList: 高价值用户列表
    """
    prompt_template = """
    # 角色
    你是一个专业的用户价值分析师。

    # 任务
    根据下面提供的文章原文和用户评论，识别出高价值用户。

    ## 高价值用户的定义
    - 评论内容深刻、有见地，能提出独到观点或补充文章内容。
    - 能够发现文章中的关键问题并提出有质量的疑问。
    - 评论内容表现出对相关领域有深入的了解。
    - 积极参与讨论，能够带动其他用户进行有价值的交流。

    ## 输入信息
    ### 文章原文
    ```
    {article}
    ```

    ### 用户评论
    ```
    {comments}
    ```

    ## 输出要求
    请根据以上信息，找出所有符合高价值用户定义的评论者，并以指定的格式输出。
    如果找不到高价值用户，请返回空列表。

    {format_instructions}
    """

    params = {
        "article": article,
        "comments": comments
    }

    return await structured_output_handler(
        prompt_template=prompt_template,
        params=params,
        output_model=HighValueUserList,
        tags=["high_value_user_agent"]
    )
