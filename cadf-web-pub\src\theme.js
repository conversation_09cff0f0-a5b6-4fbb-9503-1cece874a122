import {createTheme} from '@mui/material/styles';

export default createTheme({
    // 8px 栅格间距系统
    spacing: (factor) => factor * 8,
    
    // 苹果风格形状系统
    shape: {
        borderRadius: 8,
    },
    
    // 苹果风格色彩系统
    palette: {
        // 苹果蓝色作为主色调
        primary: {
            main: '#007AFF',
            light: '#5AC8FA',
            dark: '#0051D4',
            contrastText: '#ffffff',
        },
        // 苹果橙色作为辅助色
        secondary: {
            main: '#FF9500',
            light: '#FFCC00',
            dark: '#FF6B00',
            contrastText: '#ffffff',
        },
        // 苹果系统灰色调
        grey: {
            50: '#F2F2F7',
            100: '#E5E5EA',
            200: '#D1D1D6',
            300: '#C7C7CC',
            400: '#AEAEB2',
            500: '#8E8E93',
            600: '#636366',
            700: '#48484A',
            800: '#3A3A3C',
            900: '#1C1C1E',
        },
        // 苹果系统背景色
        background: {
            default: '#F2F2F7',
            paper: '#ffffff',
        },
        // 苹果文本颜色
        text: {
            primary: '#000000',
            secondary: '#3C3C43',
            disabled: '#8E8E93',
        },
        // 苹果系统状态颜色
        error: {
            main: '#FF3B30',
            light: '#FF6961',
            dark: '#D70015',
            contrastText: '#ffffff',
        },
        warning: {
            main: '#FF9500',
            light: '#FFCC00',
            dark: '#FF6B00',
            contrastText: '#ffffff',
        },
        info: {
            main: '#007AFF',
            light: '#5AC8FA',
            dark: '#0051D4',
            contrastText: '#ffffff',
        },
        success: {
            main: '#4CD964',
            light: '#30D158',
            dark: '#248A3D',
            contrastText: '#ffffff',
        },
    },
    
    // 苹果风格字体系统
    typography: {
        fontFamily: [
            '-apple-system',
            'BlinkMacSystemFont',
            '"Segoe UI"',
            'Roboto',
            '"Helvetica Neue"',
            'Arial',
            'sans-serif',
            '"Apple Color Emoji"',
            '"Segoe UI Emoji"',
            '"Segoe UI Symbol"',
        ].join(','),
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 600,
        h1: {
            fontWeight: 700,
            fontSize: '2.5rem',
            lineHeight: 1.2,
            letterSpacing: '-0.02em',
        },
        h2: {
            fontWeight: 700,
            fontSize: '2rem',
            lineHeight: 1.2,
            letterSpacing: '-0.01em',
        },
        h3: {
            fontWeight: 600,
            fontSize: '1.75rem',
            lineHeight: 1.3,
            letterSpacing: '-0.01em',
        },
        h4: {
            fontWeight: 600,
            fontSize: '1.5rem',
            lineHeight: 1.3,
            letterSpacing: '-0.005em',
        },
        h5: {
            fontWeight: 500,
            fontSize: '1.25rem',
            lineHeight: 1.4,
            letterSpacing: '0em',
        },
        h6: {
            fontWeight: 500,
            fontSize: '1.125rem',
            lineHeight: 1.4,
            letterSpacing: '0em',
        },
        subtitle1: {
            fontWeight: 600,
            fontSize: '1rem',
            lineHeight: 1.5,
            letterSpacing: '0.01em',
        },
        subtitle2: {
            fontWeight: 500,
            fontSize: '0.875rem',
            lineHeight: 1.5,
            letterSpacing: '0.01em',
        },
        body1: {
            fontWeight: 400,
            fontSize: '1rem',
            lineHeight: 1.5,
            letterSpacing: '0.01em',
        },
        body2: {
            fontWeight: 400,
            fontSize: '0.875rem',
            lineHeight: 1.4,
            letterSpacing: '0.01em',
        },
        button: {
            fontWeight: 500,
            fontSize: '0.875rem',
            lineHeight: 1.4,
            letterSpacing: '0.02em',
            textTransform: 'none',
        },
        caption: {
            fontWeight: 400,
            fontSize: '0.75rem',
            lineHeight: 1.4,
            letterSpacing: '0.03em',
        },
        overline: {
            fontWeight: 500,
            fontSize: '0.625rem',
            lineHeight: 1.4,
            letterSpacing: '0.08em',
            textTransform: 'uppercase',
        },
    },
    
    // 苹果风格阴影系统
    shadows: [
        'none',
        '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
        '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
        '0 4px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06)',
        '0 8px 32px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.06)',
        '0 12px 48px rgba(0, 0, 0, 0.08), 0 12px 24px rgba(0, 0, 0, 0.06)',
        '0 16px 64px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.06)',
        '0 20px 80px rgba(0, 0, 0, 0.08), 0 20px 40px rgba(0, 0, 0, 0.06)',
        '0 24px 96px rgba(0, 0, 0, 0.08), 0 24px 48px rgba(0, 0, 0, 0.06)',
        '0 28px 112px rgba(0, 0, 0, 0.08), 0 28px 56px rgba(0, 0, 0, 0.06)',
        '0 32px 128px rgba(0, 0, 0, 0.08), 0 32px 64px rgba(0, 0, 0, 0.06)',
        '0 36px 144px rgba(0, 0, 0, 0.08), 0 36px 72px rgba(0, 0, 0, 0.06)',
        '0 40px 160px rgba(0, 0, 0, 0.08), 0 40px 80px rgba(0, 0, 0, 0.06)',
        '0 44px 176px rgba(0, 0, 0, 0.08), 0 44px 88px rgba(0, 0, 0, 0.06)',
        '0 48px 192px rgba(0, 0, 0, 0.08), 0 48px 96px rgba(0, 0, 0, 0.06)',
        '0 52px 208px rgba(0, 0, 0, 0.08), 0 52px 104px rgba(0, 0, 0, 0.06)',
        '0 56px 224px rgba(0, 0, 0, 0.08), 0 56px 112px rgba(0, 0, 0, 0.06)',
        '0 60px 240px rgba(0, 0, 0, 0.08), 0 60px 120px rgba(0, 0, 0, 0.06)',
        '0 64px 256px rgba(0, 0, 0, 0.08), 0 64px 128px rgba(0, 0, 0, 0.06)',
        '0 68px 272px rgba(0, 0, 0, 0.08), 0 68px 136px rgba(0, 0, 0, 0.06)',
        '0 72px 288px rgba(0, 0, 0, 0.08), 0 72px 144px rgba(0, 0, 0, 0.06)',
        '0 76px 304px rgba(0, 0, 0, 0.08), 0 76px 152px rgba(0, 0, 0, 0.06)',
        '0 80px 320px rgba(0, 0, 0, 0.08), 0 80px 160px rgba(0, 0, 0, 0.06)',
        '0 84px 336px rgba(0, 0, 0, 0.08), 0 84px 168px rgba(0, 0, 0, 0.06)',
        '0 88px 352px rgba(0, 0, 0, 0.08), 0 88px 176px rgba(0, 0, 0, 0.06)',
    ],
    
    // 苹果风格过渡动画系统
    transitions: {
        duration: {
            shortest: 150,
            shorter: 200,
            short: 250,
            standard: 300,
            complex: 375,
            enteringScreen: 225,
            leavingScreen: 195,
        },
        easing: {
            easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
            easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
            easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
            sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
        },
    },
    
    // 层级系统
    zIndex: {
        mobileStepper: 1000,
        fab: 1050,
        speedDial: 1050,
        appBar: 1100,
        drawer: 1200,
        modal: 1300,
        snackbar: 1400,
        tooltip: 1500,
    },
    
    // MUI 组件样式覆盖
    components: {
        // Button 组件优化
        MuiButton: {
            defaultProps: {
                disableRipple: true,
            },
            styleOverrides: {
                root: {
                    borderRadius: '8px',
                    textTransform: 'none',
                    fontWeight: 500,
                    fontSize: '0.875rem',
                    padding: '8px 16px',
                    minHeight: '40px',
                    boxShadow: 'none',
                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                        transform: 'translateY(-1px)',
                    },
                    '&:active': {
                        transform: 'translateY(0)',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                        transition: 'all 0.1s cubic-bezier(0.4, 0, 0.2, 1)',
                    },
                    '&:focus': {
                        outline: '2px solid rgba(0, 122, 255, 0.5)',
                        outlineOffset: '2px',
                    },
                },
                contained: {
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                    '&:hover': {
                        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
                    },
                    '&:active': {
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                    },
                },
                outlined: {
                    borderWidth: '1px',
                    '&:hover': {
                        borderWidth: '1px',
                        backgroundColor: 'rgba(0, 122, 255, 0.04)',
                        borderColor: 'rgba(0, 122, 255, 0.8)',
                    },
                    '&:active': {
                        backgroundColor: 'rgba(0, 122, 255, 0.08)',
                    },
                },
                text: {
                    '&:hover': {
                        backgroundColor: 'rgba(0, 122, 255, 0.04)',
                    },
                    '&:active': {
                        backgroundColor: 'rgba(0, 122, 255, 0.08)',
                    },
                },
                sizeSmall: {
                    padding: '6px 12px',
                    minHeight: '32px',
                    fontSize: '0.75rem',
                    borderRadius: '6px',
                },
                sizeLarge: {
                    padding: '12px 24px',
                    minHeight: '48px',
                    fontSize: '1rem',
                    borderRadius: '10px',
                },
            },
        },
        
        // Card 组件优化
        MuiCard: {
            defaultProps: {
                elevation: 0,
            },
            styleOverrides: {
                root: {
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                    border: '1px solid rgba(0, 0, 0, 0.06)',
                    '&:hover': {
                        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06)',
                        transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                },
            },
            variants: [
                {
                    props: { variant: 'content' },//内容展示卡片
                    style: {
                        borderRadius: '12px',
                        border: '1px solid rgba(0, 0, 0, 0.08)',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                        '&:hover': {
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                            transform: 'none',
                        },
                    },
                },
                {
                    props: { variant: 'interactive' },//交互式卡片
                    style: {
                        borderRadius: '16px',
                        border: '1px solid rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        '&:hover': {
                            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08)',
                            transform: 'translateY(-4px)',
                            borderColor: 'rgba(0, 122, 255, 0.3)',
                        },
                        '&:active': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06)',
                        },
                    },
                },
                {
                    props: { variant: 'form' },//表单容器卡片
                    style: {
                        borderRadius: '16px',
                        border: '1px solid rgba(0, 0, 0, 0.08)',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                        backgroundColor: '#ffffff',
                        '&:hover': {
                            transform: 'none',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                        },
                    },
                },
                {
                    props: { variant: 'empty' },//空状态卡片
                    style: {
                        borderRadius: '12px',
                        border: '2px dashed rgba(0, 0, 0, 0.12)',
                        backgroundColor: 'rgba(0, 0, 0, 0.02)',
                        boxShadow: 'none',
                        '&:hover': {
                            borderColor: 'rgba(0, 122, 255, 0.5)',
                            backgroundColor: 'rgba(0, 122, 255, 0.04)',
                            transform: 'none',
                            boxShadow: 'none',
                        },
                    },
                },
            ],
        },
        
        // TextField 组件优化
        MuiTextField: {
            styleOverrides: {
                root: {
                    '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                        '&:hover fieldset': {
                            borderColor: '#007AFF',
                        },
                        '&.Mui-focused fieldset': {
                            borderColor: '#007AFF',
                            borderWidth: '2px',
                        },
                    },
                    '& .MuiInputLabel-root': {
                        '&.Mui-focused': {
                            color: '#007AFF',
                        },
                    },
                },
            },
        },
        
        // Chip 组件优化
        MuiChip: {
            styleOverrides: {
                root: {
                    borderRadius: '16px',
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    height: '28px',
                },
                sizeSmall: {
                    height: '24px',
                    fontSize: '0.6875rem',
                },
                colorPrimary: {
                    backgroundColor: '#007AFF',
                    color: '#ffffff',
                    '&:hover': {
                        backgroundColor: '#0051D4',
                    },
                },
                colorSecondary: {
                    backgroundColor: '#FF9500',
                    color: '#ffffff',
                    '&:hover': {
                        backgroundColor: '#FF6B00',
                    },
                },
            },
        },
        
        // Dialog 组件优化
        MuiDialog: {
            styleOverrides: {
                paper: {
                    borderRadius: '16px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.06)',
                },
            },
        },
        
        // Paper 组件优化
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: '12px',
                },
                elevation1: {
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                },
                elevation2: {
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                },
                elevation3: {
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06)',
                },
            },
        },
        
        // LinearProgress 组件优化
        MuiLinearProgress: {
            styleOverrides: {
                root: {
                    borderRadius: '4px',
                    height: '6px',
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                },
                bar: {
                    borderRadius: '4px',
                },
            },
        },
        
        // CircularProgress 组件优化
        MuiCircularProgress: {
            styleOverrides: {
                root: {
                    color: '#007AFF',
                },
            },
        },
        
        // Alert 组件优化
        MuiAlert: {
            styleOverrides: {
                root: {
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    fontWeight: 400,
                },
                standardSuccess: {
                    backgroundColor: 'rgba(76, 217, 100, 0.1)',
                    color: '#248A3D',
                },
                standardError: {
                    backgroundColor: 'rgba(255, 59, 48, 0.1)',
                    color: '#D70015',
                },
                standardWarning: {
                    backgroundColor: 'rgba(255, 149, 0, 0.1)',
                    color: '#FF6B00',
                },
                standardInfo: {
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    color: '#0051D4',
                },
            },
        },
        
        // Drawer 组件优化
        MuiDrawer: {
            styleOverrides: {
                paper: {
                    backgroundColor: '#ffffff',
                    borderRight: '1px solid rgba(0, 0, 0, 0.06)',
                },
            },
        },
        
        // AppBar 组件优化
        MuiAppBar: {
            styleOverrides: {
                root: {
                    backgroundColor: '#ffffff',
                    color: '#000000',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06)',
                },
            },
        },
        
        // Divider 组件优化
        MuiDivider: {
            styleOverrides: {
                root: {
                    borderColor: 'rgba(0, 0, 0, 0.06)',
                },
            },
        },
        
        // IconButton 组件优化
        MuiIconButton: {
            defaultProps: {
                disableRipple: true,
            },
            styleOverrides: {
                root: {
                    borderRadius: '8px',
                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                        backgroundColor: 'rgba(0, 122, 255, 0.04)',
                        transform: 'scale(1.05)',
                    },
                    '&:active': {
                        transform: 'scale(0.95)',
                        backgroundColor: 'rgba(0, 122, 255, 0.08)',
                        transition: 'all 0.1s cubic-bezier(0.4, 0, 0.2, 1)',
                    },
                    '&:focus': {
                        outline: '2px solid rgba(0, 122, 255, 0.5)',
                        outlineOffset: '2px',
                    },
                },
                sizeSmall: {
                    borderRadius: '6px',
                },
                sizeLarge: {
                    borderRadius: '10px',
                },
            },
        },
        
        // Tooltip 组件优化
        MuiTooltip: {
            styleOverrides: {
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    fontSize: '0.75rem',
                    borderRadius: '6px',
                    padding: '8px 12px',
                },
            },
        },
        
        // Snackbar 组件优化
        MuiSnackbar: {
            styleOverrides: {
                root: {
                    '& .MuiSnackbarContent-root': {
                        borderRadius: '8px',
                        backgroundColor: '#000000',
                        color: '#ffffff',
                    },
                },
            },
        },
        
        // Container 组件优化
        MuiContainer: {
            styleOverrides: {
                root: {
                    paddingLeft: '16px',
                    paddingRight: '16px',
                    '@media (min-width: 600px)': {
                        paddingLeft: '24px',
                        paddingRight: '24px',
                    },
                    '@media (min-width: 960px)': {
                        paddingLeft: '32px',
                        paddingRight: '32px',
                    },
                },
            },
            variants: [
                {
                    props: { variant: 'page' },
                    style: {
                        paddingTop: '32px',
                        paddingBottom: '32px',
                        '@media (min-width: 600px)': {
                            paddingTop: '40px',
                            paddingBottom: '40px',
                        },
                        '@media (min-width: 960px)': {
                            paddingTop: '48px',
                            paddingBottom: '48px',
                        },
                    },
                },
                {
                    props: { variant: 'section' },
                    style: {
                        paddingTop: '24px',
                        paddingBottom: '24px',
                        '@media (min-width: 600px)': {
                            paddingTop: '32px',
                            paddingBottom: '32px',
                        },
                    },
                },
            ],
        },
    },
    
    // 自定义样式类
    custom: {
        appleCard: {
            borderRadius: '12px',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06)',
                transform: 'translateY(-2px)',
            },
        },
        appleButton: {
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 500,
            padding: '8px 16px',
            minHeight: '40px',
            boxShadow: 'none',
            '&:hover': {
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06)',
                transform: 'translateY(-1px)',
            },
        },
        pageContainer: {
            paddingTop: '32px',
            paddingBottom: '32px',
            paddingLeft: '16px',
            paddingRight: '16px',
            '@media (min-width: 600px)': {
                paddingTop: '40px',
                paddingBottom: '40px',
                paddingLeft: '24px',
                paddingRight: '24px',
            },
            '@media (min-width: 960px)': {
                paddingTop: '48px',
                paddingBottom: '48px',
                paddingLeft: '32px',
                paddingRight: '32px',
            },
        },
        sectionSpacing: {
            marginBottom: '32px',
            '@media (min-width: 600px)': {
                marginBottom: '40px',
            },
        },
        cardContent: {
            padding: '24px',
            '&:last-child': {
                paddingBottom: '24px',
            },
            '@media (min-width: 600px)': {
                padding: '32px',
                '&:last-child': {
                    paddingBottom: '32px',
                },
            },
        },
    },
});