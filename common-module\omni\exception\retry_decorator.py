import asyncio
import functools
from typing import Callable, TypeVar, ParamSpec
from omni.log.log import olog

P = ParamSpec('P')
T = TypeVar('T')


def retry_on_exception(max_retries: int = 3):
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        async def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt == 0:
                        olog.debug(f"执行函数 {func.__name__}")
                    else:
                        olog.info(f"第 {attempt} 次重试执行函数 {func.__name__}")
                    
                    result = await func(*args, **kwargs)
                    
                    if attempt > 0:
                        olog.info(f"函数 {func.__name__} 重试成功")
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        olog.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}")
                    else:
                        olog.error(f"函数 {func.__name__} 所有重试尝试均失败，最后一次错误: {str(e)}")
            
            raise last_exception
        
        @functools.wraps(func)
        async def sync_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt == 0:
                        olog.debug(f"执行函数 {func.__name__}")
                    else:
                        olog.info(f"第 {attempt} 次重试执行函数 {func.__name__}")
                    
                    result = await asyncio.to_thread(func, *args, **kwargs)
                    
                    if attempt > 0:
                        olog.info(f"函数 {func.__name__} 重试成功")
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        olog.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}")
                    else:
                        olog.error(f"函数 {func.__name__} 所有重试尝试均失败，最后一次错误: {str(e)}")
            
            raise last_exception
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator