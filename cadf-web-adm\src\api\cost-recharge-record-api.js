import api from "@/core/api/api";

export const costRechargeRecordApi = {
  create: async (data) => {
    return await api({
      resource: "cost_recharge_record",
      method_name: "create",
      ...data,
    });
  },

  queryOne: async (id) => {
    return await api({
      resource: "cost_recharge_record",
      method_name: "query_one",
      id_: id,
    });
  },

  queryAll: async ({ page = 0, pageSize = 10, costUserAccountId, status } = {}) => {
    return await api({
      resource: "cost_recharge_record",
      method_name: "query_all",
      page: page,
      page_size: pageSize,
      cost_user_account_id: costUserAccountId,
      status: status,
    });
  },
}; 