import {BASE_URL} from "../../env";
import Cookies from "js-cookie";

/**
 * 使用 SSE (Server-Sent Events) 与后端的奇门遁甲聊天机器人进行流式通信
 *
 * @param {string} question - 要发送给机器人的问题
 * @param {string} sessionId - 会话ID
 * @param {object} callbacks - 包含事件回调的对象
 * @param {(chunk: object) => void} callbacks.onData - 接收到已解析JSON数据块时的回调
 * @param {(error: Error) => void} callbacks.onError - 发生错误时的回调
 * @param {() => void} callbacks.onEnd - 流结束时的回调
 * @returns {{abort: () => void}} - 返回一个包含中止方法的对象,用于提前关闭连接
 */
export const streamChat = (question, sessionId, {onData, onError, onEnd}) => {
    const token = Cookies.get("access_token"); // 从cookie中获取token
    const url = `${BASE_URL}/chat?question=${encodeURIComponent(question)}&session_id=${encodeURIComponent(sessionId)}&token=${encodeURIComponent(token)}`;
    const eventSource = new EventSource(url);

    // 监听 'message' 事件，这是默认的事件类型
    eventSource.onmessage = (event) => {
        try {
            const parsedData = JSON.parse(event.data);
            if (onData) {
                onData(parsedData);
            }
        } catch (error) {
            if (onError) {
                onError(new Error(`解析数据失败: ${error.message}. 原始数据: ${event.data}`));
            }
        }
    };

    // 监听后端发送的自定义 'end' 事件
    eventSource.addEventListener('end', () => {
        eventSource.close();
        if (onEnd) {
            onEnd();
        }
    });

    // 监听后端发送的自定义 'error' 事件
    eventSource.addEventListener('error', (event) => {
        let errorMessage = '服务器发生未知错误。';
        try {
            const errorData = JSON.parse(event.data);
            errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
            errorMessage = `解析服务器错误数据失败: ${parseError.message}. 原始数据: ${event.data}`;
        }

        if (onError) {
            onError(new Error(errorMessage));
        }
        eventSource.close();
        if (onEnd) { // 在服务端出错时也需要更新UI状态
            onEnd();
        }
    });


    // 处理连接错误
    eventSource.onerror = (error) => {
        // onopen, onmessage, addEventListener 的回调中发生的错误，不会触发 onerror
        // onerror 只在网络层面出错，例如无法连接，或者违反同源策略时触发
        // 此时 EventSource 会自动尝试重连。
        // 如果是后端发送的自定义 error 或 end 事件，会先于 onerror 捕获，并关闭连接，阻止重连。
        // 如果执行到这里，说明是意外的网络错误。
        if (onError) {
            const detail = error.message ? `: ${error.message}` : '';
            onError(new Error(`流连接失败${detail}。请检查网络或稍后重试。`));
        }
        eventSource.close(); // 停止重连
        if (onEnd) {
            onEnd();
        }
    };

    return {
        abort: () => {
            eventSource.close();
        }
    };
}; 