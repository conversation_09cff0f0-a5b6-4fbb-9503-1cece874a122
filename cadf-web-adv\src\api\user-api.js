import api from "@/core/api/api";

const RESOURCE = "user_api";

export const userApi = {
    login: async (username, password) => {
        return await api({
            resource: RESOURCE,
            method_name: "login",
            data: {
                username,
                password,
            },
        });
    },

    delete: async (id_) => {
        return await api({
            resource: RESOURCE,
            method_name: "delete",
            data: {
                _id: id_,
            },
        });
    }
}