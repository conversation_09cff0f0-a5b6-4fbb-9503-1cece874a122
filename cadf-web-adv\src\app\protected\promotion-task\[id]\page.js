"use client"

import {use, useEffect, useState} from 'react'
import {Box, Button, CardContent, Chip, Container, LinearProgress, Stack, Typography} from '@mui/material'
import {ChevronLeft} from 'lucide-react'
import {useRouter} from 'next/navigation'
import TaskCard from '@/components/TaskCard'
import {advPromotionTaskApi} from '@/api/adv-promotion-task-api'
import InfiniteScrollList from '@/core/components/InfiniteScrollList'


export default function TaskDetail({params}) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState(null);
    const resolvedParams = use(params)
    const [materials, setMaterials] = useState([]);
    const [isLoadingMaterials, setIsLoadingMaterials] = useState(false);
    const [totalMaterialCount, setTotalMaterialCount] = useState(0);
    const [materialPage, setMaterialPage] = useState(1);
    const itemsPerPage = 8;
    const [hasMore, setHasMore] = useState(true);




    const fetchMaterials = async (taskId, page = 1, isLoadMore = false) => {
        if (!taskId) return;
        if (!isLoadMore) {
            setIsLoadingMaterials(true);
        }
        setError(null);
        try {
            const materialResponse = await advPromotionTaskApi.queryPromotionTaskDetails(
                taskId,
                page,
                itemsPerPage
            );
            if (materialResponse && Array.isArray(materialResponse.results)) {
                console.log('素材数据:', materialResponse);
                const newMaterials = materialResponse.results;
                setTotalMaterialCount(materialResponse.total || 0);
                
                if (isLoadMore) {
                    setMaterials(prev => [...prev, ...newMaterials]);
                } else {
                    setMaterials(newMaterials);
                }
                
                // 检查是否还有更多数据
                const totalPages = Math.ceil((materialResponse.total || 0) / itemsPerPage);
                setHasMore(page < totalPages);
            } else {
                console.error('获取素材列表失败: 响应数据无效', materialResponse);
                setError("获取素材列表失败: 响应数据无效");
                if (!isLoadMore) {
                    setMaterials([]);
                    setTotalMaterialCount(0);
                }
                setHasMore(false);
            }
        } catch (err) {
            console.error('获取素材列表失败:', err);
            const errorMessage = err?.response?.data?.message || err?.message || "获取素材列表时发生未知错误";
            setError(`获取素材列表失败: ${errorMessage}`);
            if (!isLoadMore) {
                setMaterials([]);
                setTotalMaterialCount(0);
            }
            setHasMore(false);
        } finally {
            if (!isLoadMore) {
                setIsLoadingMaterials(false);
            }
        }
    };


    useEffect(() => {
        const initialLoad = async () => {
            setIsLoading(true);
            await fetchMaterials(resolvedParams.id, 1, false);
            setIsLoading(false);
        }
        initialLoad();
    }, [resolvedParams.id]);




    if (isLoading) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <LinearProgress/>
                <Typography align="center" sx={{mt: 2}}>加载任务详情中...</Typography>
            </Container>
        );
    }


    if (error) {
        return (
            <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>
                <Box sx={{width: '100%', mt: 10, textAlign: 'center'}}>
                    <Typography variant="h5" color="error">
                        加载失败: {error}
                    </Typography>
                    <Button
                        variant="outlined"
                        startIcon={<ChevronLeft/>}
                        onClick={() => router.push('/protected/promotion-task')}
                        sx={{mt: 2}}
                    >
                        返回任务列表
                    </Button>
                </Box>
            </Container>
        );
    }




    const loadMoreMaterials = async () => {
        if (!hasMore || isLoadingMaterials) return;
        const nextPage = materialPage + 1;
        setMaterialPage(nextPage);
        await fetchMaterials(resolvedParams.id, nextPage, true);
    };

    return (
        <Container maxWidth={false} sx={{py: 4, px: {xs: 2, sm: 3, md: 4}}}>

            <Stack direction="row" spacing={2} alignItems="center" sx={{mb: 4}}>
                <Button
                    variant="outlined"
                    startIcon={<ChevronLeft/>}
                    onClick={() => router.push('/protected/promotion-task')}
                >
                    返回
                </Button>
                <Typography variant="h4" fontWeight="bold">
                    发布任务列表
                </Typography>
            </Stack>


            <Box sx={{mb: 3}}>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                    发布任务列表
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    总计 {totalMaterialCount} 个发布任务
                </Typography>
            </Box>


            {isLoadingMaterials && materials.length === 0 ? (
                <LinearProgress/>
            ) : (
                <InfiniteScrollList
                    items={materials}
                    loadMore={loadMoreMaterials}
                    hasMore={hasMore}
                    gridColumns={{
                        xs: 12,
                        sm: 6,
                        md: 3,
                        lg: 3
                    }}
                    renderItem={(material) => (
                        <TaskCard
                            task={material}
                            onClick={() => {
                                // 可以在这里添加点击任务卡片的逻辑
                            }}
                            onLinkClick={(url) => {
                                window.open(url, '_blank', 'noopener,noreferrer');
                            }}
                        />
                    )}
                />
            )}

        </Container>
    )
}