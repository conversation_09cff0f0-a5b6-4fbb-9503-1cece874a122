import random
from typing import Dict, Any, List
from omni.llm.output_agent import structured_output_handler
from pydantic import BaseModel, Field

from agent.state import OverallState
from omni.log.log import olog


class CaptionItem(BaseModel):
    """单条配文条目"""
    title: str = Field('', description="配文的标题（可为空）")
    contents: List[str] = Field(default_factory=list, description="配文的内容数组（可为空）")


class CaptionOutput(BaseModel):
    """生成配文的结构化输出，包含多个条目"""
    items: List[CaptionItem] = Field(default_factory=list, description="配文条目数组")


async def generate_caption(state: OverallState) -> Dict[str, Any]:
    """生成配文节点
    
    Args:
        state: 整体状态对象，包含OCR文字和产品介绍信息
        
    Returns:
        包含生成配文条目的字典
    """
    olog.info("开始执行配文生成节点")
    
    ocr_text: str = state.ocr_text
    product_intro: str = state.product_intro
    group_num: int = random.choices([1, 2, 3], weights=[0.4, 0.4, 0.2])[0]
    
    olog.debug(f"输入参数 - OCR文字: {ocr_text}, 产品介绍: {product_intro}, 配文组数: {group_num}")

    prompt: str = """
# 角色
你是一名创意文案专家，擅长为图片生成吸引人的艺术字配文。

# 背景信息
- 图片中的艺术字内容: "{ocr_text}"
- 用户提供的额外信息: "{product_intro}"

# 任务
根据上述背景信息，生成一组或多组全新的、有创意、吸引人的图片艺术字配文。
配文的字数与段数与'图片中的艺术字内容'相似
每组配文包含一个标题（可以为空，根据实际情况判断是否生成标题）和若干配文内容。
合理的使用带emoji的小红书配文风格
小红书的emoji一般出现在开头
emoji与文字之间要有一个空格
不要每一个配文内容都有emoji表情
emoji规避黑色等负面颜色
配文标题不超过10个字
配文内容不超过10个字
每组配文内容的个数不超过2个
配文组数限制在{group_num}组
"""

    structured_result: CaptionOutput = await structured_output_handler(
        prompt_template=prompt,
        params={"ocr_text": ocr_text, "product_intro": product_intro, "group_num": group_num},
        output_model=CaptionOutput,
        llm_name="QWEN_PLUS"
    )
    
    olog.info(f"配文生成完成，共生成 {len(structured_result.items)} 组配文")
    
    return {
        "caption_items": structured_result.model_dump()["items"]
    }