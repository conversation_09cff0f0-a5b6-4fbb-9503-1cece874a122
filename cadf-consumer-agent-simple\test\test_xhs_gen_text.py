import os
import sys
import asyncio

from omni.mongo.mongo_client import init_models

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# 添加 common-module 到路径
common_module_path = os.path.abspath(os.path.join(project_root, '../common-module'))
sys.path.insert(0, common_module_path)

from consumers.xhs_gen_text_consumer import handle_task

async def test_xhs_gen_text():
    await init_models()

    """测试小红书文本生成消费者"""
    # 模拟消息数据
    test_message = {
        "id_": "6873a607de5224842167917b"  # 替换为实际的 AI 生成材料 ID
    }
    
    print(f"开始测试小红书文本生成任务，消息数据: {test_message}")
    
    try:
        await handle_task(test_message)
        print("测试完成")
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_xhs_gen_text())