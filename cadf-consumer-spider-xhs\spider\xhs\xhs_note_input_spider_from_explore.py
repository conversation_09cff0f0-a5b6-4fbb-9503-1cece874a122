"""
小红书写入爬虫
用于点赞,收藏,转发,评论相关写入操作
必须由主页模糊搜索后刷到帖子进入
不可精确搜索
"""

import asyncio
from typing import List

from omni.exception.retry_decorator import retry_on_exception
from omni.log.log import olog
from spider.schemas.xhs_homefeed_response_schema import XHSHomefeedResponse
from spider.tools.browser_context import new_playwright_page
from config.config import SpiderConfig

# --------------------
# 小红书页面元素选择器 (Playwright版本)
# --------------------
# 笔记列表页面-搜索输入框的定位器
SEARCH_INPUT_SELECTOR = 'input[placeholder*="搜索小红书"]'
# 笔记列表页面-搜索按钮的CSS选择器
SEARCH_ICON_SELECTOR = ".search-icon"
# 笔记列表页面-笔记列表容器的CSS选择器
NOTE_LIST_SELECTOR = ".search-layout__main"
# 笔记列表页面-单个笔记项的CSS选择器
NOTE_ITEM_SELECTOR = "section.note-item"

# 笔记详情页面
# 笔记详情页面-滚动容器的CSS选择器
NOTE_SCROLLER_SELECTOR = ".note-scroller"
# 笔记详情页面-评论输入框, 通常是textarea，带有特定占位符
COMMENT_INPUT_SELECTOR = 'textarea[placeholder*="说点什么"]'
# 笔记详情页面-评论发布按钮, 通过文本 "发布" 定位
COMMENT_SUBMIT_SELECTOR = 'text=发布'

@retry_on_exception(max_retries=3)
async def search_and_comment(
        search_keyword: str,
        filter_keyword: str,
        comment_text: str,
        cookies: List[dict]
) -> bool:
    """
    搜索指定关键字并对筛选到的第一篇笔记进行评论 - Playwright版本
    
    :param search_keyword: 搜索关键字
    :param filter_keyword: 筛选关键字
    :param comment_text: 评论内容
    :param cookies: 登录cookies
    :return: 操作是否成功
    """
    olog.info(f"开始执行小红书评论任务，搜索关键字：{search_keyword}，筛选关键字：{filter_keyword}")

    api_pattern = "/api/sns/web/v1/homefeed"

    async with new_playwright_page(
        cookies=cookies,
        headless=SpiderConfig.HEADLESS,
        use_proxy=SpiderConfig.USE_PROXY,
        no_imgs=SpiderConfig.NO_IMGS
    ) as page:
        
        # 导航到小红书首页
        await page.goto("https://www.xiaohongshu.com", wait_until="domcontentloaded")

        olog.debug("正在定位搜索框")
        search_input = await page.wait_for_selector(SEARCH_INPUT_SELECTOR, timeout=10000)

        olog.debug(f"输入搜索关键字：{search_keyword}")
        await search_input.fill(search_keyword)

        search_icon = await page.wait_for_selector(SEARCH_ICON_SELECTOR, timeout=5000)
        await search_icon.click()
        olog.debug("已点击搜索按钮")

        await page.wait_for_selector(NOTE_LIST_SELECTOR, timeout=10000)
        olog.debug("搜索结果页面加载完成")

        olog.debug(f"开始查找包含'{filter_keyword}'的笔记, 监听API: {api_pattern}")

        for i in range(10):  # 最多滚动10次
            olog.debug(f"第 {i + 1} 次滚动并监听API")
            
            # 监听API响应
            async with page.expect_response(
                lambda r: api_pattern in r.url,
                timeout=5000
            ) as response_info:
                # 滚动页面以触发新的API请求
                await page.evaluate("window.scrollBy(0, 800)")
                await asyncio.sleep(1)

            try:
                response = await response_info.value
                response_data = await response.json()
                validated_response = XHSHomefeedResponse.model_validate(response_data)

                for item in validated_response.data.items:
                    note_card = item.note_card
                    if filter_keyword in note_card.display_title:
                        olog.debug(f"找到匹配笔记：{note_card.display_title[:50]}...")
                        
                        # 使用笔记ID构建链接并点击
                        note_id = item.id
                        note_link_selector = f'a[href*="{note_id}"]'
                        
                        try:
                            note_link = await page.wait_for_selector(note_link_selector, timeout=5000)
                            await note_link.click()
                            await asyncio.sleep(3)

                            olog.debug("等待笔记详情页加载")
                            await page.wait_for_selector(NOTE_SCROLLER_SELECTOR, timeout=10000)

                            olog.debug("定位评论输入框")
                            comment_input = await page.wait_for_selector(COMMENT_INPUT_SELECTOR, timeout=10000)

                            olog.debug(f"输入评论内容：{comment_text}")
                            await comment_input.fill(comment_text)
                            await asyncio.sleep(0.5)

                            olog.debug("点击发布按钮")
                            submit_button = await page.wait_for_selector(COMMENT_SUBMIT_SELECTOR, timeout=5000)
                            await submit_button.click()

                            olog.info("评论发布成功")
                            await asyncio.sleep(2)
                            return True
                            
                        except Exception as e:
                            olog.error(f"在页面上找不到ID为 {note_id} 的笔记元素或评论失败: {e}")
                            continue

                olog.debug("当前批次未找到匹配笔记，准备再次滚动")
                await asyncio.sleep(2)  # 等待一下，避免过快滚动
                
            except Exception as e:
                olog.warning(f"在第 {i + 1} 次滚动后，未监听到API响应或解析失败: {e}")
                continue

        olog.warning(f"滚动10次后仍未找到包含'{filter_keyword}'的笔记")
        return False
