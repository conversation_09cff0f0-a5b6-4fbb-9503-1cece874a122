from contextlib import asynccontextmanager
from typing import Dict, Optional, AsyncGenerator

from playwright.async_api import async_playwright, Page

from omni.log.log import olog
from spider.tools.proxy_server import ProxyServer
from config.config import SpiderConfig


@asynccontextmanager
async def new_playwright_page(
        cookies: Optional[list[Dict]] = None,
        headless: bool = False,
        use_proxy: bool = False,
        no_imgs: bool = False
) -> AsyncGenerator[Page, None]:
    """
    一个管理Playwright浏览器生命周期的异步上下文管理器。
    它负责初始化浏览器，并在退出时自动关闭浏览器。
    可选择性地设置cookies。

    :param cookies: 可选，cookies列表
    :param headless: 是否以无头模式运行浏览器
    :param use_proxy: 是否使用代理服务器
    :param no_imgs: 是否不加载图片
    """
    async with async_playwright() as p:
        # 浏览器启动选项 - 添加反webdriver检测参数
        launch_options = {
            "headless": headless,
            "args": [
                "--window-size=1920,1080",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                # 反webdriver检测参数
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-default-apps",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--no-first-run",
                "--no-default-browser-check",
                "--no-pings",
                "--password-store=basic",
                "--use-mock-keychain",
                "--exclude-switches=enable-automation",
                "--disable-blink-features=AutomationControlled"
            ]
        }

        # 启动浏览器
        browser = await p.chromium.launch(**launch_options)

        # 创建上下文
        context_options = {
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "viewport": {"width": 1920, "height": 1080},
            "extra_http_headers": {
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        }

        # 设置代理
        proxy_config = None
        if use_proxy:
            olog.info(f"尝试使用代理服务器 (当前代理类型: {SpiderConfig.CURRENT_PROXY_TYPE})")
            proxy_server_manager = ProxyServer()
            proxy_url = await proxy_server_manager.get_proxy_ip()

            if proxy_url:
                olog.debug("检测到代理配置，正在设置代理")
                username, password = proxy_server_manager.get_proxy_credentials()
                
                if proxy_url.startswith("http://"):
                    proxy_config = {
                        "server": proxy_url,
                        "username": username,
                        "password": password
                    }
                else:
                    proxy_config = {
                        "server": f"http://{proxy_url}",
                        "username": username,
                        "password": password
                    }
                olog.debug(f"代理设置成功: {proxy_url} (类型: {SpiderConfig.CURRENT_PROXY_TYPE})")
            else:
                olog.warning("获取代理服务器失败，将不使用代理")
        else:
            olog.info("未启用代理，使用直连模式访问网络")

        if proxy_config:
            context_options["proxy"] = proxy_config

        context = await browser.new_context(**context_options)

        # 设置不加载图片和视频
        if no_imgs:
            await context.route("**/*", lambda route, request: (
                route.abort() if request.resource_type in ["image", "media"] else route.continue_()
            ))

        # 设置cookies
        if cookies:
            await context.add_cookies(cookies)
            olog.debug("已设置cookies")

        # 创建页面
        page = await context.new_page()

        try:
            yield page
        finally:
            await page.close()
            await context.close()
            await browser.close()
