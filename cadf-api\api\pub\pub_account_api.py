import time
from typing import List, Optional, Dict, Any

from beanie import PydanticObjectId
from beanie.odm.operators.update.general import Set

from models.models import Account
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog


@register_handler('pub_account_api')
class PubAccountApi:
    async def create(self, data: Dict[str, Any]) -> None:
        user_id = data.get('user_id')
        platform = data.get('platform')
        cookie = data.get('cookie')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not platform:
            raise MException("platform 不能为空")
        if not cookie:
            raise MException("cookie 不能为空")
        
        olog.debug(cookie)

        account = Account(
            user_id=user_id,
            platform=platform,
            cookie=cookie,
            status="在线",
            create_at=int(time.time())
        )
        await account.insert()

    @auth_required(['creator', 'admin'])
    async def modify(self, data: Dict[str, Any]) -> None:
        id_ = data.pop('_id', None)
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        existing_account = await Account.find_one(Account.id == PydanticObjectId(id_))
        allowed_fields = ['name', 'domain']
        update_data = {k: v for k, v in data.items() if k in allowed_fields}


        await existing_account.update(Set(update_data))

    @auth_required(['creator', 'admin'])
    async def delete(self, data: Dict[str, Any]) -> None:
        id_ = data.get('_id')
        user_id = data.get('user_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        object_id = PydanticObjectId(id_)
        # 先查找记录，然后删除
        account = await Account.find_one(Account.id == object_id)
        
        if not account:
            raise MException("未找到要删除的记录")
            
        await account.delete()

    @auth_required(['creator', 'admin'])
    async def query_one(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        id_ = data.get('_id')
        object_id = PydanticObjectId(id_)
        account = await Account.find_one(Account.id == object_id)
        return account.to_dict() if account else None

    @auth_required(['creator', 'admin'])
    async def query_all(self, data: Dict[str, Any]) -> Dict[str, Any]:
        user_id = data.get('user_id')
        platform_filter = data.get('platform')

        match_stage = {"$match": {"user_id": user_id}}
        if platform_filter:
            match_stage["$match"]["platform"] = platform_filter

        pipeline = [
            match_stage,
            {"$sort": {"create_at": -1}},
            {"$addFields": {"_id": {"$toString": "$_id"}}},
            {"$unset": "id"}
        ]

        accounts: List[Dict[str, Any]] = await Account.aggregate(pipeline).to_list()

        return {
            "total": len(accounts),
            "results": accounts
        }
