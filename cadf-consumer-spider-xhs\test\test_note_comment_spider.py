"""
小红书笔记评论爬虫测试例子
"""
import asyncio
import json
from typing import List

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from spider.xhs.xhs_note_comment_spider_from_url import crawl_comments_from_url
from spider.schemas.comment_thread_schema import CommentThread
from models.models import Account


async def test_note_comment_spider():
    """
    测试小红书笔记评论爬虫
    """
    try:
        await init_models()
        # 从数据库获取指定ID的账户cookie
        account = await Account.get("68724c33435ce73710ab0bc8")
        if not account or not account.cookie:
            olog.error("未找到ID为123123的账户或该账户没有cookie")
            return
        
        cookies = account.cookie
        olog.info(f"使用账户 {account.name} 的cookie进行测试")
        
        # 测试用的小红书笔记URL - 请替换为实际的URL
        test_url = "http://xhslink.com/m/5hXDOGNpflu"
        olog.info(f"测试URL: {test_url}")

        # 调用爬虫函数
        comments, is_logged_in = await crawl_comments_from_url(
            url=test_url,
            cookies=cookies
        )
        
        # 打印结果
        olog.info(f"成功获取 {len(comments)} 条评论，登录状态: {is_logged_in}")
        olog.info("评论数据爬取结果:")
        
        # 使用model_dump将comments转化为JSON
        comments_dict = [comment.model_dump() for comment in comments]
        formatted_json = json.dumps(comments_dict, ensure_ascii=False, indent=2, default=str)
        # olog.info(formatted_json)
            
    except Exception as e:
        olog.exception(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_note_comment_spider())