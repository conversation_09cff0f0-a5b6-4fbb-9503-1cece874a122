"use client";

import { useState, useEffect } from 'react';
import React from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Container,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ArrowLeft, Save } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { advertiserManagementApi } from "@/api/advertiser-management-api";

export default function EditAdvertiser({ params }) {
  const resolvedParams = React.use(params);
  const theme = useTheme();
  const router = useRouter();
  const dispatch = useDispatch();

  // 表单数据
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);

  // 加载广告主详情
  const loadAdvertiserDetail = async () => {
    setDataLoading(true);
    try {
      const advertiser = await advertiserManagementApi.queryDetail(resolvedParams.id);
      setFormData({
        username: advertiser.username,
        password: '',
        confirmPassword: '',
      });
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '加载广告主信息失败' 
      }));
      router.push('/protected/advertiser-management');
    } finally {
      setDataLoading(false);
    }
  };

  useEffect(() => {
    if (resolvedParams.id) {
      loadAdvertiserDetail();
    }
  }, [resolvedParams.id]);

  // 表单验证
  const validateForm = () => {
    const newErrors = {};
    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }
    // 如果输入了密码，则需要验证确认密码
    if (formData.password.trim()) {
      if (!formData.confirmPassword.trim()) {
        newErrors.confirmPassword = '请确认新密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致';
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      await advertiserManagementApi.update(
        resolvedParams.id, 
        formData.username, 
        formData.password || null
      );
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.MODIFY }));
      router.push('/protected/advertiser-management');
    } catch (error) {
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || '更新广告主失败' 
      }));
    } finally {
      setLoading(false);
    }
  };

  // 处理返回
  const handleBack = () => {
    router.push('/protected/advertiser-management');
  };

  if (dataLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      {/* 返回按钮 */}
      <Box sx={{ mb: 3 }}>
        <Button
          startIcon={<ArrowLeft size={18} />}
          onClick={handleBack}
          variant="text"
          sx={{ color: 'text.secondary' }}
        >
          返回
        </Button>
      </Box>

      <Container maxWidth="sm">
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Typography variant="h5" component="h1" fontWeight="bold">
            编辑广告主
          </Typography>
        </Box>

        {/* 表单 */}
        <Paper 
          elevation={0} 
          sx={{ 
            p: 3,
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                label="用户名"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                fullWidth
                required
                error={!!errors.username}
                helperText={errors.username}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="新密码"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                fullWidth
                helperText="留空则不修改密码"
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="确认新密码"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                fullWidth
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  disabled={loading}
                >
                  取消
                </Button>
                <Button
                  variant="contained"
                  startIcon={<Save size={18} />}
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  {loading ? '保存中...' : '保存'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Container>
    </Box>
  );
} 