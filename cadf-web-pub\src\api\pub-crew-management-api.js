import api from "@/core/api/api";

const RESOURCE = "pub_crew_management";

export const pubCrewManagementApi = {

    generateInviteCode: async () => {
        return await api({
            resource: RESOURCE,
            method_name: "generate_invite_code",
            data: {},
        });
    },

    queryCrewStatsWithCount: async () => {
        return await api({
            resource: RESOURCE,
            method_name: "query_crew_stats_with_count",
            data: {},
        });
    },

    queryCrewRevenueRanking: async (page, rowsPerPage) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_crew_revenue_ranking",
            data: {
                page,
                page_size: rowsPerPage,
            },
        });
    },

    queryCrewListUnified: async (statusFilter, page, rowsPerPage) => {
        return await api({
            resource: RESOURCE,
            method_name: "query_crew_list_unified",
            data: {
                status_filter: statusFilter,
                page,
                page_size: rowsPerPage,
            },
        });
    },


    joinFleet: async (fleetCode) => {
        return await api({
            resource: RESOURCE,
            method_name: "join_fleet",
            data: {
                fleet_code: fleetCode,
            },
        });
    }
};