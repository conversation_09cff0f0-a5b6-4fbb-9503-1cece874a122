"use client";

import {Box, Grid, Paper, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import {Area, AreaChart, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis} from 'recharts';

// 模拟趋势数据
const mockTrendData = {
    comments: [
        {day: '周一', value: 165},
        {day: '周二', value: 190},
        {day: '周三', value: 210},
        {day: '周四', value: 188},
        {day: '周五', value: 240},
        {day: '周六', value: 250},
        {day: '周日', value: 280},
    ],
    intentCustomers: [
        {day: '周一', value: 35},
        {day: '周二', value: 42},
        {day: '周三', value: 56},
        {day: '周四', value: 48},
        {day: '周五', value: 53},
        {day: '周六', value: 65},
        {day: '周日', value: 72},
    ],
    postedComments: [
        {day: '周一', value: 28},
        {day: '周二', value: 36},
        {day: '周三', value: 42},
        {day: '周四', value: 45},
        {day: '周五', value: 51},
        {day: '周六', value: 58},
        {day: '周日', value: 62},
    ]
};

const DataTrendAnalysis = ({ trendData = mockTrendData }) => {
    const theme = useTheme();

    // 预处理数据，将三个趋势数据合并为一个数据集
    const combinedData = trendData.comments.map((item, index) => ({
        day: item.day,
        评论数量: item.value,
        意向客户: trendData.intentCustomers[index].value,
        布评总数: trendData.postedComments[index].value,
    }));

    return (
        <Grid container spacing={3} sx={{width: '100%', mb: 4}}>
            <Grid size={12}>
                <Paper
                    elevation={0}
                    sx={{
                        p: 3,
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`,
                        mb: 4
                    }}
                >
                    <Typography variant="h6" gutterBottom>数据趋势分析</Typography>
                    <Box sx={{height: 300, mt: 2}}>
                        <ResponsiveContainer width="100%" height="100%">
                            <AreaChart
                                data={combinedData}
                                margin={{top: 10, right: 30, left: 0, bottom: 5}}
                            >
                                <defs>
                                    <linearGradient id="colorComments" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
                                        <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.1}/>
                                    </linearGradient>
                                    <linearGradient id="colorIntent" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={theme.palette.success.main} stopOpacity={0.8}/>
                                        <stop offset="95%" stopColor={theme.palette.success.main} stopOpacity={0.1}/>
                                    </linearGradient>
                                    <linearGradient id="colorPosted" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={theme.palette.info.main} stopOpacity={0.8}/>
                                        <stop offset="95%" stopColor={theme.palette.info.main} stopOpacity={0.1}/>
                                    </linearGradient>
                                </defs>
                                <XAxis
                                    dataKey="day"
                                    axisLine={{stroke: theme.palette.divider}}
                                    tick={{fill: theme.palette.text.secondary, fontSize: 12}}
                                />
                                <YAxis
                                    axisLine={{stroke: theme.palette.divider}}
                                    tick={{fill: theme.palette.text.secondary, fontSize: 12}}
                                />
                                <Tooltip
                                    contentStyle={{
                                        borderRadius: 8,
                                        boxShadow: theme.shadows[2],
                                        border: 'none'
                                    }}
                                />
                                <Legend/>
                                <Area
                                    type="monotone"
                                    dataKey="评论数量"
                                    name="评论数量"
                                    stroke={theme.palette.primary.main}
                                    fill="url(#colorComments)"
                                    fillOpacity={0.3}
                                    strokeWidth={2}
                                    activeDot={{r: 6}}
                                />
                                <Area
                                    type="monotone"
                                    dataKey="意向客户"
                                    name="意向客户"
                                    stroke={theme.palette.success.main}
                                    fill="url(#colorIntent)"
                                    fillOpacity={0.3}
                                    strokeWidth={2}
                                    activeDot={{r: 6}}
                                />
                                <Area
                                    type="monotone"
                                    dataKey="布评总数"
                                    name="布评总数"
                                    stroke={theme.palette.info.main}
                                    fill="url(#colorPosted)"
                                    fillOpacity={0.3}
                                    strokeWidth={2}
                                    activeDot={{r: 6}}
                                />
                            </AreaChart>
                        </ResponsiveContainer>
                    </Box>
                </Paper>
            </Grid>
        </Grid>
    );
};

export default DataTrendAnalysis; 