from typing import Any, Dict, List

from beanie import PydanticObjectId

from models.models import DataDictionary
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.log.log import olog


@register_handler('data_dictionary')
class DataDictionaryApi:

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """
        查询数据字典条目，可通过 category 筛选。
        """
        category_filter = data.get('category')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        query_conditions = []
        if category_filter:
            query_conditions.append(DataDictionary.category == category_filter)

        query = DataDictionary.find(*query_conditions)

        dictionaries = await query.skip((page - 1) * page_size).limit(page_size).to_list()

        # 计算总条数
        total = await DataDictionary.find(*query_conditions).count()

        dictionary_dicts = [dictionary.to_dict() for dictionary in dictionaries]
        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=dictionary_dicts
        )

    @auth_required(['admin'])
    async def create(self, data: Dict[str, Any]):
        """
        创建数据字典条目
        """
        category = data.get('category')
        key = data.get('key')
        value = data.get('value')

        # 输入验证
        if not category:
            raise MException("category 不能为空")
        if not key:
            raise MException("key 不能为空")
        if not value:
            raise MException("value 不能为空")

        olog.info(f"创建数据字典条目: category={category}, key={key}")

        dictionary = DataDictionary(
            category=category,
            key=key,
            value=value
        )
        await dictionary.insert()

        olog.info(f"数据字典条目创建成功: {dictionary.id}")

    @auth_required(['admin'])
    async def modify(self, data: Dict[str, Any]):
        """
        更新数据字典条目
        """
        id_ = data.get('_id')
        category = data.get('category')
        key = data.get('key')
        value = data.get('value')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")
        if not category:
            raise MException("category 不能为空")
        if not key:
            raise MException("key 不能为空")
        if not value:
            raise MException("value 不能为空")

        olog.info(f"更新数据字典条目: id={id_}")

        dictionary = await DataDictionary.find_one(DataDictionary.id == PydanticObjectId(id_))
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise MException("数据字典条目不存在")

        dictionary.category = category
        dictionary.key = key
        dictionary.value = value
        await dictionary.save()

        olog.info(f"数据字典条目更新成功: {dictionary.id}")

    @auth_required(['admin'])
    async def delete(self, data: Dict[str, Any]):
        """
        删除数据字典条目
        """
        id_ = data.get('_id')

        # 输入验证
        if not id_:
            raise MException("_id 不能为空")

        olog.info(f"删除数据字典条目: id={id_}")

        dictionary = await DataDictionary.find_one(DataDictionary.id == PydanticObjectId(id_))
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise MException("数据字典条目不存在")

        await dictionary.delete()
        olog.info(f"数据字典条目删除成功: id={id_}")

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    async def get_categories(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        获取所有数据字典类别
        """
        pipeline = [
            {"$group": {"_id": "$category"}},
            {"$project": {"category": "$_id", "_id": 0}}
        ]

        result = await DataDictionary.aggregate(pipeline).to_list()
        categories = [item['category'] for item in result if item.get('category')]
        return {"categories": categories}

    @auth_required(['admin', 'creator', 'advertiser', 'captain', 'crew'])
    async def get_by_id(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据ID获取数据字典条目
        """
        id_ = data.get('_id')

        olog.info(f"查询数据字典条目: id={id_}")

        dictionary = await DataDictionary.find_one(DataDictionary.id == PydanticObjectId(id_))
        if not dictionary:
            olog.error(f"数据字典条目不存在: id={id_}")
            raise MException("数据字典条目不存在")

        return dictionary.to_dict()
