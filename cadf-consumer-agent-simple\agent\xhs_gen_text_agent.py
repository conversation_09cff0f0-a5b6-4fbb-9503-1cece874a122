import asyncio
from typing import Dict, Any
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from pydantic import BaseModel, Field

"""
生成小红书爆文智能体
"""


# 定义输出的数据结构
class XhsOutput(BaseModel):
    """小红书爆文的结构化输出"""
    title: str = Field(..., description="生成的小红书爆文标题")
    content: str = Field(..., description="生成的小红书爆文正文内容")


async def gen_xhs_text(
        product_info: str,
        benchmark_title: str,
        benchmark_content: str,
) -> XhsOutput:
    """
    根据输入参数生成小红书风格的文本，并解析为结构化数据。

    Args:
        product_info: 详细的产品信息描述。
        benchmark_title: 对标或参考的小红书笔记标题。
        benchmark_content: 对标或参考的小红书笔记正文。

    Returns:
        包含标题和内容的 XhsOutput 对象。
    """
    olog.info("开始生成小红书文本")

    # 定义适用于小红书风格文本生成的提示模板
    xhs_prompt_template = """
    # 角色
    你是一位经验丰富的小红书内容创作者和文案专家，擅长分析文本结构并生成富有创意的内容。

    # 背景
    我将分别提供原始爆文的标题（原始标题）和内容（原始内容），以及一个新产品或主题的信息（新主题）。

    原始标题：
    ```
    {benchmark_title}
    ```

    原始内容：
    ```
    {benchmark_content}
    ```

    新主题：
    ```
    {product_info}
    ```

    # 任务
    请模仿"原始内容"的风格和结构，并参考"原始标题"的风格，为"新主题"创作一篇新的小红书爆文。
    你需要分别生成新的标题和新的内容。
    请投入更多思考，确保新文案在保持原文结构和风格的同时，针对新主题进行了最合适的调整，力求高质量。
    标题的长度不能超过20个字符。
    如果要使用emoji表情，不要使用小红书的emoji表情，请使用输入法自带的emoji表情。
    请不要使用话题标签。
    """

    params: Dict[str, Any] = {
        "product_info": product_info,
        "benchmark_title": benchmark_title,
        "benchmark_content": benchmark_content,
    }

    structured_result = await structured_output_handler(
        prompt_template=xhs_prompt_template,
        params=params,
        output_model=XhsOutput,
        tags=["xhs_text_generator"]
    )
    olog.info("小红书文本生成成功")
    return structured_result
