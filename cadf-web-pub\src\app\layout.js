"use client";

import {reduxStore} from "@/core/components/redux-store";
import {PROJECT_NAME} from "@/config";
import DefaultSkeleton from "@/core/components/skeleton";
import {Container} from "@mui/material";
import {AppRouterCacheProvider} from "@mui/material-nextjs/v13-appRouter";
import {ThemeProvider} from '@mui/material/styles';
import theme from '@/theme';
import Head from "next/head";
import {Suspense} from "react";
import {Provider} from "react-redux";
import "./globals.css";
import {SnackbarProvider} from "notistack";

export default function RootLayout({children}) {
    return (
        <html lang="en">
        <Head>
            <title>{PROJECT_NAME}</title>
        </Head>
        <body>
        <div>
            <AppRouterCacheProvider>
                <ThemeProvider theme={theme}>
                    <Suspense fallback={<DefaultSkeleton/>}>
                        <SnackbarProvider anchorOrigin={{vertical: 'bottom', horizontal: 'right'}}>
                            <Provider store={reduxStore}>
                                <Container
                                    maxWidth={false}
                                    disableGutters
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        minHeight: "100vh",
                                    }}
                                >
                                    {children}
                                </Container>
                            </Provider>
                        </SnackbarProvider>
                    </Suspense>
                </ThemeProvider>
            </AppRouterCacheProvider>
        </div>
        </body>
        </html>
    );
}
