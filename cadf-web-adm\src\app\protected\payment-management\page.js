"use client";

import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Grid,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Send, Search, FileText } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType } from "@/core/components/alert";

export default function AlipayPayment() {
  const theme = useTheme();
  const dispatch = useDispatch();
  
  // 状态
  const [paymentForm, setPaymentForm] = useState({
    account: '',
    name: '',
    amount: '',
    remark: '',
  });
  
  const [records, setRecords] = useState([
    { 
      id: 1, 
      account: '<EMAIL>', 
      name: '张三', 
      amount: 2000, 
      date: '2023-06-15', 
      status: '成功' 
    },
    { 
      id: 2, 
      account: '<EMAIL>', 
      name: '李四', 
      amount: 1500, 
      date: '2023-06-14', 
      status: '成功' 
    },
    { 
      id: 3, 
      account: '<EMAIL>', 
      name: '王五', 
      amount: 3000, 
      date: '2023-06-13', 
      status: '失败' 
    },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');

  // 处理表单输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPaymentForm({
      ...paymentForm,
      [name]: value
    });
  };

  // 处理提交
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // 模拟提交
    const newRecord = {
      id: records.length + 1,
      account: paymentForm.account,
      name: paymentForm.name,
      amount: Number(paymentForm.amount),
      date: new Date().toISOString().split('T')[0],
      status: '处理中'
    };
    
    setRecords([newRecord, ...records]);
    
    // 重置表单
    setPaymentForm({
      account: '',
      name: '',
      amount: '',
      remark: '',
    });
    
    // 显示成功通知
    dispatch(addAlert({ type: AlertType.SUCCESS, message: '支付请求已提交' }));
  };
  
  // 过滤记录
  const filteredRecords = records.filter(record => {
    // 搜索过滤
    const matchesSearch = 
      record.account.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    // 日期过滤
    let matchesDate = true;
    if (dateFilter === 'today') {
      matchesDate = record.date === new Date().toISOString().split('T')[0];
    } else if (dateFilter === 'week') {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      matchesDate = new Date(record.date) >= oneWeekAgo;
    } else if (dateFilter === 'month') {
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      matchesDate = new Date(record.date) >= oneMonthAgo;
    }
    
    return matchesSearch && matchesDate;
  });

  return (
    <Box>
      <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: 4 }}>
        支付宝打款
      </Typography>
      
      <Grid container spacing={3}>
        {/* 打款表单 */}
        <Grid item xs={12} md={4}>
          <Card 
            elevation={0} 
            sx={{ 
              height: '100%',
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                创建新打款
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                <TextField
                  label="支付宝账号"
                  name="account"
                  value={paymentForm.account}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  required
                />
                
                <TextField
                  label="收款人姓名"
                  name="name"
                  value={paymentForm.name}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  required
                />
                
                <TextField
                  label="金额(元)"
                  name="amount"
                  type="number"
                  value={paymentForm.amount}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  required
                  inputProps={{ min: 0, step: 0.01 }}
                />
                
                <TextField
                  label="备注"
                  name="remark"
                  value={paymentForm.remark}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  multiline
                  rows={3}
                />
                
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  sx={{ mt: 3 }}
                  startIcon={<Send size={18} />}
                >
                  提交打款
                </Button>
              </form>
            </CardContent>
          </Card>
        </Grid>
        
        {/* 打款记录 */}
        <Grid item xs={12} md={8}>
          <Paper
            elevation={0}
            sx={{ 
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
              overflow: 'hidden'
            }}
          >
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                打款记录
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  placeholder="搜索账号或姓名"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search size={18} style={{ marginRight: 8 }} />,
                  }}
                  sx={{ width: 200 }}
                />
                
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel id="date-filter-label">时间范围</InputLabel>
                  <Select
                    labelId="date-filter-label"
                    value={dateFilter}
                    label="时间范围"
                    onChange={(e) => setDateFilter(e.target.value)}
                  >
                    <MenuItem value="all">全部</MenuItem>
                    <MenuItem value="today">今天</MenuItem>
                    <MenuItem value="week">本周</MenuItem>
                    <MenuItem value="month">本月</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
            
            <Divider />
            
            <TableContainer>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                    <TableCell>ID</TableCell>
                    <TableCell>支付宝账号</TableCell>
                    <TableCell>姓名</TableCell>
                    <TableCell>金额(元)</TableCell>
                    <TableCell>日期</TableCell>
                    <TableCell>状态</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredRecords.length > 0 ? (
                    filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{record.id}</TableCell>
                        <TableCell>{record.account}</TableCell>
                        <TableCell>{record.name}</TableCell>
                        <TableCell>{record.amount.toFixed(2)}</TableCell>
                        <TableCell>{record.date}</TableCell>
                        <TableCell>
                          <Chip 
                            label={record.status} 
                            size="small"
                            color={
                              record.status === '成功' ? 'success' : 
                              record.status === '失败' ? 'error' : 
                              'default'
                            }
                            variant={record.status === '处理中' ? 'outlined' : 'filled'}
                          />
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                          <FileText size={24} color={theme.palette.text.secondary} />
                          <Typography color="text.secondary">
                            没有找到匹配的记录
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
} 