import time

from models.models import CostUserAccount, CostRechargeRecord
# Removed: from decimal import Decimal
from omni.log.log import olog


async def _get_or_create_user_account(user_id: str) -> CostUserAccount:
    """
    获取用户账户，如果账户不存在则创建新账户。

    Args:
        user_id: 用户ID。

    Returns:
        CostUserAccount: 用户账户对象。
    """
    user_account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
    if not user_account:
        user_account = CostUserAccount(
            user_id=user_id,
            balance=0.0,  # Changed from Decimal('0')
            updated_at=int(time.time())
        )
        await user_account.insert()
        olog.info(f"由于账户原先不存在，已为用户 {user_id} 创建新账户，初始余额为 0。")
    return user_account


async def check_balance_exceeds(user_id: str, amount_to_check: float) -> bool:
    """
    检查用户余额是否超过指定金额。如果账户不存在，则创建新账户并返回 False。

    Args:
        user_id: 用户ID。
        amount_to_check: 需要检查的金额 (float)。

    Returns:
        如果余额大于或等于指定金额，则返回 True，否则返回 False。
        如果账户是新创建的（余额为0），则返回 False。
    """
    user_account = await _get_or_create_user_account(user_id)
    # 如果账户是新创建的，其余额为0，直接比较即可
    return user_account.balance >= amount_to_check


async def get_user_balance(user_id: str) -> float:
    """
    查询用户余额，如果账户不存在则创建新账户并返回余额。

    Args:
        user_id: 用户ID。

    Returns:
        用户的余额 (float)。
    """
    user_account = await _get_or_create_user_account(user_id)
    return user_account.balance


async def process_recharge(user_id: str, amount: float, recharge_method="手工"):
    """
    一键处理充值：创建充值记录 + 更新用户余额

    Args:
        user_id: 用户ID
        amount: 充值金额 (float)
        recharge_method: 充值方式，默认"手工"

    Returns:
        dict: 包含充值记录ID和更新后的余额
    """
    olog.info(f"开始处理用户 {user_id} 充值 {amount} 元，充值方式：{recharge_method}")

    # 获取或创建用户账户
    user_account = await CostUserAccount.find_one(CostUserAccount.user_id == user_id)
    if not user_account:
        user_account = CostUserAccount(
            user_id=user_id,
            balance=0.0,  # Changed from Decimal('0')
            updated_at=int(time.time())
        )
        await user_account.insert()
        olog.info(f"为用户 {user_id} 创建新账户")

    # 创建充值记录
    recharge_record = CostRechargeRecord(
        cost_user_account_id=user_id,
        amount=amount,  # No longer Decimal(str(amount))
        recharge_method=recharge_method,
        status="成功",
        created_at=int(time.time()),
        updated_at=int(time.time())
    )
    await recharge_record.insert()
    olog.info(f"创建充值记录：{recharge_record.id}")

    # 更新用户余额
    user_account.balance += amount  # No longer Decimal(str(amount))
    user_account.updated_at = int(time.time())
    await user_account.save()

    olog.info(f"用户 {user_id} 充值成功，当前余额：{user_account.balance} 元")

    return {
        "recharge_record_id": str(recharge_record.id),
        "new_balance": user_account.balance  # Already float
    }
