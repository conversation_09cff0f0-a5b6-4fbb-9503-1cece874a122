from decimal import Decimal
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from models.models import CostUserAccount, CostRechargeRecord, CostConsumptionRecord


async def execute_task() -> None:
    """
    扫描CostRechargeRecord与CostConsumptionRecord，计算并更新CostUserAccount中的balance数据
    """
    olog.info("开始执行用户余额更新任务")
    
    # 获取所有需要更新的用户账户
    user_accounts = await CostUserAccount.find_all().to_list()
    
    if not user_accounts:
        olog.info("未找到任何用户账户，跳过余额更新")
        return
        
    updated_count = 0
    
    for account in user_accounts:
        if not account.user_id:
            olog.warning(f"账户 {account.id} 缺少user_id，跳过")
            continue
            
        # 查询该用户所有成功的充值记录
        recharge_records = await CostRechargeRecord.find(
            CostRechargeRecord.cost_user_account_id == account.user_id,
            CostRechargeRecord.status == "成功"
        ).to_list()
        
        # 计算充值总额
        total_recharge = sum(Decimal(str(record.amount or 0)) for record in recharge_records)
        
        # 查询该用户所有消费记录
        consumption_records = await CostConsumptionRecord.find(
            CostConsumptionRecord.cost_user_account_id == account.user_id
        ).to_list()
        
        # 计算消费总额
        total_consumption = sum(Decimal(str(record.amount or 0)) for record in consumption_records)
        
        # 计算余额: 充值总额 - 消费总额
        calculated_balance = total_recharge - total_consumption
        current_balance = Decimal(str(account.balance or 0))
        
        # 如果余额有变化，则更新
        if current_balance != calculated_balance:
            account.balance = float(calculated_balance)
            account.updated_at = int(__import__('time').time())
            await account.save()
            updated_count += 1
            olog.info(f"用户 {account.user_id} 余额已更新: {current_balance} -> {calculated_balance}")
            
    olog.info(f"余额更新任务完成，共更新 {updated_count} 个账户")


@register_scheduler(trigger='cron', hour=1)
class BalanceUpdateScheduler(BaseScheduler):
    async def run_task(self):
        await execute_task()


# 方便测试
if __name__ == "__main__":
    import asyncio
    
    async def main():
        await init_models()
        await execute_task()
    
    asyncio.run(main())