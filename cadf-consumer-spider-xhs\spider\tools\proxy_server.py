from typing import Optional

import aiohttp

from config.config import SpiderConfig
from omni.log.log import olog
from omni.exception.retry_decorator import retry_on_exception


class ProxyServer:
    """代理服务器管理类"""


    @retry_on_exception(max_retries=3)
    async def get_qgdl_proxy_ip(self) -> Optional[str]:
        """获取青果代理服务器地址"""
        async with aiohttp.ClientSession() as session:
            async with session.get(SpiderConfig.QGDL_PROXY_API_URL) as response:
                response.raise_for_status()
                data = await response.json()
                proxy_info = data["data"][0]
                return f"http://{proxy_info['server']}"

    @retry_on_exception(max_retries=3)
    async def get_kdl_proxy_ip(self) -> Optional[str]:
        """快代理提取代理IP"""
        async with aiohttp.ClientSession() as session:
            async with session.get(SpiderConfig.KDL_PROXY_API_URL) as response:
                response.raise_for_status()
                proxy_ip = (await response.text()).strip()
                return proxy_ip if proxy_ip else None

    async def get_proxy_ip(self) -> Optional[str]:
        """根据配置获取当前代理IP"""
        if SpiderConfig.CURRENT_PROXY_TYPE == "qgdl":
            return await self.get_qgdl_proxy_ip()
        elif SpiderConfig.CURRENT_PROXY_TYPE == "kdl":
            return await self.get_kdl_proxy_ip()
        else:
            olog.error(f"不支持的代理类型: {SpiderConfig.CURRENT_PROXY_TYPE}")
            return None

    def get_proxy_credentials(self) -> tuple[str, str]:
        """根据配置获取当前代理的用户名和密码"""
        if SpiderConfig.CURRENT_PROXY_TYPE == "qgdl":
            return SpiderConfig.QGDL_PROXY_USERNAME, SpiderConfig.QGDL_PROXY_PASSWORD
        elif SpiderConfig.CURRENT_PROXY_TYPE == "kdl":
            return SpiderConfig.KDL_PROXY_USERNAME, SpiderConfig.KDL_PROXY_PASSWORD
        else:
            olog.error(f"不支持的代理类型: {SpiderConfig.CURRENT_PROXY_TYPE}")
            return "", ""


