import secrets
import string
import time  # 导入 time 模块
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict

from beanie import PydanticObjectId

from models.models import User, CrewManagement
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse


@register_handler('pub_crew_management')
class PubCrewManagementApi:
    @auth_required(['creator', 'user', 'admin'])
    async def join_fleet(self, data: Dict[str, Any]):
        fleet_code = data.get('fleet_code')
        crew_user_id = data.get('user_id')

        # 输入验证
        if not fleet_code:
            raise MException("请输入舰队邀请码")
        if not crew_user_id:
            raise MException("user_id 不能为空")

        captain = await User.find_one(User.crew_invite_code == fleet_code)
        if not captain:
            raise MException("无效的舰队邀请码")

        captain_user_id = str(captain.id)

        if captain_user_id == crew_user_id:
            raise MException("不能加入自己的舰队")

        existing_membership = await CrewManagement.find_one(CrewManagement.crew_user_id == crew_user_id)
        if existing_membership:
            if existing_membership.captain_user_id == captain_user_id:
                raise MException("您已经是该舰队成员")
            else:
                raise MException("您已加入其他舰队，请先退出")

        new_crew_link = CrewManagement(
            captain_user_id=captain_user_id,
            crew_user_id=crew_user_id,
            create_at=int(time.time())
        )
        await new_crew_link.insert()

        return {'message': '成功加入舰队'}

    @auth_required(['captain', 'admin'])
    async def generate_invite_code(self, data: Dict[str, Any]):
        user_id = data.get('user_id')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        captain = await User.find_one(User.id == PydanticObjectId(user_id))
        if not captain:
            raise MException("舰长用户不存在")

        alphabet = string.ascii_uppercase + string.digits
        random_part = ''.join(secrets.choice(alphabet) for _ in range(6))
        new_code = f"CADF{random_part}"
        captain.crew_invite_code = new_code
        await captain.save()
        return {'invite_code': new_code}


    @auth_required(['captain', 'admin'])
    async def query_crew_stats_with_count(self, data: Dict[str, Any]) -> Dict[str, int]:
        """获取舰员统计数据和总数（合并方法）"""
        captain_user_id = data.get('user_id')
        two_days_ago = int((datetime.now() - timedelta(days=2)).timestamp())

        # 使用单个管道查询获取所有舰员统计信息
        pipeline = [
            # 1. 匹配舰长的舰员记录
            {'$match': {'captain_user_id': captain_user_id}},

            # 2. 获取舰员用户ID列表
            {'$group': {
                '_id': None,
                'crew_user_ids': {'$push': '$crew_user_id'}
            }},

            # 3. 查询账号状态信息
            {'$lookup': {
                'from': 'account',
                'let': {'crew_ids': '$crew_user_ids'},
                'pipeline': [
                    {'$match': {'$expr': {'$in': ['$user_id', '$$crew_ids']}}},
                    {'$project': {'user_id': 1, 'status': 1}}
                ],
                'as': 'accounts'
            }},

            # 4. 查询活跃用户信息
            {'$lookup': {
                'from': 'promotion_task_detail',
                'let': {'crew_ids': '$crew_user_ids'},
                'pipeline': [
                    {'$match': {
                        '$expr': {'$in': ['$user_id', '$$crew_ids']},
                        'validation_status': '成功',
                        'publish_at': {'$gte': two_days_ago}
                    }},
                    {'$group': {'_id': '$user_id'}}
                ],
                'as': 'active_users'
            }},

            # 5. 计算统计信息
            {'$project': {
                'total_crew_count': {'$size': '$crew_user_ids'},
                'online_count': {
                    '$size': {
                        '$filter': {
                            'input': '$accounts',
                            'cond': {'$eq': ['$$this.status', '在线']}
                        }
                    }
                },
                'active_count': {'$size': '$active_users'},
                'crew_user_ids': 1
            }},

            # 6. 添加派生字段
            {'$addFields': {
                'offline_count': {'$subtract': ['$total_crew_count', '$online_count']},
                'inactive_count': {'$subtract': ['$total_crew_count', '$active_count']}
            }},

            # 7. 最终结果格式化
            {'$project': {
                'online_count': 1,
                'offline_count': 1,
                'active_count': 1,
                'inactive_count': 1,
                'crew_count': '$total_crew_count'
            }}
        ]

        result = await CrewManagement.aggregate(pipeline).to_list()

        if not result:
            return {'online_count': 0, 'offline_count': 0, 'active_count': 0, 'inactive_count': 0, 'crew_count': 0}

        return result[0]

    @auth_required(['captain', 'admin'])
    async def query_crew_revenue_ranking(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        captain_user_id = data.get('user_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        # 使用单个管道查询整合多表数据
        pipeline = [
            # 1. 匹配舰长的舰员记录
            {'$match': {'captain_user_id': captain_user_id}},

            # 2. 转换舰员ID为ObjectId
            {'$addFields': {
                'crew_user_oid': {
                    '$cond': {
                        'if': {'$ne': ['$crew_user_id', None]},
                        'then': {'$toObjectId': '$crew_user_id'},
                        'else': None
                    }
                }
            }},
            {'$match': {'crew_user_oid': {'$ne': None}}},

            # 3. 关联用户信息
            {'$lookup': {
                'from': 'user',
                'localField': 'crew_user_oid',
                'foreignField': '_id',
                'as': 'user_info'
            }},
            {'$unwind': '$user_info'},

            # 4. 关联收益记录
            {'$lookup': {
                'from': 'daily_task_revenue',
                'localField': 'crew_user_id',
                'foreignField': 'user_id',
                'as': 'revenue_records'
            }},

            # 5. 计算收益和任务数量
            {'$addFields': {
                'revenue': {'$sum': '$revenue_records.daily_revenue'},
                'task_count': {'$size': {'$ifNull': [{'$setUnion': '$revenue_records.promotion_task_detail_id'}, []]}}
            }},

            # 6. 格式化输出
            {'$project': {
                '_id': 0,
                'id': '$crew_user_id',
                'user_account': '$user_info.username',
                'revenue': 1,
                'task_count': 1
            }},

            # 7. 排序
            {'$sort': {'revenue': -1, 'user_account': 1}},

            # 8. 分页和计数
            {'$facet': {
                'results': [{'$skip': (page - 1) * page_size}, {'$limit': page_size}],
                'metadata': [{'$count': 'total'}]
            }}
        ]

        result = await CrewManagement.aggregate(pipeline).to_list()

        if not result or not result[0]['results']:
            return PageResponse(page=page, page_size=page_size, total=0, results=[])

        crew_data = result[0]['results']
        total = result[0]['metadata'][0]['total'] if result[0]['metadata'] else 0

        return PageResponse(page=page, page_size=page_size, total=total, results=crew_data)



    @auth_required(['captain', 'admin'])
    async def query_crew_list_unified(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """统一的舰员列表查询方法，支持按状态筛选"""
        captain_user_id = data.get('user_id')
        status_filter = data.get('status_filter', 'all')  # all, active, inactive, online, offline
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        two_days_ago = int((datetime.now() - timedelta(days=2)).timestamp())

        # 构建匹配条件
        match_conditions = {'captain_user_id': captain_user_id}

        # 使用单个管道查询整合多表数据
        pipeline = [
            # 1. 匹配舰长的舰员记录
            {'$match': match_conditions},

            # 2. 转换舰员ID为ObjectId
            {'$addFields': {
                'crew_user_oid': {
                    '$cond': {
                        'if': {'$ne': ['$crew_user_id', None]},
                        'then': {'$toObjectId': '$crew_user_id'},
                        'else': None
                    }
                }
            }},
            {'$match': {'crew_user_oid': {'$ne': None}}},

            # 3. 关联用户信息
            {'$lookup': {
                'from': 'user',
                'localField': 'crew_user_oid',
                'foreignField': '_id',
                'as': 'user_info'
            }},
            {'$unwind': '$user_info'},

            # 4. 关联账号信息
            {'$lookup': {
                'from': 'account',
                'localField': 'crew_user_id',
                'foreignField': 'user_id',
                'as': 'account_info'
            }},
            {'$unwind': {'path': '$account_info', 'preserveNullAndEmptyArrays': True}},

            # 5. 关联推广任务详情获取活跃状态
            {'$lookup': {
                'from': 'promotion_task_detail',
                'let': {'crew_user_id': '$crew_user_id'},
                'pipeline': [
                    {'$match': {
                        '$expr': {'$eq': ['$user_id', '$$crew_user_id']},
                        'validation_status': '成功',
                        'publish_at': {'$gte': two_days_ago}
                    }},
                    {'$group': {'_id': '$user_id'}}
                ],
                'as': 'active_tasks'
            }},

            # 6. 添加活跃状态标识
            {'$addFields': {
                'is_active': {'$cond': [{'$gt': [{'$size': '$active_tasks'}, 0]}, True, False]}
            }},
        ]

        # 7. 根据状态过滤
        if status_filter == 'active':
            pipeline.append({'$match': {'is_active': True}})
        elif status_filter == 'inactive':
            pipeline.append({'$match': {'is_active': False}})
        elif status_filter == 'online':
            pipeline.append({'$match': {'account_info.status': '在线'}})
        elif status_filter == 'offline':
            pipeline.append({'$match': {'account_info.status': '离线'}})

        # 8. 格式化输出
        pipeline.extend([
            {'$project': {
                '_id': '$crew_user_id',
                'user_account': '$user_info.username',
                'platform_account': {'$ifNull': ['$account_info.name', '未设置']},
                'login_status': {'$ifNull': ['$account_info.status', '未知']},
                'activity_status': {'$cond': ['$is_active', '活跃', '不活跃']},
                'last_login': {
                    '$cond': {
                        'if': {'$ne': ['$account_info.last_login_check_at', None]},
                        'then': {'$dateToString': {
                            'format': '%Y-%m-%d %H:%M:%S',
                            'date': {'$toDate': {'$multiply': ['$account_info.last_login_check_at', 1000]}}
                        }},
                        'else': None
                    }
                }
            }},

            # 9. 分页和计数
            {'$facet': {
                'results': [{'$skip': (page - 1) * page_size}, {'$limit': page_size}],
                'metadata': [{'$count': 'total'}]
            }}
        ])

        result = await CrewManagement.aggregate(pipeline).to_list()

        if not result or not result[0]['results']:
            return PageResponse(page=page, page_size=page_size, total=0, results=[])

        crew_data = result[0]['results']
        total = result[0]['metadata'][0]['total'] if result[0]['metadata'] else 0

        return PageResponse(page=page, page_size=page_size, total=total, results=crew_data)
