import time
from datetime import datetime
from typing import Dict, Any, Coroutine

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import Account, AccountTrafficMetrics, AiGeneratedMaterial, PromotionTaskDetail, ProductTrafficMetrics
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from spider.xhs.xhs_account_traffic_metrics_spider import crawl_account_metrics


async def find_related_ids(title: str, publish_timestamp: int, account_id: str) -> dict[str, None]:
    """
    根据account_id和publish_time查找相关的ID信息

    Args:
        title: 笔记标题
        publish_timestamp: 发布时间戳
        account_id: 账号ID

    Returns:
        Dict: 包含product_id, ai_generated_material_id, promotion_task_detail_id的字典
    """
    result = {
        "product_id": None,
        "ai_generated_material_id": None,
        "promotion_task_detail_id": None
    }

    time_diff = 24 * 60 * 60  # 24小时的秒数

    # 根据account_id和publish_time筛选PromotionTaskDetail
    promotion_task_details = await PromotionTaskDetail.find(
        PromotionTaskDetail.account_id == account_id,
        PromotionTaskDetail.publish_at >= publish_timestamp - time_diff,
        PromotionTaskDetail.publish_at <= publish_timestamp + time_diff
    ).to_list()

    # 查找匹配的AiGeneratedMaterial并计算时间差
    best_match = None
    min_time_diff = float('inf')

    for task_detail in promotion_task_details:
        # 查找对应的AiGeneratedMaterial，不再使用时间差查询
        ai_material = await AiGeneratedMaterial.find_one(
            AiGeneratedMaterial.id == PydanticObjectId(task_detail.ai_generated_material_id),
            AiGeneratedMaterial.title == title
        )

        # 计算时间差找到最佳匹配
        time_diff_abs = abs(task_detail.publish_at - publish_timestamp)

        if time_diff_abs < min_time_diff:
            min_time_diff = time_diff_abs
            best_match = {
                "promotion_task_detail_id": str(task_detail.id),
                "ai_generated_material_id": str(ai_material.id),
                "product_id": ai_material.product_id
            }

    if best_match:
        result.update(best_match)
    return result


@consume_redis_set(redis_key=RedisKeyConfig.XHS_ACCOUNT_TRAFFIC_METRICS_SPIDER_SET, num_tasks=1)
async def handle_task(message_data: Dict[str, Any]) -> None:
    account_id = message_data.get("id_")
    olog.info(f"开始处理账号笔记指标爬取任务: {account_id}")

    account = await Account.find_one(
        Account.id == PydanticObjectId(account_id), 
        Account.status == "在线",
        Account.platform == "小红书"
    )
    
    notes_data, is_logged_in = await crawl_account_metrics(account.cookie)
    
    # 更新账号登录状态
    current_time = int(time.time())
    new_status = "在线" if is_logged_in else "离线"
    
    account.status = new_status
    account.last_login_check_at = current_time
    await account.save()
    olog.debug(f"账号 {account.name} 状态更新为: {new_status}")
    
    if not is_logged_in:
        olog.warning(f"账号 {account_id} 未登录，已更新状态为离线")
        return
    
    if not notes_data:
        olog.warning(f"账号 {account_id} 指标爬取失败，未获取到任何数据")
        return
    
    crawl_time = int(time.time())

    processed_count = 0
    skipped_count = 0

    for note in notes_data:
        publish_timestamp = (
            int(datetime.fromisoformat(note.publish_date).timestamp())
            if note.publish_date else None
        )

        # 查找相关的ID信息
        related_ids = {}
        if note.title and publish_timestamp:
            related_ids = await find_related_ids(note.title, publish_timestamp, str(account.id))

        # 如果没有找到所有相关ID，说明不是需要的笔记，跳过处理
        if not (related_ids.get("promotion_task_detail_id") and
                related_ids.get("ai_generated_material_id") and
                related_ids.get("product_id")):
            olog.debug(f"跳过笔记 '{note.title}'，未找到完整的相关ID: promotion_task_detail_id={related_ids.get('promotion_task_detail_id')}, ai_generated_material_id={related_ids.get('ai_generated_material_id')}, product_id={related_ids.get('product_id')}")
            skipped_count += 1
            continue

        # 构建查询条件（包含所有ID）
        query_conditions = {
            "account_id": str(account.id),
            "platform": account.platform,
            "title": note.title,
            "product_id": related_ids.get("product_id"),
            "ai_generated_material_id": related_ids.get("ai_generated_material_id"),
            "promotion_task_detail_id": related_ids.get("promotion_task_detail_id")
        }

        # 查询是否存在
        existing_record = await AccountTrafficMetrics.find_one(query_conditions)

        if existing_record:
            # 更新已存在的记录
            await existing_record.update({
                "$set": {
                    "crawled_at": crawl_time,
                    "publish_time": publish_timestamp,
                    "view_count": note.views,
                    "like_count": note.likes,
                    "comment_count": note.comments,
                    "share_count": note.shares,
                    "favorite_count": note.favorites
                }
            })
        else:
            # 新增记录
            await AccountTrafficMetrics(
                account_id=str(account.id),
                product_id=related_ids.get("product_id"),
                ai_generated_material_id=related_ids.get("ai_generated_material_id"),
                promotion_task_detail_id=related_ids.get("promotion_task_detail_id"),
                platform=account.platform,
                crawled_at=crawl_time,
                title=note.title,
                publish_time=publish_timestamp,
                view_count=note.views,
                like_count=note.likes,
                comment_count=note.comments,
                share_count=note.shares,
                favorite_count=note.favorites
            ).insert()

        processed_count += 1

    olog.info(f"成功为账号 {account_id} ({account.name}) 处理了 {processed_count} 条指标数据，跳过了 {skipped_count} 条")
