import time
from typing import Any, Dict, List

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import AiGenerationTask, Product, AiGeneratedMaterial, UserBasicMaterial
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set
from omni.redis.redis_client import rc
from omni.integration.oss.tencent_oss import oss_client


@register_handler('adv_ai_generation_task_api')
class AdvAiGenerationTaskApi:
    @auth_required(["admin", "advertiser"])
    async def create(self, data: Dict[str, Any]) -> None:
        user_id = data.get("user_id")
        product_id = data.get("product_id")
        material_ids = data.get("material_ids", [])
        target_quantity = data.get("target_quantity")

        if not user_id:
            raise MException("user_id 不能为空")
        if not product_id:
            raise MException("product_id 不能为空")
        if target_quantity is None:
            raise MException("target_quantity 不能为空")

        olog.info(f"开始创建AI生成任务, 用户ID: {user_id}, 产品ID: {product_id}")

        # 获取产品信息用于生成友好的任务名称
        product = await Product.find_one(
            Product.id == PydanticObjectId(product_id),
            Product.is_deleted == False
        )
        if not product:
            raise MException("产品不存在")
        
        # 生成用户友好的中文任务名称
        current_time = int(time.time())
        
        product_name = product.title or "未命名产品"
        # 截取产品名称，避免过长
        if len(product_name) > 15:
            product_name = product_name[:15] + "..."
            
        task_name = f"「{product_name}」AI素材生成"
        
        task_data = {
            "user_id": user_id,
            "product_id": product_id,
            "task_name": task_name,
            "create_at": current_time,
            "is_deleted": False
        }
        
        new_task = AiGenerationTask(**task_data)
        await new_task.insert()
        task_id = str(new_task.id)
        olog.info(f"任务保存成功, 任务ID: {task_id}")

        # 创建素材记录
        if not material_ids:
            raise MException("material_ids 必须是一个非空列表")

        materials_to_insert = []
        num_materials = len(material_ids)
        
        for i in range(target_quantity):
            assigned_material_id = material_ids[i % num_materials]
            material_data = {
                "user_id": user_id,
                "ai_generation_task_id": task_id,
                "product_id": product_id,
                "ai_basic_material_id": assigned_material_id,
                "image_generation_status": "待生成",
                "text_generation_status": "待生成",
                "is_deleted": False
            }
            materials_to_insert.append(AiGeneratedMaterial(**material_data))

        # 保存并推送到Redis
        if materials_to_insert:
            inserted_results = await AiGeneratedMaterial.insert_many(materials_to_insert)
            inserted_ids = [str(obj_id) for obj_id in inserted_results.inserted_ids]
            olog.info(f"成功批量保存 {len(inserted_ids)} 个 AiGeneratedMaterial")

            redis_image_set_key = RedisKeyConfig.XHS_IMAGE_GEN_SET
            redis_text_set_key = RedisKeyConfig.XHS_TEXT_GEN_SET

            image_result = await publish_messages_to_redis_set(
                redis_image_set_key, [{"id_": material_id} for material_id in inserted_ids]
            )
            text_result = await publish_messages_to_redis_set(
                redis_text_set_key, [{"id_": material_id} for material_id in inserted_ids]
            )

            if image_result is not None:
                olog.info(f"成功向 Redis Set '{redis_image_set_key}' 添加了 {image_result} 个新任务 ID")
            if text_result is not None:
                olog.info(f"成功向 Redis Set '{redis_text_set_key}' 添加了 {text_result} 个新任务 ID")

        olog.info(f"AI生成任务创建成功, 任务ID: {task_id}")

    @auth_required(["admin", "advertiser"])
    async def delete(self, data: Dict[str, Any]) -> None:
        id_ = data.get("_id")
        user_id = data.get("user_id")

        if not id_:
            raise MException("_id 不能为空")
        if not user_id:
            raise MException("user_id 不能为空")

        task = await AiGenerationTask.find_one(
            AiGenerationTask.id == PydanticObjectId(id_),
            AiGenerationTask.user_id == user_id,
            AiGenerationTask.is_deleted == False
        )
        if not task:
            raise MException("任务不存在或无权删除")

        task_id_str = str(task.id)

        # 查找关联的材料并从Redis中移除
        related_materials = await AiGeneratedMaterial.find(AiGeneratedMaterial.ai_generation_task_id == task_id_str).to_list()
        material_ids_to_remove = [str(mat.id) for mat in related_materials]

        if material_ids_to_remove:
            redis_client = rc
            redis_image_set_key = RedisKeyConfig.XHS_IMAGE_GEN_SET
            redis_text_set_key = RedisKeyConfig.XHS_TEXT_GEN_SET

            with redis_client.pipeline() as pipe:
                pipe.srem(redis_image_set_key, *material_ids_to_remove)
                pipe.srem(redis_text_set_key, *material_ids_to_remove)
                results = pipe.execute()
            
            olog.info(f"从 Redis Sets 中移除了 {results[0]} 和 {results[1]} 个 ID")

        await task.set({AiGenerationTask.is_deleted: True})
        olog.info(f"成功软删除任务记录, 任务ID: {id_}")

    @auth_required(["admin", "advertiser"])
    async def query_one(self, data: Dict[str, Any]) -> Dict[str, Any]:
        id_ = data.get("_id")
        user_id = data.get("user_id")

        task = await AiGenerationTask.find_one(
            AiGenerationTask.id == PydanticObjectId(id_),
            AiGenerationTask.user_id == user_id,
            AiGenerationTask.is_deleted == False
        )
        if not task:
            raise MException("任务不存在或无权查看")
        return task.to_dict()

    @auth_required(["admin", "advertiser"])
    async def query_all(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        user_id = data.get("user_id")
        page = data.get("page", 1)
        page_size = data.get("page_size", 10)
        task_name = data.get("task_name")

        # 构建聚合管道查询条件
        match_stage = {"$match": {"user_id": user_id, "is_deleted": False}}
        if task_name:
            match_stage["$match"]["task_name"] = {"$regex": task_name, "$options": "i"}

        # MongoDB聚合管道：一次性完成查询、关联、统计、分页
        aggregation_pipeline = [
            # 1. 基础过滤
            match_stage,
            
            # 2. 按创建时间排序
            {"$sort": {"create_at": -1}},
            
            # 3. 使用facet同时计算总数和分页结果
            {
                "$facet": {
                    "total": [{"$count": "count"}],
                    "data": [
                        {"$skip": (page - 1) * page_size},
                        {"$limit": page_size},
                        # 4. 左连接AiGeneratedMaterial集合
                        {
                            "$lookup": {
                                "from": "ai_generated_material",
                                "let": {"task_id": {"$toString": "$_id"}},
                                "pipeline": [
                                    {
                                        "$match": {
                                            "$expr": {"$eq": ["$ai_generation_task_id", "$$task_id"]},
                                            "is_deleted": False
                                        }
                                    }
                                ],
                                "as": "materials"
                            }
                        },
                        # 4.1. 左连接Product集合获取产品信息
                        {
                            "$lookup": {
                                "from": "product",
                                "let": {"product_id": {"$toObjectId": "$product_id"}},
                                "pipeline": [
                                    {
                                        "$match": {
                                            "$expr": {"$eq": ["$_id", "$$product_id"]},
                                            "is_deleted": False
                                        }
                                    }
                                ],
                                "as": "product_info"
                            }
                        },
                        # 5. 统计材料完成情况并添加产品信息
                        {
                            "$addFields": {
                                "total_materials": {"$size": "$materials"},
                                "image_completed_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.image_generation_status", "已完成"]}
                                        }
                                    }
                                },
                                "text_completed_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.text_generation_status", "已完成"]}
                                        }
                                    }
                                },
                                # 添加详细的状态统计
                                "image_processing_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.image_generation_status", "生成中"]}
                                        }
                                    }
                                },
                                "text_processing_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.text_generation_status", "生成中"]}
                                        }
                                    }
                                },
                                "image_failed_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.image_generation_status", "失败"]}
                                        }
                                    }
                                },
                                "text_failed_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.text_generation_status", "失败"]}
                                        }
                                    }
                                },
                                # 添加分离的待生成状态统计
                                "image_pending_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.image_generation_status", "待生成"]}
                                        }
                                    }
                                },
                                "text_pending_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {"$eq": ["$$this.text_generation_status", "待生成"]}
                                        }
                                    }
                                },
                                # 添加产品信息
                                "product": {
                                    "$cond": {
                                        "if": {"$gt": [{"$size": "$product_info"}, 0]},
                                        "then": {"$arrayElemAt": ["$product_info", 0]},
                                        "else": None
                                    }
                                }
                            }
                        },
                        # 5.1. 计算各种状态的素材数量
                        {
                            "$addFields": {
                                # 已完成：图片和文本都是"已完成"状态的素材数量
                                "completed_materials_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {
                                                "$and": [
                                                    {"$eq": ["$$this.image_generation_status", "已完成"]},
                                                    {"$eq": ["$$this.text_generation_status", "已完成"]}
                                                ]
                                            }
                                        }
                                    }
                                },
                                # 生成中：图片或文本有任何一个是"生成中"状态，或者一个完成一个待生成的素材数量
                                "processing_materials_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {
                                                "$or": [
                                                    # 图片生成中
                                                    {"$eq": ["$$this.image_generation_status", "生成中"]},
                                                    # 文本生成中
                                                    {"$eq": ["$$this.text_generation_status", "生成中"]},
                                                    # 图片完成，文本待生成
                                                    {
                                                        "$and": [
                                                            {"$eq": ["$$this.image_generation_status", "已完成"]},
                                                            {"$eq": ["$$this.text_generation_status", "待生成"]}
                                                        ]
                                                    },
                                                    # 文本完成，图片待生成
                                                    {
                                                        "$and": [
                                                            {"$eq": ["$$this.image_generation_status", "待生成"]},
                                                            {"$eq": ["$$this.text_generation_status", "已完成"]}
                                                        ]
                                                    },
                                                    # 图片完成，文本失败
                                                    {
                                                        "$and": [
                                                            {"$eq": ["$$this.image_generation_status", "已完成"]},
                                                            {"$eq": ["$$this.text_generation_status", "失败"]}
                                                        ]
                                                    },
                                                    # 文本完成，图片失败
                                                    {
                                                        "$and": [
                                                            {"$eq": ["$$this.image_generation_status", "失败"]},
                                                            {"$eq": ["$$this.text_generation_status", "已完成"]}
                                                        ]
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                },
                                # 待生成：图片和文本都是"待生成"状态的素材数量
                                "pending_materials_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {
                                                "$and": [
                                                    {"$eq": ["$$this.image_generation_status", "待生成"]},
                                                    {"$eq": ["$$this.text_generation_status", "待生成"]}
                                                ]
                                            }
                                        }
                                    }
                                },
                                # 失败：图片和文本都是"失败"状态的素材数量
                                "failed_materials_count": {
                                    "$size": {
                                        "$filter": {
                                            "input": "$materials",
                                            "cond": {
                                                "$and": [
                                                    {"$eq": ["$$this.image_generation_status", "失败"]},
                                                    {"$eq": ["$$this.text_generation_status", "失败"]}
                                                ]
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        # 6. 计算任务整体状态
                        {
                            "$addFields": {
                                "status": {
                                    "$switch": {
                                        "branches": [
                                            {
                                                "case": {"$eq": ["$total_materials", 0]},
                                                "then": "pending"
                                            },
                                            {
                                                "case": {"$eq": ["$completed_materials_count", "$total_materials"]},
                                                "then": "completed"
                                            },
                                            {
                                                "case": {"$gt": ["$failed_materials_count", 0]},
                                                "then": "failed"
                                            },
                                            {
                                                "case": {"$gt": ["$processing_materials_count", 0]},
                                                "then": "processing"
                                            }
                                        ],
                                        "default": "pending"
                                    }
                                }
                            }
                        },
                        # 7. 移除不需要的字段
                        {
                            "$project": {
                                "materials": 0,
                                "product_info": 0
                            }
                        }
                    ]
                }
            }
        ]

        # 执行聚合查询
        result = await AiGenerationTask.aggregate(aggregation_pipeline).to_list()
        
        # 解析结果
        total_count = result[0]["total"][0]["count"] if result[0]["total"] else 0
        tasks_data = result[0]["data"]
        
        # 转换 ObjectId 为字符串并处理产品图片
        for task in tasks_data:
            if "_id" in task:
                task["_id"] = str(task["_id"])
            
            # 提取产品第一张图片URL到任务数据中
            task["image_url"] = None
            if "product" in task and task["product"]:
                product = task["product"]
                
                # 获取第一张图片的URL
                if "images" in product and product["images"] and len(product["images"]) > 0:
                    first_image = product["images"][0]
                    if "oss_key" in first_image:
                        try:
                            task["image_url"] = await oss_client.signed_get_url(first_image["oss_key"])
                        except Exception as e:
                            olog.warning(f"生成图片签名URL失败: {e}")
                            task["image_url"] = None
                
                # 移除product字段
                del task["product"]

        return PageResponse(page=page, page_size=page_size, total=total_count, results=tasks_data)
