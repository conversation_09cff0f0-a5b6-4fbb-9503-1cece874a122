"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
    Alert, Box, Button, CircularProgress, Container, Grid,
    LinearProgress, Pagination, Paper, Stack, Typography, useMediaQuery, useTheme, IconButton
} from '@mui/material';
import React from 'react';
import NextLink from 'next/link';
import { Clock, AlertTriangle, ChevronLeft, ListChecks, Eye, CheckCircle, XCircle, Link as LinkIconLucide, RefreshCw } from 'lucide-react';
import { pubPromotionTaskApi } from '@/api/pub-promotion-task-api';
import { useSnackbar } from 'notistack';

// CSS animation for refresh icon
const refreshIconStyles = `
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
`;

// Add styles to document head
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = refreshIconStyles;
    document.head.appendChild(styleElement);
}

// 获取任务状态显示属性
function getTaskStatusProps(task) {
    let label = '待提交链接';
    let color = 'info';
    let variant = 'outlined';
    let icon = <LinkIconLucide size={16} />;

    if (task.publish_url && task.publish_at) {
        switch (task.validation_status) {
            case '成功':
                label = '已发布 (验证成功)';
                color = 'success';
                variant = 'filled';
                icon = <CheckCircle size={16} />;
                break;
            case '失败':
                label = '验证失败';
                color = 'error';
                icon = <XCircle size={16} />;
                break;
            case '待验证':
            default:
                label = '发布链接待验证';
                color = 'warning';
                icon = <AlertTriangle size={16} />;
                break;
        }
    }
    return { label, color, variant, icon };
}

// 处理任务数据
function processTaskData(tasks) {
    return tasks.map(task => ({
        ...task,
        imageUrls: task.image_url ? [task.image_url] : ['https://placehold.co/600x400?text=No+Image'],
    }));
}

function AcceptedTaskListItemCard({ task, onClick, isMobile }) {
    const theme = useTheme();

    const statusProps = getTaskStatusProps(task);
    const imageToShow = task.imageUrls && task.imageUrls.length > 0 ? task.imageUrls[0] : 'https://placehold.co/400x400/e0f2fe/0c4a6e?text=Product';

    return (
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }} key={task._id}>
            <Paper 
                elevation={2} 
                sx={{ 
                    height: '100%', 
                    borderRadius: 2, 
                    display: 'flex', 
                    flexDirection: 'column',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                        elevation: 4,
                        transform: 'translateY(-2px)'
                    }
                }}
                onClick={onClick}
            >
                <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    {/* 图片与标题 */}
                    <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 1.5, 
                        mb: 2,
                        pb: 1.5,
                        borderBottom: `1px solid ${theme.palette.divider}`
                    }}>
                        <Box
                            component="img"
                            src={imageToShow}
                            alt="Task"
                            sx={{
                                width: isMobile ? 40 : 48,
                                height: isMobile ? 40 : 48,
                                borderRadius: '50%',
                                objectFit: 'cover',
                                border: `2px solid ${theme.palette.grey[300]}`,
                                flexShrink: 0
                            }}
                        />
                        <Typography 
                            variant={isMobile ? "body2" : "body1"} 
                            sx={{ 
                                fontWeight: 'medium', 
                                flexGrow: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                color: 'text.secondary'
                            }} 
                            title={task.title}
                        >
                            {task.title || '无标题'}
                        </Typography>
                    </Box>

                    {/* 状态区域 */}
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: 2,
                        border: `2px solid ${theme.palette[statusProps.color]?.main || theme.palette.grey[400]}`,
                        overflow: 'hidden',
                        width: '100%',
                        height: isMobile ? 36 : 40,
                        mb: 2
                    }}>
                        <Box sx={{
                            bgcolor: theme.palette[statusProps.color]?.main || theme.palette.grey[400],
                            color: theme.palette[statusProps.color]?.contrastText || theme.palette.common.white,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: isMobile ? 36 : 40,
                            height: '100%',
                            flexShrink: 0
                        }}>
                            {React.cloneElement(statusProps.icon, { size: isMobile ? 16 : 18 })}
                        </Box>
                        <Box sx={{ 
                            px: 1.5, 
                            py: 0.5,
                            color: theme.palette[statusProps.color]?.main || theme.palette.text.primary,
                            fontWeight: 'bold',
                            fontSize: isMobile ? '0.875rem' : '0.9375rem',
                            flexGrow: 1
                        }}>
                            {statusProps.label}
                        </Box>
                    </Box>

                    {/* 时间信息 */}
                    <Box sx={{ mb: 2 }}>
                        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 0.5 }}>
                            <Clock size={14} color={theme.palette.text.secondary} />
                            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                接取时间: {new Date(task.accepted_at * 1000).toLocaleString()}
                            </Typography>
                        </Stack>
                        {task.publish_at && (
                            <Stack direction="row" spacing={1} alignItems="center">
                                <Clock size={14} color={theme.palette.text.secondary} />
                                <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                    发布时间: {new Date(task.publish_at * 1000).toLocaleString()}
                                </Typography>
                            </Stack>
                        )}
                    </Box>

                    {/* 异常信息 */}
                    {task.validation_status === '失败' && task.validation_details && (
                        <Box sx={{
                            bgcolor: theme.palette.error.light + '20',
                            border: `1px solid ${theme.palette.error.light}`,
                            borderRadius: 1,
                            p: 1.5,
                            mb: 2
                        }}>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                <AlertTriangle size={16} color={theme.palette.error.main} />
                                <Typography variant="caption" color="error" sx={{
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold'
                                }}>
                                    验证失败原因：
                                </Typography>
                            </Stack>
                            <Typography variant="caption" color="error" sx={{
                                fontSize: '0.75rem',
                                display: 'block',
                                wordBreak: 'break-word',
                                ml: 3
                            }}>
                                {task.validation_details}
                            </Typography>
                        </Box>
                    )}

                    {/* 链接信息 */}
                    {task.validation_status === '成功' && task.publish_url && (
                        <Box sx={{
                            bgcolor: theme.palette.success.light + '20',
                            border: `1px solid ${theme.palette.success.light}`,
                            borderRadius: 1,
                            p: 1.5,
                            mb: 2
                        }}>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 0.5 }}>
                                <LinkIconLucide size={14} color={theme.palette.success.main} />
                                <Typography variant="caption" color="success.main" sx={{
                                    fontSize: '0.75rem',
                                    fontWeight: 'medium'
                                }}>
                                    发布链接：
                                </Typography>
                            </Stack>
                            <Typography variant="caption" color="success.main" sx={{
                                fontSize: '0.75rem',
                                display: 'block',
                                wordBreak: 'break-all',
                                ml: 2.5
                            }}>
                                {task.publish_url}
                            </Typography>
                        </Box>
                    )}

                    {task.validation_status === '待验证' && task.publish_url && (
                        <Box sx={{
                            bgcolor: theme.palette.warning.light + '20',
                            border: `1px solid ${theme.palette.warning.light}`,
                            borderRadius: 1,
                            p: 1.5,
                            mb: 2
                        }}>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 0.5 }}>
                                <LinkIconLucide size={14} color={theme.palette.warning.main} />
                                <Typography variant="caption" color="warning.main" sx={{
                                    fontSize: '0.75rem',
                                    fontWeight: 'medium'
                                }}>
                                    待验证链接：
                                </Typography>
                            </Stack>
                            <Typography variant="caption" color="warning.main" sx={{
                                fontSize: '0.75rem',
                                display: 'block',
                                wordBreak: 'break-all',
                                ml: 2.5
                            }}>
                                {task.publish_url}
                            </Typography>
                        </Box>
                    )}

                    {/* 操作按钮 */}
                    <Box sx={{ mt: 'auto', pt: 1 }}>
                        <Button 
                            variant="contained" 
                            fullWidth 
                            onClick={onClick} 
                            sx={{ 
                                borderRadius: 1.5, 
                                textTransform: 'none',
                                fontWeight: 'medium'
                            }} 
                            startIcon={<Eye size={16}/>}
                        >
                            查看任务详情
                        </Button>
                    </Box>
                </Box>
            </Paper>
        </Grid>
    );
}

function AcceptedTasksContent() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const router = useRouter();
    const params = useParams();
    const { enqueueSnackbar } = useSnackbar();

    const accountId = params.accountId;

    const [tasksState, setTasksState] = useState({ tasks: [], totalCount: 0, loading: true, error: null });
    const [currentPage, setCurrentPage] = useState(0);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const rowsPerPage = isMobile ? 12 : 5; // PC: 5, Mobile: 12

    useEffect(() => {
        if (!accountId) {
            setTasksState({ tasks: [], totalCount: 0, loading: false, error: "未提供账户ID，无法查询已接任务。" });
            enqueueSnackbar("缺少账户ID", { variant: 'error' });
            return;
        }
        const fetchAccepted = async () => {
            setTasksState(prev => ({ ...prev, loading: true, error: null }));
            try {
                const response = await pubPromotionTaskApi.queryAcceptedTasks(currentPage + 1, rowsPerPage, accountId);
                if (response?.results) {
                    const processedTasks = processTaskData(response.results);
                    setTasksState({ tasks: processedTasks, totalCount: response.total || 0, loading: false, error: null });
                } else {
                    setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的已接任务数据' });
                }
            } catch (error) {
                console.error(`获取账户 ${accountId} 的已接任务列表失败:`, error);
                const errorMsg = `获取已接任务列表失败: ${error.message || '未知错误'}`;
                setTasksState({ tasks: [], totalCount: 0, loading: false, error: errorMsg });
                enqueueSnackbar(errorMsg, { variant: 'error' });
            }
        };
        fetchAccepted();
    }, [accountId, currentPage, rowsPerPage]);

    const handlePageChange = (event, newPage) => setCurrentPage(newPage - 1);

    const handleTaskClick = (task) => {
        router.push(`/protected/tasks/detail/${task._id}`);
    };

    const handleRefresh = async () => {
        if (!accountId) return;
        
        setIsRefreshing(true);
        try {
            const response = await pubPromotionTaskApi.queryAcceptedTasks(currentPage + 1, rowsPerPage, accountId);
            if (response?.results) {
                const processedTasks = processTaskData(response.results);
                setTasksState({ tasks: processedTasks, totalCount: response.total || 0, loading: false, error: null });
                enqueueSnackbar('刷新成功', { variant: 'success' });
            } else {
                setTasksState({ tasks: [], totalCount: 0, loading: false, error: '未能获取有效的已接任务数据' });
            }
        } catch (error) {
            console.error(`刷新账户 ${accountId} 的已接任务列表失败:`, error);
            const errorMsg = `刷新失败: ${error.message || '未知错误'}`;
            setTasksState(prev => ({ ...prev, loading: false, error: errorMsg }));
            enqueueSnackbar(errorMsg, { variant: 'error' });
        } finally {
            setIsRefreshing(false);
        }
    };

    const totalPages = Math.ceil(tasksState.totalCount / rowsPerPage);
    const accountDisplayName = accountId ? `账户 ${accountId.slice(-6)}` : '选择的账户';

    return (
        <Container maxWidth="xl" sx={{ py: { xs: 2, sm: 3, md: 4 }, px: { xs: 2, sm: 3, md: 4 } }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <IconButton onClick={() => router.back()}>
                    <ChevronLeft />
                </IconButton>
                <Typography 
                    variant="caption" 
                    color="text.secondary" 
                    onClick={() => router.back()} 
                    sx={{ cursor: 'pointer', '&:hover': { textDecoration: 'underline' } }}
                >
                    返回任务中心
                </Typography>
            </Stack>
            
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: { xs: 2, sm: 3, md: 4 } }}>
                <Box>
                    <Typography variant={isMobile ? "h5" : "h4"} component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
                        已接任务
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                        查看和管理您已接取的图文推广任务。
                    </Typography>
                </Box>
                    <IconButton 
                        onClick={handleRefresh}
                        disabled={isRefreshing || tasksState.loading}
                        sx={{ 
                            bgcolor: 'primary.main',
                            color: 'white',
                            '&:hover': { bgcolor: 'primary.dark' },
                            '&:disabled': { bgcolor: 'action.disabledBackground' }
                        }}
                    >
                        <RefreshCw 
                            size={20} 
                            style={{ 
                                animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                                transformOrigin: 'center'
                            }} 
                        />
                    </IconButton>
            </Stack>

            {tasksState.loading && <LinearProgress sx={{ mb: 2 }} />}
            {tasksState.error && <Alert severity="error" sx={{ mb: 2 }}>{tasksState.error}</Alert>}
            
            {!tasksState.loading && !tasksState.error && tasksState.tasks.length === 0 && (
                <Paper elevation={0} sx={{p:3, textAlign: 'center', backgroundColor: 'background.default'}}>
                    <ListChecks size={48} style={{ margin: '0 auto 16px', color: theme.palette.text.secondary }} />
                    <Typography variant="h6" gutterBottom>
                        {accountDisplayName} 暂无已接任务
                    </Typography>
                    <Typography color="text.secondary">
                        您选择的账户当前没有已接取的任务，可以前往任务市场看看。
                    </Typography>
                     <Button 
                        component={NextLink} 
                        href={`/protected/tasks/market?accountId=${accountId || ''}`} 
                        variant="outlined" 
                        sx={{ mt: 2 }}
                    >
                        前往任务市场
                    </Button>
                </Paper>
            )}

            {!tasksState.loading && !tasksState.error && tasksState.tasks.length > 0 && (
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                    {tasksState.tasks.map((task) => (
                        <AcceptedTaskListItemCard key={task._id} task={task} onClick={() => handleTaskClick(task)} isMobile={isMobile} />
                    ))}
                </Grid>
            )}

            {totalPages > 1 && !tasksState.loading && !tasksState.error && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                    <Pagination count={totalPages} page={currentPage + 1} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
                </Box>
            )}
        </Container>
    );
}

export default function AcceptedTasksPage() {
    return (
        <Suspense fallback={
            <Container maxWidth="xl" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
                <CircularProgress />
            </Container>
        }>
            <AcceptedTasksContent />
        </Suspense>
    );
}
 